<div id='top'/>

# PATIO<br/>

---

# Serenity BDD Test Automation Project

---

<!--TABLE OF CONTENTS-->

# Table of Contents
### 1. [Requirements](#requirements)
### 2. [Code location](#code-location)
### 3. [Configurations](#configurations)
* [JAVA](#setup-java)
* [Maven](#setup-maven)
* [IntelliJ IDE](#intellij-ide)
* [serenity.conf](#serenity-conf)
* [Database](#database)
### 4. [Running tests](#running-tests)
* [RunnerTestSuite class](#runner-class)
* [Command line](#command-line)
* [GitHub Actions](#github-actions)
* [Parallel run](#parallel-run)
### 5. [Project dependencies](#project-dependencies)
### 6. [Project structure](#project-structure)
### 7. [Usage](#usage)
### 8. [Available Environments & Tags](#environments-tags)
### 9. [Report](#report)
### 10. [Internal conventions](#internal-conventions)
### 11. [Tips](#tips)
### 12. [Additional Documentations](#additional-documentations)

---

<!--REQUIREMENTS-->

<div id='requirements'/>

## 1.Requirements

- [IntelliJ IDEA](https://www.jetbrains.com/idea/)
- [Java Development Kit 21 (JDK21)](https://www.oracle.com/java/technologies/javase/jdk11-archive-downloads.html)
- [Maven 3](https://maven.apache.org/download.cgi#)

---

<!--CODE LOCATION-->

<div id='code-location'/>

## 2. Code location

Azure Devops Repos:

```
https://github.com/Dajar-Internet/patio_ui_automation_test
```
---

<!--CONFIGURATIONS-->


<!--CONFIGURATIONS-->

<div id='configurations'/>

## 3. Configurations

<div id='setup-java'/>

### - Java
[Setup Java](https://phoenixnap.com/kb/install-java-ubuntu) – setup on **Linux/Ubuntu**

#### Install Java (example for OpenJDK 21)
```bash
sudo apt update
sudo apt install openjdk-21-jdk
```

#### Set environment variables
Add to your `~/.bashrc` or `~/.zshrc`:
```bash
export JAVA_HOME=/usr/lib/jvm/java-21-openjdk-amd64
export PATH=$JAVA_HOME/bin:$PATH
```
Then run:
```bash
source ~/.bashrc
```
Verify:
```bash
java -version
```

<div id='setup-maven'/>

### - Maven
[Setup Maven](https://phoenixnap.com/kb/install-maven-ubuntu) – setup on **Linux/Ubuntu**

#### Install Maven
```bash
sudo apt update
sudo apt install maven
```

#### Set environment variables (optional if installed via `apt`)
If using manual installation (e.g. from Apache):
```bash
export MAVEN_HOME=/opt/maven
export PATH=$MAVEN_HOME/bin:$PATH
```
Then run:
```bash
source ~/.bashrc
```
Verify:
```bash
mvn -version
```

<div id='intellij-ide'/>

### - IntelliJ IDE

#### Install IntelliJ IDEA Community
You can download directly from JetBrains: [Download IntelliJ IDEA Community](https://www.jetbrains.com/idea/download/#section=linux)

Or install via Snap:
```bash
sudo snap install intellij-idea-community --classic
```

#### -- How to check if dependencies failed to download:
1. In IntelliJ, on the right side, open the **Maven** tab and find **Dependencies**. If all or some are underlined in red, your code is not running.
2. Or, if you open any class and many lines require imports but IntelliJ cannot resolve them automatically, your code is not running.
3. However, if you can run the code even though dependencies are underlined in red, it could be an IntelliJ cache issue.

#### -- How to fix dependency issues:
1. Open **File → Invalidate Caches...**, check:
    - `Clear file system cache and Local History`
    - `Clear VCS Log caches and indexes`

   Click **Invalidate and Restart**. Wait for all dependencies to download (ensure VPN is OFF).

2. Delete your local Maven repository:
```bash
rm -rf ~/.m2/repository
```
Then reopen IntelliJ — it will automatically start downloading dependencies. Wait until it's completed

#### -- Setup IntelliJ first time:
1. Open **File → Project Structure** → check the **Project**, **Modules**, and **SDKs** tabs — ensure Java 21 is selected everywhere.
2. Open **File → Settings → Plugins**: install **Cucumber for Java** (plugin improves feature file syntax highlighting and support).


<div id='serenity-conf'/>

### - serenity.conf
[Serenity conf file documentation - 1](https://github.com/serenity-bdd/the-serenity-book/blob/master/modules/ROOT/pages/web-testing-in-serenity.adoc)

[Serenity conf file documentation - 2](https://github.com/serenity-bdd/the-serenity-book/blob/master/modules/ROOT/pages/serenity-system-properties.adoc)

<!--RUNNING TESTS-->

<div id='running-tests'/>

## 4. Running tests

<div id='runner-class'/>

### - RunnerTestSuite class

Add tag you want to run to 'tags' and execute RunnerTestSuite class by selecting 'Run RunnerTestSuite'

<div id='command-line'/>

### - Command line

#### **✨ Simplified Tag Format (Recommended)**

The framework now supports a simplified tag format using `-Dgroups` parameter without the "@" symbol:

```bash
# Execute all tests from RunnerTestSuite.class
mvn clean verify

# Execute single numeric tag
mvn clean verify -Dgroups="0001"

# Execute multiple numeric tags
mvn clean verify -Dgroups="0001,0002,0003"

# Execute text tags
mvn clean verify -Dgroups="cart,search"

# Execute mixed tags
mvn clean verify -Dgroups="0001,cart,smoke"
```

#### **🌐 Multi-Browser Support**

The framework supports Chrome, Firefox, and Edge browsers:

```bash
# Chrome (default)
mvn clean verify -Dgroups="0001" -Dwebdriver.driver=chrome

# Firefox
mvn clean verify -Dgroups="0001" -Dwebdriver.driver=firefox -Dserenity.environment=firefox

# Edge
mvn clean verify -Dgroups="0001" -Dwebdriver.driver=edge -Dserenity.environment=edge
```

#### **🏢 Environment Configuration**

Override the environment specified in the configuration file:

```bash
# Different environments
mvn clean verify -Dgroups="0001" -Denvironment=DEV-GDANSK
mvn clean verify -Dgroups="0001" -Denvironment=DEV-KRAKOW
mvn clean verify -Dgroups="0001" -Denvironment=STAGING
mvn clean verify -Dgroups="0001" -Denvironment=PROD
```

#### **🔧 Advanced Configuration**

```bash
# Parallel execution with custom thread count
mvn clean verify -Dgroups="cart" -Dcucumber.execution.parallel.config.fixed.parallelism=6

# Custom screenshots setting
mvn clean verify -Dgroups="0001" -Dserenity.take.screenshots=FOR_FAILURES

# Disable WebDriver autodownload
mvn clean verify -Dgroups="0001" -Dwebdriver.autodownload=false

# Custom timeouts
mvn clean verify -Dgroups="0001" -Dwebdriver.timeouts.implicitlywait=15000
```

#### **📋 Complete Example Commands**

```bash
# Single test with Chrome on DEV environment
mvn clean verify -Dgroups="0001" -Denvironment=DEV-GDANSK -Dwebdriver.driver=chrome

# Multiple tests with Firefox on STAGING
mvn clean verify -Dgroups="0001,0002,0003" -Denvironment=STAGING -Dwebdriver.driver=firefox -Dserenity.environment=firefox

# Cart tests with Edge, parallel execution
mvn clean verify -Dgroups="cart" -Denvironment=DEV-GDANSK -Dwebdriver.driver=edge -Dserenity.environment=edge -Dcucumber.execution.parallel.config.fixed.parallelism=2

# Smoke tests with custom settings
mvn clean verify -Dgroups="smoke" -Denvironment=STAGING -Dwebdriver.driver=chrome -Dserenity.take.screenshots=FOR_FAILURES -Dcucumber.execution.parallel.config.fixed.parallelism=3
```

#### **🔄 Legacy Format (Still Supported)**

For advanced tag expressions, you can still use the traditional Cucumber format:

```bash
# Advanced tag expressions
mvn clean verify -Dcucumber.filter.tags="@0001 and @smoke"
mvn clean verify -Dcucumber.filter.tags="@cart or @search"
mvn clean verify -Dcucumber.filter.tags="@regression and not @slow"
```

<div id='github-actions'/>

### - GitHub Actions

#### **🚀 Team Test Execution Workflow**

The project includes a GitHub Actions workflow for running tests in CI/CD pipeline with the same simplified format:

1. **Navigate to Actions**: Go to your repository → **Actions** → **Team Test Execution**

2. **Configure Test Run**:
   - **Test Suite**: Choose from predefined suites or `custom_tags`
   - **Environment**: Select target environment (DEV-GDANSK, DEV-KRAKOW, STAGING, PROD)
   - **Browser**: Choose Chrome, Firefox, or Edge
   - **Parallel Threads**: Set number of parallel threads (1-8)

3. **Custom Tag Examples**:
   ```
   # Single numeric tag
   Custom Tag Numbers: 0001

   # Multiple numeric tags
   Custom Tag Numbers: 0001,0002,0003

   # Advanced expressions (use Custom Tag Expression field)
   Custom Tag Expression: @0001 and @smoke
   ```

4. **Predefined Test Suites**:
   - **search** - Search functionality tests
   - **cart** - Shopping cart tests
   - **checkout** - Checkout process tests
   - **order** - Order management tests
   - **smoke** - Critical functionality tests
   - **regression** - Comprehensive test suite
   - **all** - Execute all available tests

#### **📊 Workflow Features**:
- ✅ **Multi-browser support** (Chrome, Firefox, Edge)
- ✅ **Simplified tag format** (same as local testing)
- ✅ **Automatic report generation** and artifact upload
- ✅ **Environment-specific configurations**
- ✅ **Parallel execution support**
- ✅ **Live links to test reports**
- ✅ **Workflow cleanup utility** for storage management

#### **🔗 Accessing Test Reports**:
After workflow completion, test reports are available as:
- **Artifacts**: Download complete target directory (retained for 1 day)
- **GitHub Pages**: Live links to Serenity reports (when available)
- **Direct Links**: Generated links to index.html and summary reports

**📝 Note**: Artifacts are automatically deleted after 1 day to optimize GitHub storage usage.

#### **🗑️ Workflow Cleanup**:
For managing repository storage, use the **Cleanup Workflow Runs** workflow:
- **Purpose**: Delete old workflow runs to free up GitHub storage space
- **Safety**: Requires confirmation and supports dry-run mode
- **Flexibility**: Delete all workflows or target specific ones
- **Retention**: Option to keep recent workflows (e.g., last 30 days)

**Usage**: Go to Actions → "🗑️ Cleanup Workflow Runs" → Run workflow
**Documentation**: See `WORKFLOW_CLEANUP_GUIDE.md` for detailed instructions

<div id='parallel-run'/>

### - Parallel run
 
Parallel run is executed from command line.
Setup located in junit-platform.properties file.

```
cucumber.execution.parallel.enabled=true - enable parallel execution
cucumber.execution.parallel.config.strategy=fixed
cucumber.execution.parallel.config.fixed.parallelism=12 - enter number of threads (depends from the project)
cucumber.execution.parallel.config.fixed.max-pool-size=12 - enter number of threads (depends from the project)
```

---

<!--PROJECT DEPENDENCIES-->

<div id='project-dependencies'/>

## 5. Project dependencies

* `serenity-rest-assured` -> all Rest Assured tests are valid tests for Serenity BDD.
* `serenity-cucumber` -> run Serenity tests and generate Serenity reports using Cucumber 6.
* `serenity-junit` -> provide annotations to tag tests and test classes.
* `javafaker` -> libraries with different fake data for testing.
* `awaitility` -> a Java DSL for synchronizing asynchronous operations.
---

<!--PROJECT STRUCTURE-->

<div id='project-structure'/>

## 6. Project structure

* `src`
    * `main`
        * `java`
            * `actions` - all actions store here.
            * `common`:
                * `constants` - constant values that are separated for better readability and maintainability of code.
            * `objects_behaviors` - abstract factory design pattern for Selenium(like Utility)
            * `pages` - plain Page Objects for all web pages.
            * `utils` - data related utility classes.
    * `test`
        * `java`
            * `runner` - core runner class.
            * `steps` - step definition classes.
        * `resources` - contains serenity configurations
            * `features` - all test feature files on Gherkin language.
---

<!--ENVIRONMENTS & TAGS-->

<div id='environments-tags'/>

## 8. Available Environments & Tags

### **🏢 Supported Environments**

| Environment | Description | Usage |
|-------------|-------------|-------|
| `DEV-GDANSK` | Development environment - Gdansk | `-Denvironment=DEV-GDANSK` |
| `DEV-KRAKOW` | Development environment - Krakow | `-Denvironment=DEV-KRAKOW` |
| `STAGING` | Staging environment | `-Denvironment=STAGING` |
| `PROD` | Production environment | `-Denvironment=PROD` |

### **🏷️ Available Test Tags**

#### **Numeric Tags (Test Cases)**
| Tag | Description | Feature |
|-----|-------------|---------|
| `@0001` | Add single product to cart from search | Shopping Cart |
| `@0002` | Add single product from category page | Shopping Cart |
| `@0003` | Add multiple products to cart | Shopping Cart |
| `@0004` | Remove product from cart | Shopping Cart |
| `@0005` | Update product quantity in cart | Shopping Cart |
| `@0006` | Basic search functionality | Search |
| `@0007` | Search with filters | Search |
| `@0008` | Search suggestions | Search |
| `@0009` | Checkout process | Checkout |
| `@0010` | Order placement | Order Management |

#### **Functional Tags (Categories)**
| Tag | Description | Usage Example |
|-----|-------------|---------------|
| `@account_page` | Account page functionality tests | `-Dgroups="account_page"` |
| `@cart` | Shopping cart related tests | `-Dgroups="cart"` |
| `@cart1` | Shopping cart tests (variant 1) | `-Dgroups="cart1"` |
| `@checkout` | Checkout process tests | `-Dgroups="checkout"` |
| `@guest_order` | Order placement as guest user | `-Dgroups="guest_order"` |
| `@logged_in_order` | Order placement as logged-in user | `-Dgroups="logged_in_order"` |
| `@order` | Order management tests | `-Dgroups="order"` |
| `@regression` | Comprehensive test suite | `-Dgroups="regression"` |
| `@search` | Search functionality tests | `-Dgroups="search"` |
| `@smoke` | Critical functionality tests | `-Dgroups="smoke"` |
| `@sort` | Sorting functionality tests | `-Dgroups="sort"` |

#### **Sequential Execution Tags**
| Tag | Description | Usage Example |
|-----|-------------|---------------|
| `@order_logged_in_sequential_1` | Sequential logged-in order tests (group 1) | `-Dgroups="order_logged_in_sequential_1"` |
| `@order_logged_in_sequential_2` | Sequential logged-in order tests (group 2) | `-Dgroups="order_logged_in_sequential_2"` |
| `@sequential` | General sequential execution tests | `-Dgroups="sequential"` |

#### **Special/Utility Tags**
| Tag | Description | Usage Example |
|-----|-------------|---------------|
| `@abi` | Special ABI tests | `-Dgroups="abi"` |
| `@abi2` | Special ABI tests (group 2) | `-Dgroups="abi2"` |
| `@abi3` | Special ABI tests (group 3) | `-Dgroups="abi3"` |
| `@jio1` | JIO1 specific tests | `-Dgroups="jio1"` |
| `@remove_newly_added_address` | Address cleanup utility tests | `-Dgroups="remove_newly_added_address"` |

#### **Priority Tags**
| Tag | Description | Usage |
|-----|-------------|-------|
| `@critical` | Critical business functionality | `-Dgroups="critical"` |
| `@high` | High priority tests | `-Dgroups="high"` |
| `@medium` | Medium priority tests | `-Dgroups="medium"` |
| `@low` | Low priority tests | `-Dgroups="low"` |

### **🌐 Browser Support**

| Browser | Command | Environment Override |
|---------|---------|---------------------|
| **Chrome** | `-Dwebdriver.driver=chrome` | Not required |
| **Firefox** | `-Dwebdriver.driver=firefox` | `-Dserenity.environment=firefox` |
| **Edge** | `-Dwebdriver.driver=edge` | `-Dserenity.environment=edge` |

### **📋 Tag Combination Examples**

```bash
# Single test case
mvn clean verify -Dgroups="0001" -Denvironment=DEV-GDANSK -Dwebdriver.driver=chrome

# Multiple specific test cases
mvn clean verify -Dgroups="0001,0002,0003" -Denvironment=STAGING -Dwebdriver.driver=firefox -Dserenity.environment=firefox

# All cart tests
mvn clean verify -Dgroups="cart" -Denvironment=DEV-GDANSK -Dwebdriver.driver=edge -Dserenity.environment=edge

# Smoke tests across all browsers
mvn clean verify -Dgroups="smoke" -Denvironment=STAGING -Dwebdriver.driver=chrome
mvn clean verify -Dgroups="smoke" -Denvironment=STAGING -Dwebdriver.driver=firefox -Dserenity.environment=firefox
mvn clean verify -Dgroups="smoke" -Denvironment=STAGING -Dwebdriver.driver=edge -Dserenity.environment=edge

# Mixed tags
mvn clean verify -Dgroups="0001,cart,smoke" -Denvironment=DEV-GDANSK -Dwebdriver.driver=chrome
```

---

<!--USAGE-->

<div id='usage'/>

<!--REPORT-->

<div id='report'/>

## 9. Report

**Location:** Target -> site -> serenity:
* **SINGLE PAGE REPORT:** -> serenity-summary.html
* **FULL REPORT:** -> index.html

**Generate report:**
* It is always generated when execute tests from command line, for example: ***mvn clean verify***
* If execute test through RunnerTestSuite class, then to generate report use command: ***mvn serenity:aggregate***

---
<!--INTERNAL CONVENTIONS-->

<div id='internal-conventions'/>

## 10. Internal conventions

* **Feature file:** <br>
  -**Tags:** starts from '@' and all lowercase.<br>
  -**Generate Step Def files:** always generate step def method through RunnerTestSuite class, <br>
  -by changing dryRun to true and passing correct tag.


* **Step Def:**<br>
  -Method: naming of the method should follow underscore convention 'method_name'.


* **Packages:**<br>
  -Should be lowercase and follow underscore convention.<br>
  -Naming of the packages and classes should be the same in Pages, Actions, Step Def, <br>
  -just add package name at the end.<br>
  -**For example:** Test Actions, TestPages, TestSteps.
---

<!--TIPS-->

<div id='tips'/>

## 11. Tips

* Start Automation in next steps: Feature file -> Step Def -> Actions -> Pages
* Use command 'CTRL + ALT + L' - it will fix style on the page
* Before commit changes on git do next: Click the right button on Project and select 'Optimize Imports' (automatically delete all unused imports in project)

---

<!--ADDITIONAL DOCUMENTATIONS-->

<div id='additional-documentations'/>

## 12. Additional Documentations

- [Serenity BDD Framework](https://serenity-bdd.github.io/docs/guide/user_guide_intro)
- [Lombok](https://projectlombok.org/features/all)
- [AssertJ](https://assertj.github.io/doc/)
- [Cucumber](https://cucumber.io/)
- [Cucumber BDD DOC Gherkin](https://cucumber.io/docs/gherkin/reference/)
- [BDD Gherkin rules](https://techbeacon.com/app-dev-testing/better-behavior-driven-development-4-rules-writing-good-gherkin)
---

## 🚀 Quick Reference

### **Most Common Commands**

```bash
# Single test with Chrome (simplest)
mvn clean verify -Dgroups="0001"

# Single test with Firefox
mvn clean verify -Dgroups="0001" -Dwebdriver.driver=firefox -Dserenity.environment=firefox

# Multiple tests with specific environment
mvn clean verify -Dgroups="0001,0002,0003" -Denvironment=STAGING

# All cart tests with Edge
mvn clean verify -Dgroups="cart" -Dwebdriver.driver=edge -Dserenity.environment=edge

# Smoke tests with parallel execution
mvn clean verify -Dgroups="smoke" -Dcucumber.execution.parallel.config.fixed.parallelism=6
```

### **GitHub Actions Quick Start**
1. Go to **Actions** → **Team Test Execution**
2. Select **Test Suite**: `custom_tags` or `account_page`
3. Enter **Custom Tag Numbers**: `0001` or `0001,0002,0003` (if using custom_tags)
4. Choose **Browser**: `chrome`, `firefox`, or `edge`
5. Select **Environment**: `DEV-GDANSK`, `STAGING`, etc.
6. Click **Run workflow**



---

## [GO TOP](#top)