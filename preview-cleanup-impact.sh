#!/bin/bash

echo "🔍 Preview: What Will Be Cleaned Up"
echo "==================================="
echo ""
echo "This script shows you exactly what the cleanup workflow will process"
echo "when you run it with 'Delete All Workflows' option."
echo ""

# Check if we can access GitHub (basic check)
if ! curl -s https://api.github.com > /dev/null; then
    echo "❌ Cannot access GitHub API"
    echo "This preview requires internet access to GitHub"
    exit 1
fi

echo "📁 Repository: This will scan your patio repository"
echo "🎯 Scope: ALL workflows (current and historical)"
echo ""

echo "💡 What the cleanup workflow will do:"
echo "======================================"
echo ""
echo "1️⃣ Find ALL workflows in your repository:"
echo "   - Current active workflows (like Team Test Execution)"
echo "   - Old workflows from deleted .yml files"
echo "   - Any workflow that has ever run"
echo ""
echo "2️⃣ For EACH workflow found, it will:"
echo "   - List all workflow runs (completed, failed, cancelled)"
echo "   - Show run dates, status, and conclusions"
echo "   - Delete runs based on your criteria (age, specific workflow, etc.)"
echo ""
echo "3️⃣ What gets deleted:"
echo "   ✅ Workflow run records and logs"
echo "   ✅ All artifacts (test reports, files, etc.)"
echo "   ✅ Run history and execution details"
echo ""
echo "4️⃣ What stays safe:"
echo "   ❌ Workflow .yml files (your actual workflow definitions)"
echo "   ❌ Repository code and commits"
echo "   ❌ Issues, PRs, secrets, variables"
echo ""

echo "🚀 To see EXACTLY what will be affected:"
echo "========================================"
echo ""
echo "1. Run the cleanup workflow with these settings:"
echo "   Confirm Deletion: DELETE ALL WORKFLOWS"
echo "   Days to Keep: 0 (or any number you prefer)"
echo "   Workflow Name: (leave empty for all workflows)"
echo "   Dry Run: ✅ TRUE (this is key!)"
echo ""
echo "2. The dry run will show you:"
echo "   - Every workflow name found"
echo "   - Every run that would be deleted"
echo "   - Dates, status, and details of each run"
echo "   - Total count of what would be deleted"
echo ""
echo "3. After reviewing the dry run results:"
echo "   - If you're happy with what would be deleted, run again with Dry Run: false"
echo "   - If you want to keep some workflows, adjust the parameters"
echo ""

echo "⚠️ IMPORTANT REMINDERS:"
echo "======================="
echo ""
echo "🔒 Safety first:"
echo "   - ALWAYS run with 'Dry Run: true' first"
echo "   - Review the output carefully"
echo "   - Deletions are permanent and cannot be undone"
echo ""
echo "📊 Storage impact:"
echo "   - This will free up significant GitHub Actions storage"
echo "   - All artifacts (reports, logs) will also be deleted"
echo "   - You'll see immediate storage reduction in GitHub settings"
echo ""
echo "🎯 Targeting options:"
echo "   - Leave 'Workflow Name' empty to clean ALL workflows"
echo "   - Set 'Days to Keep' > 0 to preserve recent runs"
echo "   - Use specific workflow names to target individual workflows"
echo ""

echo "✅ Ready to proceed?"
echo "==================="
echo ""
echo "Go to: Actions → '🗑️ Cleanup Workflow Runs' → Run workflow"
echo ""
echo "Start with: Dry Run = TRUE to see what would be deleted!"
echo ""
echo "This cleanup will process ALL workflows in your repository,"
echo "including old ones that still show runs in your Actions tab."
