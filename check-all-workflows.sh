#!/bin/bash

echo "🔍 Checking All Workflows in Repository"
echo "======================================="
echo ""

# Check if gh CLI is available
if ! command -v gh &> /dev/null; then
    echo "❌ GitHub CLI (gh) is not installed"
    echo "This script requires GitHub CLI to check workflows"
    echo "Install it from: https://cli.github.com/"
    exit 1
fi

# Check if authenticated
if ! gh auth status &> /dev/null; then
    echo "❌ Not authenticated with GitHub CLI"
    echo "Run: gh auth login"
    exit 1
fi

REPO=$(gh repo view --json nameWithOwner -q .nameWithOwner)
echo "📁 Repository: $REPO"
echo ""

echo "🔍 Fetching all workflows..."
WORKFLOWS=$(gh api repos/$REPO/actions/workflows --jq '.workflows[] | {id: .id, name: .name, state: .state, path: .path}')

if [[ -z "$WORKFLOWS" ]]; then
    echo "❌ No workflows found in repository"
    exit 1
fi

echo "📋 Found Workflows:"
echo "==================="

WORKFLOW_COUNT=0
TOTAL_RUNS=0

# Process each workflow
echo "$WORKFLOWS" | jq -r '. | "\(.id)|\(.name)|\(.state)|\(.path)"' | while IFS='|' read -r WORKFLOW_ID WORKFLOW_NAME WORKFLOW_STATE WORKFLOW_PATH; do
    ((WORKFLOW_COUNT++))
    
    echo ""
    echo "🔄 Workflow #$WORKFLOW_COUNT:"
    echo "   ID: $WORKFLOW_ID"
    echo "   Name: $WORKFLOW_NAME"
    echo "   State: $WORKFLOW_STATE"
    echo "   Path: $WORKFLOW_PATH"
    
    # Get run count for this workflow
    RUN_COUNT=$(gh api repos/$REPO/actions/workflows/$WORKFLOW_ID/runs --jq '.total_count')
    echo "   Runs: $RUN_COUNT"
    
    if [[ $RUN_COUNT -gt 0 ]]; then
        # Get some recent runs
        echo "   Recent runs:"
        gh api repos/$REPO/actions/workflows/$WORKFLOW_ID/runs --jq '.workflow_runs[0:3][] | "     - \(.created_at) | \(.status) | \(.conclusion // "in_progress")"'
    fi
    
    TOTAL_RUNS=$((TOTAL_RUNS + RUN_COUNT))
done

echo ""
echo "📊 Summary:"
echo "==========="
echo "Total workflows found: $(echo "$WORKFLOWS" | jq -s length)"
echo "Total workflow runs: $TOTAL_RUNS"
echo ""
echo "💡 The cleanup workflow will process ALL of these workflows"
echo "   and delete runs based on your specified criteria."
echo ""
echo "🚀 Next steps:"
echo "1. Run the cleanup workflow with 'Dry Run: true' first"
echo "2. Review what would be deleted"
echo "3. Run again with 'Dry Run: false' to actually delete"
