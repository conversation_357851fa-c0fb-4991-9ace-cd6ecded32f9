@regression @coupon_code
Feature: Coupon Code Tests

  Background:
    Given that user is on the home page

  @0188 @smoke @smoke
  Scenario: Verify user can apply coupon code at checkout
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills recipient information as "individual"
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user applies coupon code "automation" at checkout
    Then verify coupon code is applied successfully at checkout
    And verify discount amount is displayed correctly at checkout
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @0189
  Scenario: Verify checkout with multiple products and coupon code applied at checkout
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user returns to the previous page
    And user opens the second product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills recipient information as "individual"
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user applies coupon code "automation" at checkout
    Then verify coupon code is applied successfully at checkout
    And verify discount amount is displayed correctly at checkout
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @0190
  Scenario: Verify checkout total calculation with coupon code applied at checkout
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills recipient information as "individual"
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user applies coupon code "automation" at checkout
    Then verify subtotal is calculated correctly at checkout
    And verify discount amount is displayed correctly at checkout
    And verify final total is calculated correctly at checkout
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @0191
  Scenario: Verify invalid coupon code error at checkout
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills recipient information as "individual"
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user applies coupon code "invalid123" at checkout
    Then verify coupon code error message is displayed at checkout

  @0192
  Scenario: Verify empty coupon code handling at checkout
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills recipient information as "individual"
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user applies coupon code " " at checkout
    Then verify coupon code error message is displayed at checkout

  @0193
  Scenario: Verify coupon code persistence after checkout navigation
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills recipient information as "individual"
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user applies coupon code "automation" at checkout
    Then verify coupon code is applied successfully at checkout
    And user goes back to delivery method selection
    And click on continue button
    Then verify coupon code is preserved at checkout
    And verify discount amount is displayed correctly at checkout

  @0194
  Scenario: Verify checkout with quantity changes and coupon code
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And user changes quantity to "2" in cart
    And user applies discount code "automation"
    Then verify discount is applied correctly and total price is updated with discount applied
    And open cart and proceed to checkout
    And user fills recipient information as "individual"
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    Then verify coupon code is preserved at checkout
    And verify discount amount is displayed correctly at checkout
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @0195
  Scenario: Verify checkout without coupon code
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills recipient information as "individual"
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    Then verify no coupon code is applied at checkout
    And verify subtotal equals final total at checkout
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @0017 @smoke
  Scenario: Verify user can apply discount code in cart
    When user searches for "Fotel"
    And user opens the first product from search results
    And user adds the product to cart
    And user opens cart
    And user applies discount code "automation"
    Then verify discount is applied correctly and total price is updated with discount applied

  @0018
  Scenario: Verify the discount is updated correctly when user adds more quantity of the same product in cart
    When user searches for "Fotel"
    And user opens the first product from search results
    And user adds the product to cart
    And user opens cart
    When user applies discount code "automation"
    Then verify discount is applied correctly and total price is updated with discount applied
    And user changes quantity to "2" in cart
    Then verify discount is applied correctly and total price is updated with discount applied

  @0019
  Scenario: Verify the discount is updated correctly when user adds another product in cart
    When user searches for "Fotel"
    And user opens the first product from search results
    And user adds the product to cart
    And user opens cart
    When user applies discount code "automation"
    Then verify discount is applied correctly and total price is updated with discount applied
    And user returns to the previous page
    When user searches for "Krzesła"
    And user opens the second product from search results
    And user adds the product to cart
    And user opens cart
    Then verify discount is applied correctly and total price is updated with discount applied

  @0020
  Scenario: Verify the discount is updated correctly when user removes a product from cart
    When user searches for "Fotel"
    And user opens the first product from search results
    And user adds the product to cart
    And user opens cart
    When user applies discount code "automation"
    Then verify discount is applied correctly and total price is updated with discount applied
    And user returns to the previous page
    When user searches for "Krzesła"
    And user opens the second product from search results
    And user adds the product to cart
    And user opens cart
    And verify discount is applied correctly and total price is updated with discount applied
    And user removes one product from cart
    Then verify discount is applied correctly and total price is updated with discount applied

  @0021
  Scenario: Verify the discount is updated correctly when user reduces quantity of a product in cart
    When user searches for "Fotel"
    And user opens the first product from search results
    And user adds the product to cart
    And user opens cart
    When user applies discount code "automation"
    Then verify discount is applied correctly and total price is updated with discount applied
    And user changes quantity to "2" in cart
    Then verify discount is applied correctly and total price is updated with discount applied
    And user changes quantity to "1" in cart
    Then verify discount is applied correctly and total price is updated with discount applied

  @0022 @smoke
  Scenario: Verify user can remove discount code in cart
    When user searches for "Fotel"
    And user opens the first product from search results
    And user adds the product to cart
    And user opens cart
    And user applies discount code "automation"
    And user removes discount code
    Then verify discount is removed and total price is updated correctly

  @0023
  Scenario: Verify user can not apply discount code that does not exist
    When user searches for "Fotel"
    And user opens the first product from search results
    And user adds the product to cart
    And user opens cart
    And user applies discount code "nonexistentcode"
    Then verify error message is displayed indicating invalid discount code
    And verify total price remains unchanged

  @0255
  Scenario: Verify user can delete applied coupon code at checkout
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills recipient information as "individual"
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user applies coupon code "automation" at checkout
    Then verify coupon code is applied successfully at checkout
    And verify discount amount is displayed correctly at checkout
    And user removes coupon code at checkout
    Then verify coupon code is removed at checkout

  @0256
  Scenario: Verify user can remove and apply another coupon code at cart page
    When user searches for "Fotel"
    And user opens the first product from search results
    And user adds the product to cart
    And user opens cart
    And user applies discount code "automation"
    Then verify discount is applied correctly and total price is updated with discount applied
    And user removes discount code
    Then verify discount is removed and total price is updated correctly
    And user applies discount code "automation2"
    Then verify discount is applied correctly and total price is updated with discount applied

  @0257
  Scenario: Verify user can remove and apply another coupon code at checkout page
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills recipient information as "individual"
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user applies coupon code "automation" at checkout
    Then verify coupon code is applied successfully at checkout
    And verify discount amount is displayed correctly at checkout
    And user removes coupon code at checkout
    Then verify coupon code is removed at checkout
    And user applies coupon code "automation2" at checkout
    Then verify coupon code is applied successfully at checkout
    And verify discount amount is displayed correctly at checkout

  @0324
  Scenario: Verify coupon code is applied until order from cart
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And user applies discount code "automation"
    Then verify discount is applied correctly and total price is updated with discount applied
    And open cart and proceed to checkout
    Then verify coupon code is preserved at initial checkout step
    And user fills recipient information as "individual"
    And click on continue button
    Then verify coupon code is preserved after address form completion
    And user selects delivery method "Kurier"
    And click on continue button
    Then verify coupon code is preserved after delivery method selection
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @0325
  Scenario: Verify coupon code state during login at checkout
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user applies coupon code "automation" at checkout
    Then verify coupon code is applied successfully at checkout
    And user clicks on login link at checkout
    And user logs in at checkout with account test credentials
    Then verify user is logged in at checkout
    And verify coupon code is preserved after login at checkout
    And select one saved shipping address from the list
    And check the same as delivery checkbox
    And click on continue button
    Then verify coupon code is preserved after address form completion
    And user selects delivery method "Kurier"
    And click on continue button
    Then verify coupon code is preserved after delivery method selection
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @0328
  Scenario: Verify coupon code price is updated when user applies coupon code and adds quantity at cart
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user applies coupon code "automation" at checkout
    Then verify coupon code is applied successfully at checkout
    And verify discount amount is displayed correctly at checkout
    When user clicks on show cart link at checkout
    And user changes quantity to "2" in cart
    Then verify discount value is updated correctly after quantity change
    When open cart and proceed to checkout
    And user fills recipient information as "individual"
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    Then verify discount value is updated correctly after quantity change