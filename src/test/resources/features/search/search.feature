@regression
Feature: Product Search

  Background: Open the home page
    Given that user is on the home page

  @search @0024 @smoke
  Scenario: Verify user is able to search for a product
    When user searches for "Fotel"
    Then user should see the search results page
    And the number of products header is displayed
    And verify user should see that the results contain "Fotel"

  @search @0025
  Scenario: Verify search bar dropdown
    When user clicks on the search bar
    Then user should see the search bar dropdown
    And verify user is able to close the search bar dropdown

  #Commented out due to the issue with category links in the search dropdown
  #@search @0026 @regression
  #Scenario: Verify user can navigate to category page from search bar dropdown
  #  When user clicks on the search bar
  #  Then verify that all category links in the search dropdown open correct pages

  @search @0027
  Scenario: Verify user can close the quick search dropdown
    When user clicks on the search bar
    Then user should see the search bar dropdown
    And user should be able to close the search bar dropdown

  @search @0028
  Scenario Outline: Verify search is case-insensitive
    When user searches for "<searchTerm>"
    Then user should see the search results page
    And the number of products header is displayed
    And verify user should see that the results contain "<searchTerm>"
    Examples:
      | searchTerm |
      | FOTEL      |
      | fotel      |
      | FoTeL      |

  @search @0029
  Scenario: Verify search results for non-existing product
    When user searches for "NonExistingProduct"
    And user should see the search results page
    Then verify user should see that the header shows no results found for "NonExistingProduct"

  @search @0030
  Scenario: Verify that the number of results matches the count shown
    When user searches for "Fotel"
    And user should see the search results page
    And the number of products header is displayed
    Then verify user should see that he number of results matches the count shown

  @search @0031 @smoke
  Scenario: Verify that product titles and prices are visible on the results page
    When user searches for "Fotel"
    And user should see the search results page
    Then verify user should see that product titles and prices are visible on the results page

  @search @0032
  Scenario Outline: Verify the search box is visible and accessible on all relevant pages
    Given that user is on the "<page>"
    Then verify user should see the search box
    When user searches for "Fotel"
    Then user should see the search results page
    Examples:
      | page                |
      | home page           |
      | category page       |
      | product page        |
      | cart page           |
      | contact page        |
      | about page          |
      | copyright page      |
      | cookies policy page |
      | privacy policy page |
      | inspirations page   |
      | error page          |

  @search @0033
  Scenario Outline: Verify search results page persists in different sessions
    When user searches for "Fotel"
    And user should see the search results page
    Then verify user should see that the search results page persists in different "<action>"
    Examples:
      | action                                         |
      | refresh page                                   |
      | open product and return to search results page |

  @search @0034
  Scenario: Verify user can search by clicking on the search icon
    When user types "Fotel" in the search bar
    And user clicks on the search icon
    Then user should see the search results page
    And verify user should see that the results contain "Fotel"