@regression @login
Feature: Login Tests

  Background: Open the home page
    Given that user is on the home page

  @0213 @smoke
  Scenario: Verify user can login
    When user clicks on login link
    And user enters valid login credentials
    And user clicks on login button
    Then verify user is successfully logged in

  @0214 @smoke
  Scenario: Verify invalid email login
    When user clicks on login link
    And user enters invalid email "test.@." and valid password
    And user clicks on login button
    Then verify invalid email error message is displayed on login modal

  #comment out for now, because of bug
  #@login @0215 @smoke
  #Scenario: Verify empty password login
  #  When user clicks on login link
  #  And user enters valid email and empty password
  #  And user clicks on login button
  #  Then verify empty password error message is displayed

  @0216 @smoke
  Scenario: Verify invalid user credentials login
    When user clicks on login link
    And user enters invalid credentials
    And user clicks on login button
    Then verify invalid login credentials error message is displayed

  @0217 @smoke
  Scenario: Verify login modal elements
    When user clicks on login link
    Then verify login modal header is displayed
    And verify login modal sub header is displayed
    And verify forgot password link is displayed
    And verify you dont have account text is displayed
    And verify register link is displayed

  @0218
  Scenario: Verify user can send forget password request
    When user clicks on login link
    And user clicks on forgot password link
    Then verify forgot password modal header is displayed
    And verify forgot password description is displayed
    When user enters valid email for password reset
    And user clicks on reset password button
    Then verify reset password success message header is displayed
    And verify reset password success message is displayed
    When user clicks on close reset password modal button
    Then verify login modal is closed

  @0219
  Scenario: Verify user can get back to login modal from forgot password modal
    When user clicks on login link
    And user clicks on forgot password link
    Then verify forgot password modal header is displayed
    When user clicks on return to login button
    Then verify login modal header is displayed

  @0220
  Scenario: Verify empty and invalid email error on forgot password modal
    When user clicks on login link
    And user clicks on forgot password link
    When user enters invalid email "test.@." for password reset
    And user clicks on reset password button
    Then verify invalid email error message is displayed on forgot password modal
    When user clears email field for password reset
    And user clicks on reset password button
    Then verify invalid email error message is displayed on forgot password modal

  @0221
  Scenario: Verify login modal can be closed and reopened
    When user clicks on login link
    Then verify login modal header is displayed
    When user closes the login modal
    Then verify login modal is closed
    When user clicks on login link again
    Then verify login modal header is displayed

  @0222
  Scenario: Verify user can navigate to register modal from login modal
    When user clicks on login link
    And user clicks on register link
    Then verify register modal header is displayed
    And verify login link is displayed on register modal
    When user clicks on login link on register modal
    Then verify login modal header is displayed

  #Commented out for now, because of bug
  #@login @0223
  #Scenario: Verify login with whitespace-only email and password
  #  When user clicks on login link
  #  And user enters email with only whitespaces and password with only whitespaces
  #  And user clicks on login button
  #  Then verify appropriate validation error messages are displayed

  @0224
  Scenario: Verify login with very long email and password
    When user clicks on login link
    And user enters very long email and very long password
    And user clicks on login button
    Then verify login attempt is handled appropriately

  @0225
  Scenario: Verify login persists across page refresh
    When user clicks on login link
    And user enters valid login credentials
    And user clicks on login button
    Then verify user is successfully logged in
    When user refreshes the page after login
    Then verify user remains logged in

  @0226
  Scenario: Verify multiple failed login attempts handling
    When user clicks on login link
    And user enters invalid credentials
    And user clicks on login button
    Then verify invalid login credentials error message is displayed
    When user enters invalid credentials again
    And user clicks on login button
    Then verify invalid login credentials error message is displayed
    When user enters invalid credentials again
    And user clicks on login button
    Then verify invalid login credentials error message is displayed

  @0227
  Scenario: Verify login modal keyboard navigation
    When user clicks on login link
    And user navigates through login form using tab key
    Then verify all form elements are accessible via keyboard
    And verify user can submit form using enter key
    And verify user is logged in

  @0228
  Scenario: Verify forgot password modal can be closed
    When user clicks on login link
    And user clicks on forgot password link
    Then verify forgot password modal header is displayed
    When user closes forgot password modal
    Then verify forgot password modal is closed
