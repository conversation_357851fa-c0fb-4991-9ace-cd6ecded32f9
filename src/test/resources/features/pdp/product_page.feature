@regression
Feature: Product Page Tests

  Background: Open the home page
    Given that user is on the home page

  @pdp @0160 @smoke
  Scenario: Verify product image is displayed
    When user searches for product by code "47550"
    Then verify product image is displayed

  @pdp @0161
  Scenario: Verify delivery price header for product below 300 zł
    When user searches for product by code "47550"
    Then verify delivery price header shows "Dostawa od: 12,99 zł"

  @pdp @0163
  Scenario: Verify delivery price header for product above 300 zł
    When user searches for product by code "461830"
    Then verify delivery price header shows "Dostawa: 0,00 zł"

  @pdp @0164 @smoke
  Scenario: Verify product details section functionality
    When user searches for product by code "461830"
    Then verify dimension and details button is displayed
    When user clicks on dimension and details button
    Then verify dimension and details header shows "Wymiary i szczegóły"
    And verify dimension and details content is displayed with correct information

  @pdp @0165 @smoke
  Scenario: Verify product added view cart button functionality
    When user searches for product by code "47550"
    And save product details to storage
    And user adds the product to cart
    Then verify product added view cart button is displayed
    When user clicks on product added view cart button
    Then verify cart is opened
    And verify product is displayed in cart

  @pdp @0166 @smoke
  Scenario: Verify plus button increases product quantity and adds correct amount to cart
    When user searches for product by code "47550"
    Then verify product amount input shows "1"
    When user clicks plus button randomly between 2 and 5 times
    And user adds the product to cart
    And user opens cart
    Then verify product quantity in cart matches the selected amount

  @pdp @0167 @smoke
  Scenario: Verify plus and minus buttons update product quantity correctly
    When user searches for product by code "47550"
    Then verify product amount input shows "1"
    When user clicks plus button randomly between 3 and 7 times
    Then verify product amount input is updated correctly
    When user clicks minus button randomly between 1 and 3 times
    Then verify product amount input is updated correctly after minus clicks

  @pdp @0168
  Scenario: Verify minus button cannot reduce quantity below 1
    When user searches for product by code "47550"
    Then verify product amount input shows "1"
    When user clicks minus button 5 times
    Then verify product amount input shows "1"

  @pdp @0169
  Scenario: Verify maximum quantity limit handling
    When user searches for product by code "47550"
    And save product details to storage
    Then verify product amount input shows "1"
    When user clicks plus button 50 times
    Then verify product amount input does not exceed reasonable limit
    And verify plus button behavior at maximum quantity
    And verify maximum quantity error message is displayed
    When user adds the product to cart
    And user opens cart
    Then verify product name is displayed in cart
    And verify product quantity in cart matches the maximum allowed quantity

  @pdp @0170
  Scenario: Verify user can send a question about the product
    When user searches for product by code "47550"
    And user clicks on ask question button
    And user enters email "<EMAIL>" for question
    And user enters message "This is a test question about the product. What are the dimensions?" for question
    And user checks agree to privacy policy checkbox
    And user clicks send message button
    Then verify message sent confirmation is displayed

  @pdp @0171
  Scenario: Verify error message when trying to send question without checking privacy policy
    When user searches for product by code "47550"
    And user clicks on ask question button
    And user enters random email for question
    And user enters message "This is a test question without privacy policy agreement." for question
    And user clicks send message button
    Then verify error message for unselected privacy policy checkbox shows "Zaznacz wymagane zgody."

  @pdp @0172 @smoke
  Scenario: Verify user can add multiple products to cart by typing quantity in input field
    When user searches for product by code "47550"
    And save product details to storage
    Then verify product amount input shows "1"
    When user types quantity "5" in input field
    Then verify product quantity input shows "5"
    When user adds the product to cart
    And user opens cart
    Then verify product is displayed in cart
    And verify product quantity in cart matches the selected amount

  @pdp @0173
  Scenario: Verify error message when user enters quantity above maximum limit via input
    When user searches for product by code "47550"
    And save product details to storage
    Then verify product amount input shows "1"
    When user types quantity "100" in input field
    Then verify maximum quantity error message is displayed for input

  @pdp @0174
  Scenario: Verify user can reduce quantity with minus button after entering value via input
    When user searches for product by code "47550"
    And save product details to storage
    Then verify product amount input shows "1"
    When user types quantity "10" in input field
    Then verify product quantity input shows "10"
    When user clicks minus button after typing quantity
    Then verify quantity is updated correctly after button click
    When user adds the product to cart
    And user opens cart
    Then verify product is displayed in cart
    And verify product quantity in cart matches the selected amount

  @pdp @0175
  Scenario: Verify user can increase quantity with plus button after entering value via input
    When user searches for product by code "47550"
    And save product details to storage
    Then verify product amount input shows "1"
    When user types quantity "8" in input field
    Then verify product quantity input shows "8"
    When user clicks plus button after typing quantity
    Then verify quantity is updated correctly after button click
    When user adds the product to cart
    And user opens cart
    Then verify product is displayed in cart
    And verify product quantity in cart matches the selected amount

  @pdp @0176 @smoke
  Scenario: Verify related products section on PDP
    When user searches for product by code "461830"
    Then verify related products header shows "Powiązane produkty"
    And verify related products are listed
    And verify related product prices are displayed
    And verify related product names are displayed

  @pdp @0177 @smoke
  Scenario: Verify breadcrumbs are displayed on PDP
    When user searches for product by code "461830"
    Then verify breadcrumbs are displayed

  @pdp @0178
  Scenario: Verify breadcrumb links navigation functionality
    When user searches for product by code "461830"
    Then verify breadcrumb links navigation works correctly

  @pdp @0179 @smoke
  Scenario: Verify product SKU is displayed
    When user searches for product by code "47550"
    Then verify product SKU is displayed

  @pdp @0180
  Scenario: Verify includes VAT price sub text is displayed
    When user searches for product by code "47550"
    Then verify includes VAT text shows "z VAT, bez kosztów dostawy"

  @pdp @0181
  Scenario: Verify cost without delivery link navigation and content
    When user searches for product by code "47550"
    And user clicks on cost without delivery link
    Then verify user is on payment and delivery page
    And verify payment and delivery page content matches expected text

  @pdp @0182 @smoke
  Scenario: Verify product description is displayed and contains content
    When user searches for product by code "47550"
    Then verify product description is displayed and contains content

  @pdp @0183
  Scenario: Verify delivery details link in product description works
    When user searches for product by code "47550"
    And user clicks on delivery details link in product description
    Then verify user is on payment and delivery page
    And verify payment and delivery page content matches expected text

  @pdp @0184 @smoke
  Scenario: Verify graphic dimension detail is displayed
    When user searches for product by code "461830"
    And user clicks on dimension and details button
    Then verify graphic dimension detail is displayed

  @pdp @0185
  Scenario: Verify question and answers section functionality
    When user searches for product by code "461830"
    And user clicks on question and answers button
    Then verify question and answers section displays correct count and content

  @pdp @0186
  Scenario: Verify question and answers list items are expandable and closable
    When user searches for product by code "461830"
    And user clicks on question and answers button
    Then verify question and answers list items are expandable and closable when clicked
