@regression @registration
Feature: User Registration Tests

  Background: Open the home page and navigate to registration
    Given that user is on the home page

  @0230 @smoke
  Scenario: Verify user can register with all valid information
    When user clicks on login link
    And user clicks on register link
    And user enters valid registration information with first name last name email and password
    And user accepts the registration agreement
    And user clicks on registration button
    Then verify registration success message header is displayed
    And verify registration success message is displayed
    When user closes registration success modal
    Then verify registration modal is closed

  @0231
  Scenario: Verify invalid email registration
    When user clicks on login link
    And user clicks on register link
    And user enters invalid email format for registration
    And user enters valid password for registration
    And user accepts the registration agreement
    And user clicks on registration button
    Then verify incorrect email format error message is displayed

  #Commented out for now, because of bug
  #@registration @0232 @smoke
  #Scenario: Verify empty password registration
  #  When user clicks on login link
  #  And user clicks on register link
  #  And user enters valid email for registration
  #  And user leaves password field empty for registration
  #  And user accepts the registration agreement
  #  And user clicks on registration button
  #  Then verify empty password error message is displayed for registration

  #Commented out for now, because of bug
  #@registration @0233 @smoke
  #Scenario: Verify small length password registration
  #  When user clicks on login link
  #  And user clicks on register link
  #  And user enters valid email for registration
  #  And user enters short password for registration
  #  And user accepts the registration agreement
  #  And user clicks on registration button
  #  Then verify password too short error message is displayed for registration

  @0234
  Scenario: Verify empty email registration
    When user clicks on login link
    And user clicks on register link
    And user leaves email field empty for registration
    And user enters valid password for registration
    And user accepts the registration agreement
    And user clicks on registration button
    Then verify incorrect email format error message is displayed

  @0235
  Scenario: Verify trying to register without accepting the agreement
    When user clicks on login link
    And user clicks on register link
    And user enters valid email for registration
    And user enters valid password for registration
    And user does not accept the registration agreement
    And user clicks on registration button
    Then verify agreement checkbox not checked error message is displayed

  @0236
  Scenario: Verify the agreement text label
    When user clicks on login link
    And user clicks on register link
    Then verify registration agreement checkbox label text is displayed

  @0237
  Scenario: Verify registering without accepting, see error, then accept and register
    When user clicks on login link
    And user clicks on register link
    And user enters valid email for registration
    And user enters valid password for registration
    And user does not accept the registration agreement
    And user clicks on registration button
    Then verify agreement checkbox not checked error message is displayed
    When user accepts the registration agreement
    And user clicks on registration button
    Then verify registration success message header is displayed
    And verify registration success message is displayed

  @0238
  Scenario: Verify user can register with only email and password
    When user clicks on login link
    And user clicks on register link
    And user enters valid email for registration
    And user enters valid password for registration
    And user accepts the registration agreement
    And user clicks on registration button
    Then verify registration success message header is displayed
    And verify registration success message is displayed

  @0239
  Scenario: Verify registration modal elements are displayed
    When user clicks on login link
    And user clicks on register link
    Then verify registration modal header is displayed
    And verify do you have an account text is displayed
    And verify registration first name input is displayed
    And verify registration last name input is displayed
    And verify registration email input is displayed
    And verify registration password input is displayed
    And verify registration agreement checkbox is displayed
    And verify registration button is displayed

  @0240
  Scenario: Verify user can navigate back to login from registration modal
    When user clicks on login link
    And user clicks on register link
    Then verify registration modal header is displayed
    When user clicks on login link on register modal
    Then verify login modal header is displayed

  #@registration @0241 @smoke
  #Scenario: Verify registration with existing email address
  #  When user clicks on login link
  #  And user clicks on register link
  #  And user enters already registered email for registration
  #  And user enters valid password for registration
  #  And user accepts the registration agreement
  #  And user clicks on registration button
  #  Then verify email already exists error message is displayed

  @0242 @smoke
  Scenario: Verify registration form field validation - multiple empty fields
    When user clicks on login link
    And user clicks on register link
    And user leaves all registration fields empty
    And user clicks on registration button
    Then verify multiple field validation errors are displayed

  @0243
  Scenario: Verify registration with very long email address
    When user clicks on login link
    And user clicks on register link
    And user enters very long email for registration
    And user enters valid password for registration
    And user accepts the registration agreement
    And user clicks on registration button
    Then verify registration success message header is displayed
    And verify registration success message is displayed
    When user closes registration success modal
    Then verify registration modal is closed

  @0244
  Scenario: Verify registration with very long password
    When user clicks on login link
    And user clicks on register link
    And user enters valid email for registration
    And user enters very long password for registration
    And user accepts the registration agreement
    And user clicks on registration button
    Then verify registration success message header is displayed
    And verify registration success message is displayed
    When user closes registration success modal
    Then verify registration modal is closed

  @0245
  Scenario: Verify registration with special characters in name fields
    When user clicks on login link
    And user clicks on register link
    And user enters first name with special characters for registration
    And user enters last name with special characters for registration
    And user enters valid email for registration
    And user enters valid password for registration
    And user accepts the registration agreement
    And user clicks on registration button
    Then verify registration success message header is displayed
    And verify registration success message is displayed
    When user closes registration success modal
    Then verify registration modal is closed

  @0246
  Scenario: Verify registration form keyboard navigation
    When user clicks on login link
    And user clicks on register link
    And user navigates through registration form using tab key
    Then verify all registration form elements are accessible via keyboard
    And verify user can submit registration form using enter key
    And verify registration success message header is displayed
    And verify registration success message is displayed
    When user closes registration success modal
    Then verify registration modal is closed

  @0247
  Scenario: Verify registration modal can be closed and reopened
    When user clicks on login link
    And user clicks on register link
    Then verify registration modal header is displayed
    When user closes the registration modal
    Then verify registration modal is closed
    When user clicks on login link
    And user clicks on register link
    Then verify registration modal header is displayed

  @0248
  Scenario: Verify registration with whitespace-only input fields
    When user clicks on login link
    And user clicks on register link
    And user enters whitespace-only values in registration fields
    And user accepts the registration agreement
    And user clicks on registration button
    Then verify appropriate validation error messages are displayed for registration

  @0249
  Scenario: Verify registration agreement checkbox interaction
    When user clicks on login link
    And user clicks on register link
    Then verify registration agreement checkbox is unchecked by default
    When user clicks on registration agreement checkbox
    Then verify registration agreement checkbox is checked
    When user clicks on registration agreement checkbox again
    Then verify registration agreement checkbox is unchecked

 # @registration @0250
 # Scenario: Verify registration with password containing only numbers
 #   When user clicks on login link
 #   And user clicks on register link
 #   And user enters valid email for registration
 #   And user enters numeric-only password for registration
 #   And user accepts the registration agreement
 #   And user clicks on registration button
 #   Then verify password validation error message is displayed
#
 # @registration @0251
 # Scenario: Verify registration with password containing only letters
 #   When user clicks on login link
 #   And user clicks on register link
 #   And user enters valid email for registration
 #   And user enters letters-only password for registration
 #   And user accepts the registration agreement
 #   And user clicks on registration button
 #   Then verify password validation error message is displayed

  @0252 @smoke
  Scenario: Verify registration success modal elements
    When user clicks on login link
    And user clicks on register link
    And user enters valid email for registration
    And user enters valid password for registration
    And user accepts the registration agreement
    And user clicks on registration button
    Then verify registration success message header is displayed
    And verify registration success message is displayed
    And verify close registration success modal button is displayed

  @0253
  Scenario: Verify registration form auto-focus behavior
    When user clicks on login link
    And user clicks on register link
    Then verify first registration input field has focus
    When user enters data in first name field and tabs
    Then verify focus moves to last name field
    When user enters data in last name field and tabs
    Then verify focus moves to email field

  @0254
  Scenario: Verify registration with email containing plus sign
    When user clicks on login link
    And user clicks on register link
    And user enters email with plus sign for registration
    And user enters valid password for registration
    And user accepts the registration agreement
    And user clicks on registration button
    Then verify registration success message header is displayed
    And verify registration success message is displayed
    When user closes registration success modal
    Then verify registration modal is closed
