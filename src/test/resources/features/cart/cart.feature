@regression
Feature: Shopping Cart Tests

  Background: Open the home page
    Given that user is on the home page

  @cart1 @0001 @smoke
  Scenario: Verify user can add single product to cart from search results
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And verify cart counter shows "1"
    When user opens cart
    Then verify product is displayed in cart

  @cart @0002 @smoke
  Scenario: Verify user can add a single product from category page
    And navigate to "Huśtawki" subcategory from "Meble ogrodowe"
    And user opens the first product
    And user adds the product to cart
    Then verify cart counter shows "1"
    When user opens cart
    Then verify product is displayed in cart


  @cart @0003
  Scenario: Verify user can add multiple products to cart from search results
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user returns to the previous page
    And user opens the second product
    And user adds the product to cart
    Then verify cart counter shows "2"
    When user opens cart
    Then verify both products are displayed in cart

  @cart @0004
  Scenario: Verify user can add multiple products to cart from category page
    And navigate to "Huśtawki" subcategory from "Meble ogrodowe"
    And user opens the first product
    And user adds the product to cart
    And user returns to the previous page
    And user opens the second product
    And user adds the product to cart
    Then verify cart counter shows "2"
    When user opens cart
    Then verify both products are displayed in cart

  @cart @0005 @smoke
  Scenario: Verify user can update product quantity in cart
    When user searches for "Fotel"
    And user opens the first product from search results
    And user adds the product to cart
    And user opens cart
    And user changes quantity to "3" in cart
    Then verify total price is updated correctly to "3" times the product price
    And verify quantity shows "3" in cart
    And verify cart counter shows "3"

  @cart @0006 @smoke
  Scenario: Verify user can remove product from cart
    When user searches for "Fotel"
    And user opens the first product from search results
    And user adds the product to cart
    And user opens cart
    And user removes the product from cart
    Then verify cart is empty
    And verify cart counter shows "0"

  @cart @0007
  Scenario: Verify cart total calculation
    When user searches for "Fotel"
    And user opens the first product from search results
    And user adds the product to cart
    And user returns to the previous page
    And user opens the second product from search results
    And user adds the product to cart
    And user opens cart
    Then verify subtotal is calculated correctly
    And verify vat cost is displayed correctly


  @cart @0008
  Scenario: Verify cart persistence across sessions
    When user searches for "Fotel"
    And user opens the first product from search results
    And user adds the product to cart
    And user refreshes the page
    Then verify cart counter shows "1"
    When user opens cart
    Then verify product is still in cart

  @cart @0009
  Scenario: Verify empty cart state
    When user opens cart
    Then verify cart is empty
    And verify continue shopping button is displayed
    Then verify user can click continue shopping button

  @cart @0010
  Scenario: Verify cart product navigation
    When user searches for "Fotel"
    And user opens the first product from search results
    And user adds the product to cart
    And user opens cart
    And user clicks on product name in cart
    Then user should be on product detail page

  @cart @0011 @smoke
  Scenario: Verify user can add quantity of product in cart by clicking plus button
    When user searches for "Fotel"
    And user opens the first product from search results
    And user adds the product to cart
    And user opens cart
    When user clicks on plus button to increase quantity
    Then verify cart counter shows "2"
    And the quantity input shows "2"
    And the total is updated correctly

  @cart @0012
  Scenario: Verify user can reduce quantity of product in cart by clicking minus button
    When user searches for "Fotel"
    And user opens the first product from search results
    And user adds the product to cart
    And user opens cart
    And user changes quantity to "2" in cart
    And verify cart counter shows "2"
    And the quantity input shows "2"
    And the total is updated correctly
    And user clicks on minus button to decrease quantity
    Then verify cart counter shows "1"
    And the quantity input shows "1"
    And the total is updated correctly

  @cart @0013
  Scenario: Verify use is able to see an error message when trying to increase quantity of product in cart to more than available stock
    When user searches for "Fotel"
    And user opens the first product from search results
    And user adds the product to cart
    And user opens cart
    And user changes quantity to "99999999" in cart
    Then verify error message is displayed indicating insufficient stock
    And verify cart counter shows "1"
    And the total is updated correctly

  @cart @0014
  Scenario: Verify user can delete one product from cart and see the total price is updated correctly
    When user searches for "Fotel"
    And user opens the first product from search results
    And user adds the product to cart
    And user returns to the previous page
    And user opens the second product from search results
    And user adds the product to cart
    And user opens cart
    And user removes one product from cart
    Then verify remaining product is displayed in cart
    And verify total price is updated correctly for remaining product
    And verify cart counter shows "1"

  @cart @0015
  Scenario: Verify free shipping threshold is displayed in cart
    When user searches for product by code "47550"
    And user adds the product to cart
    And user opens cart
    Then verify free shipping threshold message is displayed and threshold amount is correct
    And verify remaining amount for free shipping is correct

  @cart @0016
  Scenario: Verify free deliver message is displayed in cart when free shipping threshold is met
    When user searches for product by code "46223"
    And user adds the product to cart
    And user opens cart
    Then verify free delivery message is displayed in cart

  @cart @0320
  Scenario: Verify free shipping threshold progress bar displays correct percentage and remaining amount
    When user searches for product by code "47550"
    And user adds the product to cart
    And user opens cart
    Then verify free shipping threshold progress bar shows correct percentage
    And verify remaining amount for free shipping is correct
    When user clicks on plus button to increase quantity
    Then verify free shipping threshold progress bar shows correct percentage
    And verify remaining amount for free shipping is correct
    When user changes quantity to random amount in cart
    Then verify free shipping threshold progress bar shows correct percentage
    And verify remaining amount for free shipping is correct
