@regression @account_page
Feature: User Account Tests - Part 2

  Background: User is logged in and cart is clean
    Given that user is on the home page
    And user logs in with account test credentials 2
    And remove product from cart if present

  @0104 @account_sequential_2
  Scenario: Verify user can attach JPG file in chat with the store
    Given cleanup any downloaded test files
    When user opens chat history
    And user types message and attaches "automation_jpg.jpg" file
    Then verify attached file name "automation_jpg.jpg" is displayed
    When user sends the message with attached file
    Then verify attached file appears in chat
    And verify attached file can be downloaded
    Then perform post-test cleanup

  @0105 @account_sequential_2
  Scenario: Verify user can attach DOCX file in chat with the store
    Given cleanup any downloaded test files
    When user opens chat history
    And user types message and attaches "automation_docx.docx" file
    Then verify attached file name "automation_docx.docx" is displayed
    When user sends the message with attached file
    Then verify attached file appears in chat
    And verify attached file can be downloaded
    Then perform post-test cleanup

  @0106 @account_sequential_2
  Scenario: Verify user cannot attach unsupported PNG file in chat with the store
    Given cleanup any downloaded test files
    When user opens chat history
    And user tries to attach unsupported "automation_png.png" file
    Then verify invalid file type error is displayed
    Then perform post-test cleanup

  @0212 @account_sequential_2
  Scenario: Verify error messages when trying to submit ticket request without selecting reason and without entering message
    When go to my account page
    And click on contact with the store link
    And click on create a ticket link
    And user attempts to submit ticket request without selecting reason and without entering message
    Then verify empty message field error is displayed
    And verify reason for contact not selected error is displayed

  @0257 @account_sequential_2
  Scenario: Verify user can save company billing address without phone number
    When user clicks add new billing address
    And user selects company billing address option
    And user enters company billing address data without phone number
    And user saves the new address
    Then verify company billing address can be saved without phone number
    And user deletes the newly added billing address


  @0258 @account_sequential_2
  Scenario: Verify user can save company billing address with phone number
    When user clicks add new billing address
    And user selects company billing address option
    And user enters company billing address data with phone number
    And user saves the new address
    Then verify company billing address can be saved with phone number
    And user deletes the newly added billing address

  @0259 @account_sequential_2
  Scenario: Verify user can save personal billing address without phone number
    When user clicks add new billing address
    And user selects individual billing address option
    And user enters personal billing address data without phone number
    And user saves the new address
    Then verify personal billing address can be saved without phone number
    And user deletes the newly added billing address

  @0260 @account_sequential_2
  Scenario: Verify user can save personal billing address with phone number
    When user clicks add new billing address
    And user selects individual billing address option
    And user enters personal billing address data with phone number
    And user saves the new address
    Then verify personal billing address can be saved with phone number
    And user deletes the newly added billing address

  @0261 @account_sequential_2
  Scenario: Verify shipping address can be saved after fixing name field error
    When user clicks add new shipping address
    And user enters shipping address data without name
    And user saves the new address
    Then required field error should be displayed for name
    When verify shipping address can be saved after fixing errors
    And user deletes the newly added shipping address

  @0262 @account_sequential_2
  Scenario: Verify shipping address can be saved after fixing last name field error
    When user clicks add new shipping address
    And user enters shipping address data without last name
    And user saves the new address
    Then required field error should be displayed for last name
    When verify shipping address can be saved after fixing errors
    And user deletes the newly added shipping address

  @0263 @account_sequential_2
  Scenario: Verify shipping address can be saved after fixing address field error
    When user clicks add new shipping address
    And user enters shipping address data without address
    And user saves the new address
    Then required field error should be displayed for address
    When verify shipping address can be saved after fixing errors
    And user deletes the newly added shipping address

  @0264 @account_sequential_2
  Scenario: Verify shipping address can be saved after fixing post code field error
    When user clicks add new shipping address
    And user enters shipping address data without post code
    And user saves the new address
    Then required field error should be displayed for post code
    When verify shipping address can be saved after fixing errors
    And user deletes the newly added shipping address

  @0265 @account_sequential_2
  Scenario: Verify shipping address can be saved after fixing city field error
    When user clicks add new shipping address
    And user enters shipping address data without city
    And user saves the new address
    Then required field error should be displayed for city
    When verify shipping address can be saved after fixing errors
    And user deletes the newly added shipping address

  @0266 @account_sequential_2
  Scenario: Verify shipping address can be saved after fixing phone number field error
    When user clicks add new shipping address
    And user enters shipping address data without phone number
    And user saves the new address
    Then required field error should be displayed for phone number
    When verify shipping address can be saved after fixing errors
    And user deletes the newly added shipping address

  @0267 @account_sequential_2
  Scenario: Verify personal billing address can be saved after fixing name field error
    When user clicks add new billing address
    And user selects individual billing address option
    And user enters billing address data without name
    And user saves the new address
    Then required field error should be displayed for name
    When verify personal billing address can be saved after fixing errors
    And user deletes the newly added billing address

  @0268 @account_sequential_2
  Scenario: Verify personal billing address can be saved after fixing last name field error
    When user clicks add new billing address
    And user selects individual billing address option
    And user enters billing address data without last name
    And user saves the new address
    Then required field error should be displayed for last name
    When verify personal billing address can be saved after fixing errors
    And user deletes the newly added billing address

  @0269 @account_sequential_2
  Scenario: Verify personal billing address can be saved after fixing address field error
    When user clicks add new billing address
    And user selects individual billing address option
    And user enters billing address data without address
    And user saves the new address
    Then required field error should be displayed for address
    When verify personal billing address can be saved after fixing errors
    And user deletes the newly added billing address

  @0270 @account_sequential_2
  Scenario: Verify personal billing address can be saved after fixing post code field error
    When user clicks add new billing address
    And user selects individual billing address option
    And user enters billing address data without post code
    And user saves the new address
    Then required field error should be displayed for post code
    When verify personal billing address can be saved after fixing errors
    And user deletes the newly added billing address

  @0271 @account_sequential_2
  Scenario: Verify personal billing address can be saved after fixing city field error
    When user clicks add new billing address
    And user selects individual billing address option
    And user enters billing address data without city
    And user saves the new address
    Then required field error should be displayed for city
    When verify personal billing address can be saved after fixing errors
    And user deletes the newly added billing address

  @0272 @account_sequential_2
  Scenario: Verify company billing address can be saved after fixing company name field error
    When user clicks add new billing address
    And user selects company billing address option
    And user enters company billing address data without company name
    And user saves the new address
    Then required field error should be displayed for company name
    When verify company billing address can be saved after fixing errors
    And user deletes the newly added billing address

 #@0273
 #Scenario: Verify company billing address can be saved after fixing NIP field error
 #  When user clicks add new billing address
 #  And user selects company billing address option
 #  And user enters company billing address data without NIP
 #  And user saves the new address
 #  Then required field error should be displayed for NIP
 #  When verify company billing address can be saved after fixing errors
 #  And user deletes the newly added billing address

  @0274 @account_sequential_2
  Scenario: Verify company billing address can be saved after fixing address field error
    When user clicks add new billing address
    And user selects company billing address option
    And user enters company billing address data without address
    And user saves the new address
    Then required field error should be displayed for address
    When verify company billing address can be saved after fixing errors
    And user deletes the newly added billing address

  @0275 @account_sequential_2
  Scenario: Verify company billing address can be saved after fixing post code field error
    When user clicks add new billing address
    And user selects company billing address option
    And user enters company billing address data without post code
    And user saves the new address
    Then required field error should be displayed for post code
    When verify company billing address can be saved after fixing errors
    And user deletes the newly added billing address

  @0276 @account_sequential_2
  Scenario: Verify company billing address can be saved after fixing city field error
    When user clicks add new billing address
    And user selects company billing address option
    And user enters company billing address data without city
    And user saves the new address
    Then required field error should be displayed for city
    When verify company billing address can be saved after fixing errors
    And user deletes the newly added billing address

  @0277 @account_sequential_2
  Scenario: Verify error is shown when entering invalid phone number prefix for shipping address
    When user clicks add new shipping address
    And user enters shipping address data with invalid phone number prefix
    And user saves the new address
    Then verify invalid phone number error is displayed for shipping address

  @0278 @account_sequential_2
  Scenario: Verify error is shown when entering invalid phone number prefix for personal billing address
    When user clicks add new billing address
    And user selects individual billing address option
    And user enters personal billing address data with invalid phone number prefix
    And user saves the new address
    Then verify invalid phone number error is displayed for billing address
  @account_sequential_2
  @0279 @account_sequential_2
  Scenario: Verify error is shown when entering invalid phone number prefix for company billing address
    When user clicks add new billing address
    And user selects company billing address option
    And user enters company billing address data with invalid phone number prefix
    And user saves the new address
    Then verify invalid phone number error is displayed for billing address
