@regression @account_page
Feature: User Account Tests - Part 1

  Background: User is logged in and cart is clean
    Given that user is on the home page
    And user logs in with account test credentials
    And remove product from cart if present

  @0080 @smoke @account_sequential_1
  Scenario: Verify user can logout
    When user logs out
    Then verify user is logged out

  @0081 @smoke @account_sequential_1
  Scenario: Verify user can login, logout and login with another credential
    When user logs out
    And user logs in with alternative credentials
    And user navigates to account details
    Then email value should be "<EMAIL>"
    Then verify user is logged in

  @0082 @smoke @account_sequential_1
  Scenario: Verify cart status is persistent across sessions
    When user adds product to cart
    And user logs out
    And user logs in with account test credentials
    And user opens cart
    Then verify product is still in cart
    And remove product from cart if present

  @0083 @smoke @account_sequential_1
  Scenario: Verify user can change details
    When user logs out
    And user logs in with alternative credentials
    And user navigates to account details
    And user changes name to "<PERSON><PERSON><PERSON>" and last name to "<PERSON><PERSON><PERSON>"
    And user saves the changes
    And user navigates to account details
    Then user profile header should contain "Profil"
    And name and last name header should contain "<PERSON><PERSON><PERSON> i nazwisko"
    And name and last name value should contain "<RANDOM_FULL_NAME>"
    And password header should contain "Hasło"
    And password value should contain "****"

  @0084 @account_sequential_1
  Scenario: Verify user can edit billing address
    When user navigates to billing address
    And user edits billing address with valid data
    And user saves the changes
    Then verify billing address is updated

  @0085 @account_sequential_1
  Scenario: Verify user can edit shipping address
    When user navigates to shipping address
    And user edits shipping address with valid data
    And user saves the changes
    Then verify shipping address is updated

  @0086 @account_sequential_1
  Scenario: Verify user can add new shipping address
    When user clicks add new shipping address
    And user enters valid shipping address data
    And user saves the new address
    Then verify new shipping address is added
    And user deletes the newly added shipping address

  @0087 @account_sequential_1
  Scenario: Verify user can add new billing address for company
    When user clicks add new billing address
    And user selects company billing address option
    And user enters valid company billing address data
    And user saves the new address
    Then verify new company billing address is added
    And user deletes the newly added billing address

  @0088 @account_sequential_1
  Scenario: Verify user can add new billing address for individual
    When user clicks add new billing address
    And user selects individual billing address option
    And user enters valid individual billing address data
    And user saves the new address
    Then verify new individual billing address is added
    And user deletes the newly added billing address

  @0089 @account_sequential_1
  Scenario: Verify user cannot save shipping address without phone number
    When user clicks add new shipping address
    And user enters shipping address data without phone number
    And user saves the new address
    Then required field error should be displayed for phone number

  @0090 @account_sequential_1
  Scenario: Verify user cannot save shipping address without name
    When user clicks add new shipping address
    And user enters shipping address data without name
    And user saves the new address
    Then required field error should be displayed for name

  @0091 @account_sequential_1
  Scenario: Verify user cannot save shipping address without last name
    When user clicks add new shipping address
    And user enters shipping address data without last name
    And user saves the new address
    Then required field error should be displayed for last name

  @0092 @account_sequential_1
  Scenario: Verify user cannot save shipping address without address
    When user clicks add new shipping address
    And user enters shipping address data without address
    And user saves the new address
    Then required field error should be displayed for address

  @0093 @account_sequential_1
  Scenario: Verify user cannot save shipping address without city
    When user clicks add new shipping address
    And user enters shipping address data without city
    And user saves the new address
    Then required field error should be displayed for city

  @0094 @account_sequential_1
  Scenario: Verify user cannot save shipping address without post code
    When user clicks add new shipping address
    And user enters shipping address data without post code
    And user saves the new address
    Then required field error should be displayed for post code

  @0095 @account_sequential_1
  Scenario: Verify user cannot save billing address without name
    When user clicks add new billing address
    And user selects individual billing address option
    And user enters billing address data without name
    And user saves the new address
    Then required field error should be displayed for name

  @0096 @account_sequential_1
  Scenario: Verify user cannot save billing address without last name
    When user clicks add new billing address
    And user selects individual billing address option
    And user enters billing address data without last name
    And user saves the new address
    Then required field error should be displayed for last name

  @0097 @account_sequential_1
  Scenario: Verify user cannot save billing address without address
    When user clicks add new billing address
    And user selects individual billing address option
    And user enters billing address data without address
    And user saves the new address
    Then required field error should be displayed for address

  @0098 @account_sequential_1
  Scenario: Verify user cannot save billing address without city
    When user clicks add new billing address
    And user selects individual billing address option
    And user enters billing address data without city
    And user saves the new address
    Then required field error should be displayed for city

  @0099 @account_sequential_1
  Scenario: Verify user cannot save billing address without post code
    When user clicks add new billing address
    And user selects individual billing address option
    And user enters billing address data without post code
    And user saves the new address
    Then required field error should be displayed for post code

  @0100 @account_sequential_1
  Scenario: Verify user can chat to the store about ordered product
    When user opens chat history
    And user types random automation test message in chat
    Then verify correct user name is displayed in chat
    And verify date and time is displayed in chat
    And verify the message has been sent in chat
    And verify message received header is displayed

  @0101 @account_sequential_1
  Scenario: Verify user can check and uncheck the newsletter consent checkbox
    When user navigates to account details
    Then verify newsletter agreement checkbox label is displayed correctly
    When user checks the newsletter agreement checkbox
    Then verify newsletter agreement checkbox is checked
    When user unchecks the newsletter agreement checkbox
    Then verify newsletter agreement checkbox is unchecked

  @0102 @account_sequential_1
  Scenario: Verify coupon code persistence after logout and login
    When user searches for "Fotel"
    And user opens the first product from search results
    And user adds the product to cart
    And user opens cart
    And user applies discount code "automation"
    Then verify discount is applied correctly and total price is updated with discount applied
    When user logs out
    And user logs in with account test credentials
    And user opens cart
    Then verify coupon code is still applied and cart is preserved after login

  @0103 @account_sequential_1
  Scenario: Verify user can attach PDF file in chat with the store
    Given cleanup any downloaded test files
    When user opens chat history
    And user types message and attaches "automation_pdf.pdf" file
    Then verify attached file name "automation_pdf.pdf" is displayed
    When user sends the message with attached file
    Then verify attached file appears in chat
    And verify attached file can be downloaded
    Then perform post-test cleanup

