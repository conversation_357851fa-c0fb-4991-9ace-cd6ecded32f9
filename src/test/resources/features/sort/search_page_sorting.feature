@regression
Feature: Search Page Sorting Tests

  Background: Search for products
    Given that user is on the home page
    When user searches for "<PERSON><PERSON><PERSON><PERSON><PERSON>"
    Then user should see the search results page

  @sort @0124
  Scenario: Verify user can apply recommended sort on search page
    When user applies recommended sort
    Then verify recommended sort is applied

  @sort @0125 @smoke
  Scenario: Verify user can apply price low to high sort on search page
    When user applies price low to high sort
    And user loads all available products after sorting
    Then verify price low to high sort is applied
    And verify URL contains price ascending parameter
    And verify products are sorted by price in ascending order

  @sort @0126
  Scenario: Verify user can apply price high to low sort on search page
    When user applies price high to low sort
    And user loads all available products after sorting
    Then verify price high to low sort is applied
    And verify URL contains price descending parameter
    And verify products are sorted by price in descending order

  @sort @0127
  Scenario: Verify user can apply name A to Z sort on search page
    When user applies name A to Z sort
    And user loads all available products after sorting
    Then verify name A to Z sort is applied
    And verify URL contains name ascending parameter
    And verify products are sorted by name in ascending order

  @sort @0128
  Scenario: Verify user can apply name Z to A sort on search page
    When user applies name Z to A sort
    And user loads all available products after sorting
    Then verify name Z to A sort is applied
    And verify URL contains name descending parameter
    And verify products are sorted by name in descending order

  @sort @0129
  Scenario: Verify price low to high sort persists when page is refreshed
    When user applies price low to high sort
    Then verify price low to high sort is applied
    When user refreshes the page
    Then verify price low to high sort is applied
    And verify URL contains price ascending parameter

  @sort @0130
  Scenario: Verify price high to low sort persists when page is refreshed
    When user applies price high to low sort
    Then verify price high to low sort is applied
    When user refreshes the page
    Then verify price high to low sort is applied
    And verify URL contains price descending parameter

  @sort @0140
  Scenario: Verify name A to Z sort persists when page is refreshed
    When user applies name A to Z sort
    Then verify name A to Z sort is applied
    When user refreshes the page
    Then verify name A to Z sort is applied
    And verify URL contains name ascending parameter

  @sort @0150
  Scenario: Verify name Z to A sort persists when page is refreshed
    When user applies name Z to A sort
    Then verify name Z to A sort is applied
    When user refreshes the page
    Then verify name Z to A sort is applied
    And verify URL contains name descending parameter

  @sort @0151
  Scenario: Verify sort persists after opening product and navigating back
    When user applies price low to high sort
    Then verify price low to high sort is applied
    And user opens the first product
    When user navigates back to the previous page
    Then verify price low to high sort is applied
    And verify URL contains price ascending parameter

  @sort @0152
  Scenario: Verify sort persists after adding product to cart and returning
    When user applies price high to low sort
    Then verify price high to low sort is applied
    And user opens the first product
    And user adds the product to cart
    And open cart and proceed to checkout
    When user navigates back to the previous page
    And user navigates back to the previous page
    And user navigates back to the previous page
    Then verify price high to low sort is applied
    And verify URL contains price descending parameter

  @sort @0153
  Scenario: Verify default recommended sort is applied on search page load
    # This scenario verifies that Polecane (recommended) sort is applied by default
    Then verify recommended sort is applied

  @sort @0154
  Scenario: Verify sort dropdown is accessible and functional
    When user applies price low to high sort
    Then verify price low to high sort is applied
    When user applies name A to Z sort
    Then verify name A to Z sort is applied
    When user applies recommended sort
    Then verify recommended sort is applied

  @sort @0155
  Scenario: Verify sort works with different search terms
    When user applies price low to high sort
    Then verify price low to high sort is applied
    When user searches for "Fotel"
    When user applies price low to high sort
    Then verify price low to high sort is applied
    And verify URL contains price ascending parameter

  @sort @0156
  Scenario: Verify sort functionality with no search results
    When user searches for "NonExistingProduct"
    And user should see the search results page
    # Sort dropdown should not be visible when there are no results
    Then verify user should see that the header shows no results found for "NonExistingProduct"

  @sort @0157
  Scenario: Verify sort maintains state across multiple sort changes
    When user applies price low to high sort
    Then verify price low to high sort is applied
    When user applies name Z to A sort
    Then verify name Z to A sort is applied
    When user applies price high to low sort
    Then verify price high to low sort is applied
    And verify URL contains price descending parameter

  @sort @0158
  Scenario: Verify sort works correctly with products having discounted prices
    When user applies price low to high sort
    Then verify price low to high sort is applied
    And verify products are sorted by price in ascending order

  @sort @0159
  Scenario: Verify sort functionality with Polish characters in product names
    When user applies name A to Z sort
    Then verify name A to Z sort is applied
    And verify products are sorted by name in ascending order
