@regression
Feature: Category Page Sorting Tests

  Background: Navigate to category page with all products
    Given that user is on the home page
    And navigate to "<PERSON>ka<PERSON> wszystko" subcategory from "Meble ogrodowe"

  @sort @0107
  Scenario: Verify user can apply recommended sort on category page
    When user applies recommended sort
    Then verify recommended sort is applied

  @sort @0108 @smoke
  Scenario: Verify user can apply price low to high sort on category page
    When user applies price low to high sort
    And user loads all available products after sorting
    Then verify price low to high sort is applied
    And verify URL contains price ascending parameter
    And verify products are sorted by price in ascending order

  @sort @0109
  Scenario: Verify user can apply price high to low sort on category page
    When user applies price high to low sort
    And user loads all available products after sorting
    Then verify price high to low sort is applied
    And verify URL contains price descending parameter
    And verify products are sorted by price in descending order

  @sort @0110
  Scenario: Verify user can apply name A to Z sort on category page
    When user applies name A to Z sort
    And user loads all available products after sorting
    Then verify name A to Z sort is applied
    And verify URL contains name ascending parameter
    And verify products are sorted by name in ascending order

  @sort @0111
  Scenario: Verify user can apply name Z to A sort on category page
    When user applies name Z to A sort
    And user loads all available products after sorting
    Then verify name Z to A sort is applied
    And verify URL contains name descending parameter
    And verify products are sorted by name in descending order

  @sort @0112
  Scenario: Verify price low to high sort persists when page is refreshed
    When user applies price low to high sort
    Then verify price low to high sort is applied
    When user refreshes the page
    Then verify price low to high sort is applied
    And verify URL contains price ascending parameter

  @sort @0113
  Scenario: Verify price high to low sort persists when page is refreshed
    When user applies price high to low sort
    Then verify price high to low sort is applied
    When user refreshes the page
    Then verify price high to low sort is applied
    And verify URL contains price descending parameter

  @sort @0114
  Scenario: Verify name A to Z sort persists when page is refreshed
    When user applies name A to Z sort
    Then verify name A to Z sort is applied
    When user refreshes the page
    Then verify name A to Z sort is applied
    And verify URL contains name ascending parameter

  @sort @0115
  Scenario: Verify name Z to A sort persists when page is refreshed
    When user applies name Z to A sort
    Then verify name Z to A sort is applied
    When user refreshes the page
    Then verify name Z to A sort is applied
    And verify URL contains name descending parameter

  @sort @0116
  Scenario: Verify sort persists after opening product and navigating back
    When user applies price low to high sort
    Then verify price low to high sort is applied
    And user opens the first product from category page
    When user navigates back to the previous page
    Then verify price low to high sort is applied
    And verify URL contains price ascending parameter

  @sort @0117
  Scenario: Verify sort persists after adding product to cart and returning
    When user applies price high to low sort
    Then verify price high to low sort is applied
    And user opens the first product from category page
    And user adds the product to cart
    And open cart and proceed to checkout
    When user navigates back to the previous page
    And user navigates back to the previous page
    And user navigates back to the previous page
    Then verify price high to low sort is applied
    And verify URL contains price descending parameter

  @sort @0118
  Scenario: Verify default recommended sort is applied on category page load
    # This scenario verifies that Polecane (recommended) sort is applied by default
    Then verify recommended sort is applied

  @sort @0119
  Scenario: Verify sort dropdown is accessible and functional
    When user applies price low to high sort
    Then verify price low to high sort is applied
    When user applies name A to Z sort
    Then verify name A to Z sort is applied
    When user applies recommended sort
    Then verify recommended sort is applied

  @sort @0120
  Scenario: Verify sort maintains state across multiple sort changes
    When user applies price low to high sort
    Then verify price low to high sort is applied
    When user applies name Z to A sort
    Then verify name Z to A sort is applied
    When user applies price high to low sort
    Then verify price high to low sort is applied
    And verify URL contains price descending parameter

  @sort @0121
  Scenario: Verify sort works correctly with products having discounted prices
    When user applies price low to high sort
    Then verify price low to high sort is applied
    And verify products are sorted by price in ascending order

  @sort @0122
  Scenario: Verify sort functionality with Polish characters in product names
    When user applies name A to Z sort
    Then verify name A to Z sort is applied
    And verify products are sorted by name in ascending order

  @sort @0123
  Scenario: Verify sort works after loading more products
    When user applies price low to high sort
    Then verify price low to high sort is applied
    And verify products are sorted by price in ascending order
