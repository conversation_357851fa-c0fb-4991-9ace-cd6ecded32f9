@regression
Feature: Guest Order Tests

  Background: Open the home page
    Given that user is on the home page

  @order @0035 @smoke
  Scenario: Verify successful order placement with single product as a guest
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills recipient information as "person"
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @order @0036 @smoke
  Scenario: Verify order placement with single product as a guest with company name
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills recipient information as "company"
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @order @0037
  Scenario: Verify successful order placement with multiple products as a guest
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user returns to the previous page
    And user opens the second product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills recipient information as "person"
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Online transfer"
    Then verify order confirmation page is displayed

  @order @0038
  Scenario: Verify order placement with multiple products as a guest with company name
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user returns to the previous page
    And user opens the second product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills recipient information as "company"
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Card"
    Then verify order confirmation page is displayed

  @order @0039 @smoke
  Scenario: Verify order placement with discount code
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And user applies discount code "automation"
    And open cart and proceed to checkout
    Then verify discount is applied correctly and total price is updated with discount applied
    And user fills recipient information as "person"
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Card"
    Then verify order confirmation page is displayed

  @order @0040 @smoke
  Scenario Outline: Verify order placement with different payment methods
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills recipient information as "person"
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "<paymentMethod>"
    Then verify order confirmation page is displayed
    Examples:
      | paymentMethod   |
      | Blik            |
      | Card            |
      | Online transfer |

  @order @0041
  Scenario Outline: Verify order placement with different delivery methods
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills recipient information as "person"
    And click on continue button
    And user selects delivery method "<deliveryMethod>"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed
    Examples:
      | deliveryMethod   |
      | Kurier           |


  @order @0042
  Scenario: Verify order cancellation before payment
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills recipient information as "person"
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user selects payment method "Online transfer"
    And user selects online bank payment option
    And navigate back to checkout page
    Then verify order is not created
    And verify cart contents are preserved

  @order @0043
  Scenario: Verify order with free shipping threshold
    When user searches for product by code "46223"
    And user adds the product to cart
    And user opens cart
    Then verify free delivery message is displayed in cart
    And open cart and proceed to checkout
    And verify the delivery cost is zero at checkout
    And user fills recipient information as "person"
    And click on continue button
    And user selects delivery method "Kurier"
    And verify the delivery cost is zero on the right side of "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @order @0044
  Scenario: Verify order modification before payment
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills recipient information as "person"
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user selects payment method "Online transfer"
    And user selects online bank payment option
    And navigate back to checkout page
    And user opens cart
    And user changes quantity to "2" in cart
    And open cart and proceed to checkout
    And verify total price is updated correctly after modifying quantity
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Online transfer"
    Then verify order confirmation page is displayed

  @order @0045
  Scenario: Verify order with different billing and shipping addresses - billing as company
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills recipient information as "person"
    And click on the same as delivery checkbox
    And user fills billing information as "company"
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @order @0046
  Scenario: Verify order with different billing and shipping addresses - billing as person
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills recipient information as "person"
    And click on the same as delivery checkbox
    And user fills billing information as "person"
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @order @0047
  Scenario: Verify user can use different billing and change it back to the same as shipping and order successfully
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills recipient information as "person"
    And click on the same as delivery checkbox
    And user fills billing information as "company"
    And click on continue button
    And click on change order details button
    And click on the same as delivery checkbox
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @order @0048
  Scenario: Verify order failure due to invalid blik code
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills recipient information as "person"
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user enters invalid blik code
    And verify error message is displayed indicating invalid blik code
    Then verify order is not created

  @order @0049
  Scenario: Verify order failure due to invalid card number
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills recipient information as "person"
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user enters invalid card number
    And verify error message is displayed indicating invalid card number
    Then verify order is not created