@regression @logged_in_order
Feature: Logged In User Order Tests - 2

  Background: Open the home page and log in with second account
    Given that user is on the home page
    And user logs in with order logged in credentials 2
    And remove product from cart if present

  #@0065 @order_logged_in_sequential_2
  #Scenario: Verify order with new billing address as a company and logged in user
  #  When user searches for "Fotel"
  #  And user opens the first product
  #  And user adds the product to cart
  #  And user opens cart
  #  And open cart and proceed to checkout
  #  And select one saved shipping address from the list
  #  And uncheck the same as delivery checkbox
  #  And select one saved billing address from the list as a "company"
  #  And click on add new billing address button
  #  And user adds new billing address as "company"
  #  And click on continue button
  #  And user selects delivery method "Kurier"
  #  And verify the shipping and billing addresses are different
  #  And click on continue button
  #  And user agrees to terms and conditions
  #  And user chooses to pay with "Blik"
  #  Then verify order confirmation page is displayed

  #@0066 @order_logged_in_sequential_2 @remove_newly_added_address
  #Scenario: Verify logged in user can place an order with new billing address with a different country as person
  #  When user searches for "Fotel"
  #  And user opens the first product
  #  And user adds the product to cart
  #  And user opens cart
  #  And open cart and proceed to checkout
  #  And select one saved shipping address from the list
  #  And uncheck the same as delivery checkbox
  #  And select one saved billing address from the list as a "person"
  #  And click on add new billing address button
  #  And user adds new billing address as "person" with a different country
  #  And click on continue button
  #  And verify the shipping and billing addresses are different
  #  And user selects delivery method "Kurier"
  #  And click on continue button
  #  And user agrees to terms and conditions
  #  And user chooses to pay with "Blik"
  #  Then verify order confirmation page is displayed
#
  #@0067 @order_logged_in_sequential_2 @remove_newly_added_address
  #Scenario: Verify logged in user can place an order with new billing address with a different country as company
  #  When user searches for "Fotel"
  #  And user opens the first product
  #  And user adds the product to cart
  #  And user opens cart
  #  And open cart and proceed to checkout
  #  And select one saved shipping address from the list
  #  And uncheck the same as delivery checkbox
  #  And select one saved billing address from the list as a "company"
  #  And click on add new billing address button
  #  And user adds new billing address as "company" with a different country
  #  And click on continue button
  #  And verify the shipping and billing addresses are different
  #  And user selects delivery method "Kurier"
  #  And click on continue button
  #  And user agrees to terms and conditions
  #  And user chooses to pay with "Blik"
  #  Then verify order confirmation page is displayed

  ###Order History Start

  @0068 @order_logged_in_sequential_2
  Scenario: Verify user can see the ordered product in order history - same as delivery
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And select one saved shipping address from the list
    And check the same as delivery checkbox
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed
    And go to my account page
    When navigate to order history
    Then verify the recent order is displayed in order history
    And verify order details match the placed order

  @0069 @order_logged_in_sequential_2
  Scenario: Verify user can see the ordered product in order history - different billing address as company
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And select one saved shipping address from the list
    And uncheck the same as delivery checkbox
    And select one saved billing address from the list as a "company"
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed
    And go to my account page
    When navigate to order history
    Then verify the recent order is displayed in order history
    And verify order details match the placed order

  @0070 @order_logged_in_sequential_2
  Scenario: Verify user can see the ordered product in order history - different billing address as person
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And select one saved shipping address from the list
    And uncheck the same as delivery checkbox
    And select one saved billing address from the list as a "person"
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed
    And go to my account page
    When navigate to order history
    Then verify the recent order is displayed in order history
    And verify order details match the placed order

  @0071 @order_logged_in_sequential_2
  Scenario: Verify user can see the ordered product in order history - enter new shipping address
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And click on add new shipping address button
    And user adds new shipping address as "person"
    And check the same as delivery checkbox
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed
    And go to my account page
    When navigate to order history
    Then verify the recent order is displayed in order history
    And verify order details match the placed order

  @0072 @order_logged_in_sequential_2
  Scenario: Verify user can see the ordered product in order history - enter new billing address as person
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And select one saved shipping address from the list
    And uncheck the same as delivery checkbox
    And click on add new billing address button
    And user adds new billing address as "person"
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed
    And go to my account page
    When navigate to order history
    Then verify the recent order is displayed in order history
    And verify order details match the placed order

  #@0073 @order_logged_in_sequential_2
  #Scenario: Verify user can see the ordered product in order history - enter new billing address as company
  #  When user searches for "Fotel"
  #  And user opens the first product
  #  And user adds the product to cart
  #  And user opens cart
  #  And open cart and proceed to checkout
  #  And select one saved shipping address from the list
  #  And uncheck the same as delivery checkbox
  #  And click on add new billing address button
  #  And user adds new billing address as "company"
  #  And click on continue button
  #  And user selects delivery method "Kurier"
  #  And click on continue button
  #  And user agrees to terms and conditions
  #  And user chooses to pay with "Blik"
  #  Then verify order confirmation page is displayed
  #  And go to my account page
  #  When navigate to order history
  #  Then verify the recent order is displayed in order history
  #  And verify order details match the placed order

  @0074 @order_logged_in_sequential_2
  Scenario: Verify user can see the ordered product in order history - enter new shipping and billing address as person
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And click on add new shipping address button
    And user adds new shipping address as "person"
    And uncheck the same as delivery checkbox
    And click on add new billing address button
    And user adds new billing address as "person"
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed
    And go to my account page
    When navigate to order history
    Then verify the recent order is displayed in order history
    And verify order details match the placed order

    #Comment out for now - on account page company name is not displayed when shipping address contains company name
  #@logged_in_order @0075 @order_logged_in_sequential_2
  #Scenario: Verify user can see the ordered product in order history - enter new shipping and billing address as company
  #  When user searches for "Fotel"
  #  And user opens the first product
  #  And user adds the product to cart
  #  And user opens cart
  #  And open cart and proceed to checkout
  #  And click on add new shipping address button
  #  And user adds new shipping address as "company"
  #  And uncheck the same as delivery checkbox
  #  And click on add new billing address button
  #  And user adds new billing address as "company"
  #  And click on continue button
  #  And user selects delivery method "Kurier"
  #  And click on continue button
  #  And user agrees to terms and conditions
  #  And user chooses to pay with "Blik"
  #  Then verify order confirmation page is displayed
  #  And go to my account page
  #  When navigate to order history
  #  Then verify the recent order is displayed in order history
  #  And verify order details match the placed order

    ###Order History End

  @0076 @order_logged_in_sequential_2
  Scenario: Verify order with free shipping threshold as a logged in user
    When user searches for product by code "46223"
    And user adds the product to cart
    And user opens cart
    Then verify free delivery message is displayed in cart
    And open cart and proceed to checkout
    And verify the delivery cost is zero at checkout
    And click on continue button
    And user selects delivery method "Kurier"
    And verify the delivery cost is zero on the right side of "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @0077 @order_logged_in_sequential_2
  Scenario: Verify logged in user an order with coupon code
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And user applies discount code "automation"
    And open cart and proceed to checkout
    Then verify discount is applied correctly and total price is updated with discount applied
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @0078 @order_logged_in_sequential_2
  Scenario: Verify order cancellation before payment as a logged in user
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user selects payment method "Online transfer"
    And user selects online bank payment option
    And navigate back to checkout page
    Then verify order is not created
    And verify cart contents are preserved
    Then go to my account page
    And user logs out
    And verify user is logged out
    And user logs in with order logged in credentials 2
    And verify cart contents are preserved
    And open cart and proceed to checkout
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @0079 @order_logged_in_sequential_2
  Scenario: Verify user can create a ticket request for the latest order made
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed
    And go to my account page
    And click on contact with the store link
    And click on create a ticket link
    And click on reason for contact dropdown
    And select a random reason for contact
    And click on order to create a ticket dropdown
    And verify the first order matches the recently created order
    And select the first order from dropdown
    And enter ticket message "Automation test"
    And click on submit request button
    Then verify request submitted text contains "Wysłano"
    And click on contact with the store link
    And user refreshes the page
    Then verify ticket created date is current date
    And verify ticket header contains order information
    And verify ticket last message header is "Ostatnia wiadomość"
    And verify ticket last message is "Automation test"
