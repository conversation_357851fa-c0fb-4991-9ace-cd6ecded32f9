@regression @country_and_language
Feature: Country and Language Selection Tests

  Background: User is on the home page
    Given that user is on the home page

  @0280
  Scenario: Verify user can open country selection modal
    When user opens country selection modal
    Then verify country selection modal is open
    And verify country selection modal header
    And verify country selection modal description

  @0283
  Scenario: Verify language dropdown is enabled after country selection
    When user opens country selection modal
    And user selects "Austria" country
    Then verify language dropdown is enabled

  @0284
  Scenario: Verify Poland country selection with Polish language
    When user opens country selection modal
    And user selects "Poland" country
    And verify language dropdown is enabled
    And user selects "polski" language
    And user clicks save and go to shop
    Then verify country name "Polska" is displayed in header
    And verify URL contains country code "PL" and language code "pl"
    And verify footer displays "Polska • PLN • polski"

  @0283
  Scenario: Verify Poland country selection with English language
    When user opens country selection modal
    And user selects "Poland" country
    And verify language dropdown is enabled
    And user selects "angielski" language
    And user clicks save and go to shop
    Then verify country name "Poland" is displayed in header
    And verify URL contains country code "PL" and language code "en"
    And verify footer displays "Poland • PLN • English"

  @0284
  Scenario: Verify Austria country selection with German language
    When user opens country selection modal
    And user selects "Austria" country
    And verify language dropdown is enabled
    And user selects "niemiecki" language
    And user clicks save and go to shop
    Then verify country name "Österreich" is displayed in header
    And verify URL contains country code "AT" and language code "de"
    And verify footer displays "Österreich • EUR • Deutsch"

  @0285
  Scenario: Verify Deutschland country selection with German language
    When user opens country selection modal
    And user selects "Deutschland" country
    And verify language dropdown is enabled
    And user selects "niemiecki" language
    And user clicks save and go to shop
    Then verify country name "Deutschland" is displayed in header
    And verify URL contains country code "DE" and language code "de"
    And verify footer displays "Deutschland • EUR • Deutsch"

  @0286
  Scenario: Verify Belgium country selection with English language
    When user opens country selection modal
    And user selects "Belgium" country
    And verify language dropdown is enabled
    And user selects "angielski" language
    And user clicks save and go to shop
    Then verify country name "Belgium" is displayed in header
    And verify URL contains country code "BE" and language code "en"
    And verify footer displays "Belgium • EUR • English"

  @0287
  Scenario: Verify Bulgaria country selection with English language
    When user opens country selection modal
    And user selects "Bulgaria" country
    And verify language dropdown is enabled
    And user selects "angielski" language
    And user clicks save and go to shop
    Then verify country name "Bulgaria" is displayed in header
    And verify URL contains country code "BG" and language code "en"
    And verify footer displays "Bulgaria • BGN • English"

  @0288
  Scenario: Verify Croatia country selection with English language
    When user opens country selection modal
    And user selects "Croatia" country
    And verify language dropdown is enabled
    And user selects "angielski" language
    And user clicks save and go to shop
    Then verify country name "Croatia" is displayed in header
    And verify URL contains country code "HR" and language code "en"
    And verify footer displays "Croatia • EUR • English"

  @0289
  Scenario: Verify Czech Republic country selection with English language
    When user opens country selection modal
    And user selects "Czech Republic" country
    And verify language dropdown is enabled
    And user selects "angielski" language
    And user clicks save and go to shop
    Then verify country name "Czech Republic" is displayed in header
    And verify URL contains country code "CZ" and language code "en"
    And verify footer displays "Czech Republic • CZK • English"

  @0290
  Scenario: Verify Denmark country selection with English language
    When user opens country selection modal
    And user selects "Denmark" country
    And verify language dropdown is enabled
    And user selects "angielski" language
    And user clicks save and go to shop
    Then verify country name "Denmark" is displayed in header
    And verify URL contains country code "DK" and language code "en"
    And verify footer displays "Denmark • DKK • English"

  @0291
  Scenario: Verify Estonia country selection with English language
    When user opens country selection modal
    And user selects "Estonia" country
    And verify language dropdown is enabled
    And user selects "angielski" language
    And user clicks save and go to shop
    Then verify country name "Estonia" is displayed in header
    And verify URL contains country code "EE" and language code "en"
    And verify footer displays "Estonia • EUR • English"

  @0292
  Scenario: Verify Finland country selection with English language
    When user opens country selection modal
    And user selects "Finland" country
    And verify language dropdown is enabled
    And user selects "angielski" language
    And user clicks save and go to shop
    Then verify country name "Finland" is displayed in header
    And verify URL contains country code "FI" and language code "en"
    And verify footer displays "Finland • EUR • English"

  @0293
  Scenario: Verify France country selection with English language
    When user opens country selection modal
    And user selects "France" country
    And verify language dropdown is enabled
    And user selects "angielski" language
    And user clicks save and go to shop
    Then verify country name "France" is displayed in header
    And verify URL contains country code "FR" and language code "en"
    And verify footer displays "France • EUR • English"

  @0294
  Scenario: Verify Greece country selection with English language
    When user opens country selection modal
    And user selects "Greece" country
    And verify language dropdown is enabled
    And user selects "angielski" language
    And user clicks save and go to shop
    Then verify country name "Greece" is displayed in header
    And verify URL contains country code "GR" and language code "en"
    And verify footer displays "Greece • EUR • English"

  @0295
  Scenario: Verify Hungary country selection with English language
    When user opens country selection modal
    And user selects "Hungary" country
    And verify language dropdown is enabled
    And user selects "angielski" language
    And user clicks save and go to shop
    Then verify country name "Hungary" is displayed in header
    And verify URL contains country code "HU" and language code "en"
    And verify footer displays "Hungary • HUF • English"

  @0296
  Scenario: Verify Ireland country selection with English language
    When user opens country selection modal
    And user selects "Ireland" country
    And verify language dropdown is enabled
    And user selects "angielski" language
    And user clicks save and go to shop
    Then verify country name "Ireland" is displayed in header
    And verify URL contains country code "IE" and language code "en"
    And verify footer displays "Ireland • EUR • English"

  @0297
  Scenario: Verify Italy country selection with English language
    When user opens country selection modal
    And user selects "Italy" country
    And verify language dropdown is enabled
    And user selects "angielski" language
    And user clicks save and go to shop
    Then verify country name "Italy" is displayed in header
    And verify URL contains country code "IT" and language code "en"
    And verify footer displays "Italy • EUR • English"

  @0298
  Scenario: Verify Latvia country selection with English language
    When user opens country selection modal
    And user selects "Latvia" country
    And verify language dropdown is enabled
    And user selects "angielski" language
    And user clicks save and go to shop
    Then verify country name "Latvia" is displayed in header
    And verify URL contains country code "LV" and language code "en"
    And verify footer displays "Latvia • EUR • English"

  @0299
  Scenario: Verify Lithuania country selection with English language
    When user opens country selection modal
    And user selects "Lithuania" country
    And verify language dropdown is enabled
    And user selects "angielski" language
    And user clicks save and go to shop
    Then verify country name "Lithuania" is displayed in header
    And verify URL contains country code "LT" and language code "en"
    And verify footer displays "Lithuania • EUR • English"

  @0300
  Scenario: Verify Luxembourg country selection with English language
    When user opens country selection modal
    And user selects "Luxembourg" country
    And verify language dropdown is enabled
    And user selects "angielski" language
    And user clicks save and go to shop
    Then verify country name "Luxembourg" is displayed in header
    And verify URL contains country code "LU" and language code "en"
    And verify footer displays "Luxembourg • EUR • English"

  @0301
  Scenario: Verify Netherlands country selection with English language
    When user opens country selection modal
    And user selects "Netherlands" country
    And verify language dropdown is enabled
    And user selects "angielski" language
    And user clicks save and go to shop
    Then verify country name "Netherlands" is displayed in header
    And verify URL contains country code "NL" and language code "en"
    And verify footer displays "Netherlands • EUR • English"

  @0302
  Scenario: Verify Portugal country selection with English language
    When user opens country selection modal
    And user selects "Portugal" country
    And verify language dropdown is enabled
    And user selects "angielski" language
    And user clicks save and go to shop
    Then verify country name "Portugal" is displayed in header
    And verify URL contains country code "PT" and language code "en"
    And verify footer displays "Portugal • EUR • English"

  @0303
  Scenario: Verify Romania country selection with English language
    When user opens country selection modal
    And user selects "Romania" country
    And verify language dropdown is enabled
    And user selects "angielski" language
    And user clicks save and go to shop
    Then verify country name "Romania" is displayed in header
    And verify URL contains country code "RO" and language code "en"
    And verify footer displays "Romania • RON • English"

  @0304
  Scenario: Verify Slovakia country selection with English language
    When user opens country selection modal
    And user selects "Slovakia" country
    And verify language dropdown is enabled
    And user selects "angielski" language
    And user clicks save and go to shop
    Then verify country name "Slovakia" is displayed in header
    And verify URL contains country code "SK" and language code "en"
    And verify footer displays "Slovakia • EUR • English"

  @0305
  Scenario: Verify Slovenia country selection with English language
    When user opens country selection modal
    And user selects "Slovenia" country
    And verify language dropdown is enabled
    And user selects "angielski" language
    And user clicks save and go to shop
    Then verify country name "Slovenia" is displayed in header
    And verify URL contains country code "SI" and language code "en"
    And verify footer displays "Slovenia • EUR • English"

  @0306
  Scenario: Verify Spain country selection with English language
    When user opens country selection modal
    And user selects "Spain" country
    And verify language dropdown is enabled
    And user selects "angielski" language
    And user clicks save and go to shop
    Then verify country name "Spain" is displayed in header
    And verify URL contains country code "ES" and language code "en"
    And verify footer displays "Spain • EUR • English"

  @0307
  Scenario: Verify Sweden country selection with English language
    When user opens country selection modal
    And user selects "Sweden" country
    And verify language dropdown is enabled
    And user selects "angielski" language
    And user clicks save and go to shop
    Then verify country name "Sweden" is displayed in header
    And verify URL contains country code "SE" and language code "en"
    And verify footer displays "Sweden • SEK • English"

  @0308
  Scenario: Verify United Kingdom country selection with English language
    When user opens country selection modal
    And user selects "United Kingdom" country
    And verify language dropdown is enabled
    And user selects "angielski" language
    And user clicks save and go to shop
    Then verify country name "United Kingdom" is displayed in header
    And verify URL contains country code "GB" and language code "en"
    And verify footer displays "United Kingdom • GBP • English"

  @0309
  Scenario: Verify user can close country selection modal
    When user opens country selection modal
    Then verify country selection modal is open
    When user closes country selection modal
    Then verify country selection modal is closed

  @0281
  Scenario: Verify cart and coupon state preserved when changing language from Polish to English
    Given that user is on the "home page"
    When user searches for "Fotel"
    And user opens the first product from search results
    And save product SKU from PDP
    And user adds the product to cart
    And user opens cart
    And user applies discount code "automation"
    Then verify discount is applied correctly and total price is updated with discount applied
    When user opens country selection modal
    And user selects "Poland" country
    And verify language dropdown is enabled
    And user selects "angielski" language
    And user clicks save and go to shop
    And user opens cart
    Then verify product SKU matches in cart
    And verify discount is applied correctly and total price is updated with discount applied

  @0310
  Scenario: Verify language dropdown is disabled until country is selected
    When user opens country selection modal
    Then verify language dropdown is disabled

  @0311
  Scenario: Verify go to store button is disabled until all options are selected
    When user opens country selection modal
    Then verify go to store button is disabled
    When user selects "Poland" country
    Then verify go to store button is disabled
    When user selects "angielski" language
    Then verify go to store button is enabled
