serenity {
  console.colors = true
  take.screenshots = FOR_FAILURES
  browser.maximized = true
  browser.width = 1920
  browser.height = 1180
  # Increase timeouts to prevent stale elements
  timeout = 35000
  wait.for.timeout = 45000
  webdriver.wait.for.timeout = 45000
  # Additional stability settings
  step.delay = 500
  webdriver.implicit.wait = 15000
  webdriver.page.load.timeout = 60000
  webdriver.script.timeout = 30000
}

webdriver {
  autodownload = true
  driver = chrome
  implicit.wait = 10000
  capabilities {
    browserName = "chrome"
    acceptInsecureCerts = true
    "goog:chromeOptions" {
      args = ["start-maximized", "test-type", "ignore-certificate-errors", "disable-default-apps",
        "disable-web-security", "disable-infobars", "disable-gpu", "homepage=about:blank", "no-first-run",
        "safebrowsing-disable-download-protection", "safebrowsing-disable-extension-blacklist", "disable-extensions",
        "disable-print-preview", "disable-extensions-file-access-check", "disable-notifications", "remote-allow-origins=*",
        "deny-permission-prompts"]
      prefs {
        //{0 ==> default, 1 ==> allow, 2 ==> block}
        default_content_settings.popups = 2
        default_content_settings.notifications = 2
        default_content_settings.geolocation = 2
        credentials_enable_service = false
        password_manager_enabled = false
        safebrowsing.enabled = true
        profile.cookie_controls_mode = 0
      }
      excludeSwitches = ["enable-automation", "load-extension"],
    }
  }
  firefox {
      webdriver {
        driver = firefox
        autodownload = true
        capabilities {
          browserName = "firefox"
          acceptInsecureCerts = true
          pageLoadStrategy = "normal"
          unhandledPromptBehavior = "dismiss"
          strictFileInteractability = true
          "moz:firefoxOptions" {
            args = [
              "-headless",
              "--no-sandbox",
              "--disable-gpu",
              "--disable-dev-shm-usage",
              "--window-size=1920,1080"
            ]
            binary = "/usr/bin/firefox"
            prefs {
              "dom.webnotifications.enabled" = false
              "dom.push.enabled" = false
              "browser.safebrowsing.malware.enabled" = false
              "browser.safebrowsing.phishing.enabled" = false
              "browser.startup.homepage" = "about:blank"
              "browser.newtab.preload" = false
              "browser.newtabpage.enabled" = false
              "datareporting.policy.dataSubmissionEnabled" = false
              "datareporting.healthreport.uploadEnabled" = false
              "toolkit.telemetry.enabled" = false
              "toolkit.telemetry.unified" = false
              "experiments.supported" = false
              "experiments.enabled" = false
              "network.captive-portal-service.enabled" = false
              "network.dns.disablePrefetch" = true
              "network.http.speculative-parallel-limit" = 0
              "network.predictor.enabled" = false
              "network.prefetch-next" = false
              "security.fileuri.strict_origin_policy" = false
              "media.gmp-manager.updateEnabled" = false
              "extensions.pocket.enabled" = false
              "extensions.shield-recipe-client.enabled" = false
              "app.shield.optoutstudies.enabled" = false
              "app.normandy.enabled" = false
              "extensions.formautofill.addresses.enabled" = false
              "extensions.formautofill.available" = "off"
              "extensions.formautofill.creditCards.enabled" = false
              "signon.rememberSignons" = false
              "signon.autofillForms" = false
              "security.mixed_content.block_active_content" = false
              "security.mixed_content.block_display_content" = false
            }
          }
        }
      }
  }
  edge {
    webdriver {
      driver = edge
      autodownload = true
      capabilities {
        browserName = "MicrosoftEdge"
        acceptInsecureCerts = true
        "ms:edgeOptions" {
          args = [
            "--headless=new",
            "--no-sandbox",
            "--disable-dev-shm-usage",
            "--disable-gpu",
            "--disable-web-security",
            "--disable-infobars",
            "--disable-extensions",
            "--disable-default-apps",
            "--disable-notifications",
            "--disable-print-preview",
            "--disable-extensions-file-access-check",
            "--remote-allow-origins=*",
            "--deny-permission-prompts",
            "--window-size=1920,1080",
            "--start-maximized",
            "--disable-background-timer-throttling",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding",
            "--disable-features=TranslateUI,VizDisplayCompositor",
            "--disable-ipc-flooding-protection",
            "--disable-client-side-phishing-detection",
            "--disable-component-update",
            "--disable-domain-reliability",
            "--disable-sync",
            "--disable-translate",
            "--disable-background-networking",
            "--disable-background-mode",
            "--disable-component-extensions-with-background-pages",
            "--disable-popup-blocking",
            "--disable-prompt-on-repost",
            "--disable-hang-monitor",
            "--disable-breakpad",
            "--disable-crash-reporter",
            "--disable-logging",
            "--disable-metrics",
            "--disable-metrics-reporting",
            "--no-first-run",
            "--no-default-browser-check",
            "--no-pings",
            "--force-device-scale-factor=1",
            "--aggressive-cache-discard",
            "--memory-pressure-off",
            "--max_old_space_size=4096",
            "--disable-blink-features=AutomationControlled",
            "--enable-features=NetworkService,NetworkServiceLogging",
            "--force-color-profile=srgb",
            "--disable-field-trial-config"
          ]
          binary = "/usr/bin/microsoft-edge"
        }
      }
    }
  }
}

environment = DEV-GDANSK

environments {
  DEV-GDANSK {
    patio.ui.base.url = "https://gdansk.dajardev.pl/pl/PL"
  }
  DEV-KOSZALIN {
    patio.ui.base.url = "https://koszalin.dajardev.pl/pl/PL"
  }
  DEV-WARSZAWA {
    patio.ui.base.url = "https://warszawa.dajardev.pl/pl/PL"
  }
  DEV-DAJAR-DEV-PL {
    patio.ui.base.url = "https://dajardev.pl/pl/PL"
  }
}
