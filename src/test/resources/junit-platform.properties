# REDUCED parallel execution to prevent <PERSON><PERSON>ty report corruption
cucumber.execution.parallel.enabled=true
cucumber.execution.parallel.config.strategy=fixed
cucumber.execution.parallel.config.fixed.parallelism=8
cucumber.execution.parallel.config.fixed.max-pool-size=8
cucumber.execution.exclusive-resources.sequential.read-write=java.lang.System.properties
cucumber.execution.exclusive-resources.order_logged_in_sequential_1.read-write=utils.lock_objects.LoggedInOrderLock1.lockObject
cucumber.execution.exclusive-resources.order_logged_in_sequential_2.read-write=utils.lock_objects.LoggedInOrderLock2.lockObject
cucumber.execution.exclusive-resources.account_sequential_1.read-write=utils.lock_objects.AccountLock1.lockObject
cucumber.execution.exclusive-resources.account_sequential_2.read-write=utils.lock_objects.AccountLock2.lockObject