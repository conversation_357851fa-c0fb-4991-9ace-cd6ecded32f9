serenity {
  console.colors = true
  take.screenshots = FOR_FAILURES
  browser.maximized = true
  browser.width = 1920
  browser.height = 1180
  # Increase timeouts for Docker environment to prevent stale elements
  timeout = 45000
  wait.for.timeout = 45000
  webdriver.wait.for.timeout = 45000
  # Add stability settings for <PERSON><PERSON> to prevent stale elements
  restart.browser.each.scenario = false
  use.unique.browser = false
  # Additional stability settings
  step.delay = 500
  webdriver.implicit.wait = 15000
  webdriver.page.load.timeout = 60000
  webdriver.script.timeout = 30000
}

webdriver {
  autodownload = true
  driver = chrome
  # Increase implicit wait for Docker
  implicit.wait = 10000
  capabilities {
    browserName = "chrome"
    acceptInsecureCerts = true,
    "goog:chromeOptions" {
      args = [
        "--headless=new",
        "--no-sandbox",
        "--disable-dev-shm-usage",
        "--disable-gpu",
        "--disable-web-security",
        "--disable-infobars",
        "--disable-extensions",
        "--disable-default-apps",
        "--disable-notifications",
        "--disable-print-preview",
        "--disable-extensions-file-access-check",
        "--remote-allow-origins=*",
        "--deny-permission-prompts",
        "--window-size=1920,1080",
        "--start-maximized",
        # Stability options to prevent stale elements in Docker
        "--disable-background-timer-throttling",
        "--disable-backgrounding-occluded-windows",
        "--disable-renderer-backgrounding",
        "--disable-features=TranslateUI,VizDisplayCompositor",
        "--disable-ipc-flooding-protection",
        "--disable-client-side-phishing-detection",
        "--disable-component-update",
        "--disable-domain-reliability",
        "--disable-sync",
        "--disable-translate",
        "--disable-background-networking",
        "--disable-background-mode",
        "--disable-component-extensions-with-background-pages",
        "--disable-default-apps",
        "--disable-popup-blocking",
        "--disable-prompt-on-repost",
        "--disable-hang-monitor",
        "--disable-breakpad",
        "--disable-crash-reporter",
        "--disable-logging",
        "--disable-metrics",
        "--disable-metrics-reporting",
        "--no-first-run",
        "--no-default-browser-check",
        "--no-pings",
        "--no-zygote",
        "--single-process",
        "--force-device-scale-factor=1",
        "--aggressive-cache-discard",
        "--memory-pressure-off",
        "--max_old_space_size=4096",
        # Additional stability for element interactions
        "--disable-blink-features=AutomationControlled",
        "--disable-features=VizDisplayCompositor,TranslateUI",
        "--enable-features=NetworkService,NetworkServiceLogging",
        "--force-color-profile=srgb",
        "--disable-field-trial-config"
      ]
      binary = "/usr/bin/google-chrome"
      excludeSwitches = ["enable-automation", "load-extension"]
      prefs {
        default_content_settings.popups = 2
        default_content_settings.notifications = 2
        default_content_settings.geolocation = 2
        credentials_enable_service = false
        password_manager_enabled = false
        safebrowsing.enabled = true
        profile.cookie_controls_mode = 0
        # Add performance settings
        profile.default_content_setting_values.notifications = 2
        profile.managed_default_content_settings.images = 1
      }
    }
  }
}

environment = DEV-GDANSK

environments {
  DEV-GDANSK {
    patio.ui.base.url = "https://gdansk.dajardev.pl/pl/PL"
  }
  DEV-KOSZALIN {
    patio.ui.base.url = "https://koszalin.dajardev.pl/pl/PL"
  }
  DEV-WARSZAWA {
    patio.ui.base.url = "https://warszawa.dajardev.pl/pl/PL"
  }
  DEV-DAJAR-DEV-PL {
    patio.ui.base.url = "https://dajardev.pl/pl/PL"
  }
}
