package steps.cart_steps;

import actions.CartActions;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import net.serenitybdd.annotations.Steps;

public class CartSteps {
    @Steps
    private CartActions cartActions;

    @And("verify cart counter shows {string}")
    public void verifyCartCounterShows(String productCount) {
        cartActions.verifyCartCounterShows(productCount);
    }

    @And("verify product details are correct in cart")
    public void verifyProductDetailsAreCorrectInCart() {
        cartActions.verifyProductDetailsAreCorrectInCart();

    }

    @Then("verify product is displayed in cart")
    public void verifyProductIsDisplayedInCart() {
        cartActions.verifyProductIsDisplayedInCart();
    }

    @Then("verify both products are displayed in cart")
    public void verifyBothProductsAreDisplayedInCart() {
        cartActions.verifyBothProductsAreDisplayedInCart();
    }

    @And("user changes quantity to {string} in cart")
    public void userChangesQuantityToInCart(String quantity) {
        cartActions.changeProductQuantityInCart(quantity);
    }

    @And("user changes quantity to random amount in cart")
    public void userChangesQuantityToRandomAmountInCart() {
        cartActions.changeProductQuantityToRandomAmount();
    }

    @Then("verify total price is updated correctly")
    public void verifyTotalPriceIsUpdatedCorrectly() {
        cartActions.verifyTotalPriceIsUpdatedCorrectly();
    }

    @And("verify quantity shows {string} in cart")
    public void verifyQuantityShowsInCart(String quantity) {
        cartActions.verifyQuantityShowsInCart(quantity);
    }

    @Then("verify cart is empty")
    public void verifyCartIsEmpty() {
        cartActions.verifyCartIsEmpty();
    }

    @And("user removes the product from cart")
    public void userRemovesTheProductFromCart() {
        cartActions.removeProductFromCart();
    }

    @And("user clicks on product name in cart")
    public void userClicksOnProductNameInCart() {
        cartActions.clickOnProductNameInCart();
    }

    @And("verify continue shopping button is displayed")
    public void verifyContinueShoppingButtonIsDisplayed() {
        cartActions.verifyContinueShoppingButtonIsDisplayed();
    }

    @When("verify user can click continue shopping button")
    public void userClicksContinueShopping() {
        cartActions.clickContinueShopping();
    }

    @Then("verify product is still in cart")
    public void verifyProductIsStillInCart() {
        cartActions.verifyProductIsStillInCart();
    }

    @Then("verify total price is updated correctly to {string} times the product price")
    public void verifyTotalPriceIsUpdatedCorrectlyToTimesTheProductPrice(String quantity) {
        cartActions.verifyTotalPriceIsUpdatedCorrectlyToTimesTheProductPrice(quantity);
    }

    @Then("verify subtotal is calculated correctly")
    public void verifySubtotalIsCalculatedCorrectly() {
        cartActions.verifySubtotalIsCalculatedCorrectly();
    }

    @And("verify vat cost is displayed correctly")
    public void verifyVatCostIsDisplayedCorrectly() {
        cartActions.verifyVatCostIsDisplayedCorrectly();
    }

    @And("user clicks on plus button to increase quantity")
    public void userClicksOnPlusButtonToIncreaseQuantity() {
        cartActions.clickOnPlusButtonToIncreaseQuantity();
    }

    @Then("verify error message is displayed indicating insufficient stock")
    public void verifyErrorMessageIsDisplayedIndicatingInsufficientStock() {
        cartActions.verifyErrorMessageForInsufficientStock();
    }

    @And("user clicks on minus button to decrease quantity")
    public void userClicksOnMinusButtonToDecreaseQuantity() {
        cartActions.clickOnMinusButtonToDecreaseQuantity();
    }

    @And("the total is updated correctly")
    public void theTotalIsUpdatedCorrectly() {
        cartActions.calculateTotalPriceInCart();
    }

    @And("the quantity input shows {string}")
    public void theQuantityInputShows(String quantity) {
        cartActions.verifyQuantityInputShows(quantity);
    }

    @And("user removes one product from cart")
    public void userRemovesOneProductFromCart() {
        cartActions.removeOneProductFromCart();
    }

    @Then("verify remaining product is displayed in cart")
    public void verifyRemainingProductIsDisplayedInCart() {
        cartActions.verifyRemainingProductIsDisplayedInCart();
    }

    @And("verify total price is updated correctly for remaining product")
    public void verifyTotalPriceIsUpdatedCorrectlyForRemainingProduct() {
        cartActions.verifyTotalPriceIsUpdatedCorrectlyForRemainingProduct();
    }

    @Then("verify free shipping threshold message is displayed and threshold amount is correct")
    public void verifyFreeShippingThresholdMessageIsDisplayed() {
        cartActions.verifyFreeShippingThresholdMessageIsDisplayed();
    }

    @Then("verify free delivery message is displayed in cart")
    public void verifyFreeDeliveryMessageIsDisplayedInCart() {
        cartActions.verifyFreeDeliveryMessageIsDisplayedInCart();
    }

    @Then("verify free shipping threshold progress bar shows correct percentage")
    public void verifyFreeShippingThresholdProgressBarShowsCorrectPercentage() {
        cartActions.verifyFreeShippingThresholdProgressBarShowsCorrectPercentage();
    }

    @Then("verify remaining amount for free shipping is correct")
    public void verifyRemainingAmountForFreeShippingIsCorrect() {
        cartActions.verifyRemainingAmountForFreeShippingIsCorrect();
    }

    @And("user applies discount code {string}")
    public void userAppliesDiscountCode(String discountCode) {
        cartActions.applyDiscountCode(discountCode);
    }

    @Then("verify discount is applied correctly and total price is updated with discount applied")
    public void verifyDiscountIsAppliedCorrectlyAndTotalPriceIsUpdatedWithDiscountApplied() {
        cartActions.verifyDiscountIsAppliedCorrectlyAndTotalPriceIsUpdatedWithDiscountApplied();
    }

    @And("user removes discount code")
    public void userRemovesDiscountCode() {
        cartActions.removeDiscountCode();
    }

    @Then("verify discount is removed and total price is updated correctly")
    public void verifyDiscountIsRemovedAndTotalPriceIsUpdatedCorrectly() {
        cartActions.verifyDiscountIsRemovedAndTotalPriceIsUpdatedCorrectly();
    }

    @Then("verify error message is displayed indicating invalid discount code")
    public void verifyErrorMessageIsDisplayedIndicatingInvalidDiscountCode() {
        cartActions.verifyErrorMessageForInvalidDiscountCode();
    }

    @And("verify total price remains unchanged")
    public void verifyTotalPriceRemainsUnchanged() {
        cartActions.verifyTotalPriceRemainsUnchanged();
    }

    @And("verify cart contents are preserved")
    public void verifyCartContentsArePreserved() {
        cartActions.verifyCartContentsArePreserved();
    }

    @And("verify total price is updated correctly after modifying quantity")
    public void verifyTotalPriceIsUpdatedCorrectlyAfterModifyingQuantity() {
        cartActions.verifyTotalPriceIsUpdatedCorrectlyAfterModifyingQuantity();
    }

    @And("remove product from cart if present")
    public void removeProductFromCartIfPresent() {
        cartActions.removeProductFromCartIfPresent();
    }

    @Then("verify coupon code is still applied and cart is preserved after login")
    public void verifyCouponCodeIsStillAppliedAndCartIsPreservedAfterLogin() {
        cartActions.verifyCouponCodePersistenceAfterLogin();
    }

    @Then("verify product name is displayed in cart")
    public void verifyProductNameIsDisplayedInCart() {
        cartActions.verifyProductIsStillInCart();
    }
}
