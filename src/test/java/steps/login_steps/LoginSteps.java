package steps.login_steps;

import actions.LoginActions;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import net.serenitybdd.annotations.Steps;

public class LoginSteps {
    @Steps
    LoginActions loginActions;

    @Given("user logs in with valid credentials")
    public void userLogsInWithValidCredentials() {
        loginActions.navigateToLoginPage();
        loginActions.loginWithValidCredentials();
    }

    @Given("user logs in as a {string}")
    public void userLogsInAsA(String userType) {
        loginActions.navigateToLoginPage();
        loginActions.loginWithSpecificUser(userType);
    }

    @When("user navigates to login page")
    public void userNavigatesToLoginPage() {
        loginActions.navigateToLoginPage();
    }

    @When("user enters email {string} and password {string}")
    public void userEntersEmailAndPassword(String email, String password) {
        // This would need a custom method in LoginActions
    }



    @Then("user should be successfully logged in")
    public void userShouldBeSuccessfullyLoggedIn() {
        loginActions.verifyLoginSuccessful();
    }

    @Then("user should see login error message")
    public void userShouldSeeLoginErrorMessage() {
        loginActions.verifyLoginFailed();
    }

    @When("user logs out")
    public void userLogsOut() {
        loginActions.logout();
    }

    @And("user logs in with order logged in credentials")
    public void userLogsInWithOrderLoggedInCredentials() {
        loginActions.navigateToLoginPage();
        loginActions.loginWithLoggedInOrderCredentials();
    }

    @And("user logs in with order logged in credentials 2")
    public void userLogsInWithOrderLoggedInCredentials2() {
        loginActions.navigateToLoginPage();
        loginActions.loginWithLoggedInOrderCredentials2();
    }

    @And("user logs in with account test credentials")
    public void userLogsInWithAccountTestCredentials() {
        loginActions.navigateToLoginPage();
        loginActions.loginWithAccountTestCredentials();
    }

    @And("user logs in with account test credentials 2")
    public void userLogsInWithAccountTestCredentials2() {
        loginActions.navigateToLoginPage();
        loginActions.loginWithAccountTestCredentials2();
    }

    @When("user clicks on login link")
    public void userClicksOnLoginLink() {
        loginActions.navigateToLoginPage();
    }

    @And("user enters valid login credentials")
    public void userEntersValidLoginCredentials() {
        loginActions.enterValidCredentials();
    }

    @And("user clicks on login button")
    public void userClicksOnLoginButton() {
        loginActions.clickLoginButton();
    }

    @Then("verify user is successfully logged in")
    public void verifyUserIsSuccessfullyLoggedIn() {
        loginActions.verifyLoginSuccessful();
    }

    @And("user enters invalid email {string} and valid password")
    public void userEntersInvalidEmailAndValidPassword(String invalidEmail) {
        loginActions.enterInvalidEmailAndValidPassword(invalidEmail);
    }

    @Then("verify invalid email error message is displayed on login modal")
    public void verifyInvalidEmailErrorMessageIsDisplayedOnLoginModal() {
        loginActions.verifyInvalidEmailErrorMessage();
    }

    @And("user enters valid email and empty password")
    public void userEntersValidEmailAndEmptyPassword() {
        loginActions.enterValidEmailAndEmptyPassword();
    }

    @Then("verify empty password error message is displayed")
    public void verifyEmptyPasswordErrorMessageIsDisplayed() {
        loginActions.verifyEmptyPasswordErrorMessage();
    }

    @And("user enters invalid credentials")
    public void userEntersInvalidCredentials() {
        loginActions.enterInvalidCredentials();
    }

    @Then("verify invalid login credentials error message is displayed")
    public void verifyInvalidLoginCredentialsErrorMessageIsDisplayed() {
        loginActions.verifyInvalidLoginCredentialsErrorMessage();
    }

    @Then("verify login modal header is displayed")
    public void verifyLoginModalHeaderIsDisplayed() {
        loginActions.verifyLoginModalHeader();
    }

    @And("verify login modal sub header is displayed")
    public void verifyLoginModalSubHeaderIsDisplayed() {
        loginActions.verifyLoginModalSubHeader();
    }

    @And("verify forgot password link is displayed")
    public void verifyForgotPasswordLinkIsDisplayed() {
        loginActions.verifyForgotPasswordLinkDisplayed();
    }

    @And("verify register link is displayed")
    public void verifyRegisterLinkIsDisplayed() {
        loginActions.verifyRegisterLinkDisplayed();
    }

    @And("verify you dont have account text is displayed")
    public void verifyYouDontHaveAccountTextIsDisplayed() {
        loginActions.verifyYouDontHaveAccountTextDisplayed();
    }

    @And("user clicks on forgot password link")
    public void userClicksOnForgotPasswordLink() {
        loginActions.clickForgotPasswordLink();
    }

    @Then("verify forgot password modal header is displayed")
    public void verifyForgotPasswordModalHeaderIsDisplayed() {
        loginActions.verifyForgotPasswordModalHeader();
    }

    @And("verify forgot password description is displayed")
    public void verifyForgotPasswordDescriptionIsDisplayed() {
        loginActions.verifyForgotPasswordDescription();
    }

    @When("user enters valid email for password reset")
    public void userEntersValidEmailForPasswordReset() {
        loginActions.enterValidEmailForPasswordReset();
    }

    @And("user clicks on reset password button")
    public void userClicksOnResetPasswordButton() {
        loginActions.clickResetPasswordButton();
    }

    @Then("verify reset password success message header is displayed")
    public void verifyResetPasswordSuccessMessageHeaderIsDisplayed() {
        loginActions.verifyResetPasswordSuccessMessageHeader();
    }

    @And("verify reset password success message is displayed")
    public void verifyResetPasswordSuccessMessageIsDisplayed() {
        loginActions.verifyResetPasswordSuccessMessage();
    }

    @When("user clicks on close reset password modal button")
    public void userClicksOnCloseResetPasswordModalButton() {
        loginActions.clickCloseResetPasswordModalButton();
    }

    @Then("verify login modal is closed")
    public void verifyLoginModalIsClosed() {
        loginActions.verifyLoginModalIsClosed();
    }

    @When("user clicks on return to login button")
    public void userClicksOnReturnToLoginButton() {
        loginActions.clickReturnToLoginButton();
    }

    @When("user enters invalid email {string} for password reset")
    public void userEntersInvalidEmailForPasswordReset(String invalidEmail) {
        loginActions.enterInvalidEmailForPasswordReset(invalidEmail);
    }

    @Then("verify invalid email error message is displayed on forgot password modal")
    public void verifyInvalidEmailErrorMessageIsDisplayedOnForgotPasswordModal() {
        loginActions.verifyInvalidEmailErrorOnForgotPasswordModal();
    }

    @When("user clears email field for password reset")
    public void userClearsEmailFieldForPasswordReset() {
        loginActions.clearEmailFieldForPasswordReset();
    }

    @When("user closes the login modal")
    public void userClosesTheLoginModal() {
        loginActions.closeLoginModal();
    }

    @When("user clicks on login link again")
    public void userClicksOnLoginLinkAgain() {
        loginActions.clickLoginLinkAgain();
    }

    @And("user clicks on register link")
    public void userClicksOnRegisterLink() {
        loginActions.clickRegisterLink();
    }

    @Then("verify register modal header is displayed")
    public void verifyRegisterModalHeaderIsDisplayed() {
        loginActions.verifyRegisterModalHeader();
    }

    @And("verify login link is displayed on register modal")
    public void verifyLoginLinkIsDisplayedOnRegisterModal() {
        loginActions.verifyLoginLinkDisplayedOnRegisterModal();
    }

    @When("user clicks on login link on register modal")
    public void userClicksOnLoginLinkOnRegisterModal() {
        loginActions.clickLoginLinkOnRegisterModal();
    }

    @And("user enters email with only whitespaces and password with only whitespaces")
    public void userEntersEmailWithOnlyWhitespacesAndPasswordWithOnlyWhitespaces() {
        loginActions.enterWhitespaceEmailAndPassword();
    }

    @Then("verify appropriate validation error messages are displayed")
    public void verifyAppropriateValidationErrorMessagesAreDisplayed() {
        loginActions.verifyAppropriateValidationErrorMessages();
    }

    @And("user enters very long email and very long password")
    public void userEntersVeryLongEmailAndVeryLongPassword() {
        loginActions.enterVeryLongEmailAndPassword();
    }

    @Then("verify login attempt is handled appropriately")
    public void verifyLoginAttemptIsHandledAppropriately() {
        loginActions.verifyLoginAttemptHandledAppropriately();
    }

    @When("user refreshes the page after login")
    public void userRefreshesThePageAfterLogin() {
        loginActions.refreshPage();
    }

    @Then("verify user remains logged in")
    public void verifyUserRemainsLoggedIn() {
        loginActions.verifyUserRemainsLoggedIn();
    }

    @When("user enters invalid credentials again")
    public void userEntersInvalidCredentialsAgain() {
        loginActions.enterInvalidCredentialsAgain();
    }

    @And("user navigates through login form using tab key")
    public void userNavigatesThroughLoginFormUsingTabKey() {
        loginActions.navigateThroughLoginFormUsingTabKey();
    }

    @Then("verify all form elements are accessible via keyboard")
    public void verifyAllFormElementsAreAccessibleViaKeyboard() {
        loginActions.verifyAllFormElementsAccessibleViaKeyboard();
    }

    @And("verify user can submit form using enter key")
    public void verifyUserCanSubmitFormUsingEnterKey() {
        loginActions.verifyUserCanSubmitFormUsingEnterKey();
    }

    @When("user closes forgot password modal")
    public void userClosesForgotPasswordModal() {
        loginActions.closeForgotPasswordModal();
    }

    @Then("verify forgot password modal is closed")
    public void verifyForgotPasswordModalIsClosed() {
        loginActions.verifyForgotPasswordModalIsClosed();
    }

}