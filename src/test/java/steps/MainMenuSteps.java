package steps;

import actions.MainMenuActions;
import io.cucumber.java.en.And;
import io.cucumber.java.en.When;
import net.serenitybdd.annotations.Steps;

public class MainMenuSteps {
    @Steps
    private MainMenuActions mainMenuActions;
    @And("navigate to {string} category")
    public void navigateToCategory(String categoryName) {
        mainMenuActions.openMainCategory(categoryName);
    }

    @And("navigate to {string} subcategory from {string}")
    public void navigateToSubcategoryFrom(String subCategory, String mainCategory) {
        mainMenuActions.openMainCategory(mainCategory);
        mainMenuActions.openSubCategory(subCategory);

    }

    @And("open cart and proceed to checkout")
    public void openCartAndProceedToCheckout() {
        mainMenuActions.openCartAndProceedToCheckout();
    }

    @When("user opens cart")
    public void userOpensCart() {
        mainMenuActions.openCart();
    }

    @And("user returns to the previous page")
    public void userReturnsToThePreviousPage() {
        mainMenuActions.navigateBack();
    }

    @And("go to my account page")
    public void goToMyAccountPage() {
        mainMenuActions.goToMyAccountPage();
    }
}
