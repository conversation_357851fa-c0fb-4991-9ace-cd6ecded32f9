package steps.search_steps;

import actions.SearchActions;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import net.serenitybdd.annotations.Steps;

public class SearchSteps {
    @Steps
    private SearchActions searchActions;

    @When("user searches for {string}")
    public void userSearchesFor(String searchTerm) {
        searchActions.searchForProduct(searchTerm);
    }

    @Then("user should see the search results page")
    public void userShouldSeeTheSearchResultsPage() {
        searchActions.verifySearchResultsPageIsDisplayed();
    }

    @And("verify user should see that the results contain {string}")
    public void verifyUserShouldSeeThatTheResultsContain(String searchTerm) {
        searchActions.verifySearchResultsContain(searchTerm);
    }

    @When("user clicks on the search bar")
    public void userClicksOnTheSearchBar() {
        searchActions.clickOnSearchBar();
    }

    @Then("user should see the search bar dropdown")
    public void userShouldSeeTheSearchBarDropdown() {
        searchActions.verifyTheSearchBarDropdownIsDisplayed();
    }

    @And("verify user is able to close the search bar dropdown")
    public void verifyUserIsAbleToCloseTheSearchBarDropdown() {
        searchActions.verifyUserIsAbleToCloseTheSearchBarDropdown();
    }

    @Then("verify that all category links in the search dropdown open correct pages")
    public void verifyThatAllCategoryLinksInTheSearchDropdownOpenCorrectPages() {
        searchActions.verifyThatAllCategoryLinksInTheSearchDropdownOpenCorrectPages();
    }

    @And("user opens the first product from search results")
    public void userOpensTheFirstProductFromSearchResults() {
        searchActions.openTheFirstProduct();
    }

    @And("user opens the second product from search results")
    public void userOpensTheSecondProductFromSearchResults() {
        searchActions.openTheSecondProduct();
    }

    @And("user should be able to close the search bar dropdown")
    public void userShouldBeAbleToCloseTheSearchBarDropdown() {
        searchActions.verifyUserIsAbleToCloseTheSearchBarDropdown();
    }

    @Then("verify user should see that the header shows no results found for {string}")
    public void verifyUserShouldSeeThatTheHeaderShowsNoResultsFoundFor(String searchTerm) {
        searchActions.verifyNoResultsFoundHeader(searchTerm);
    }

    @And("the number of products header is displayed")
    public void theNumberOfProductsHeaderIsDisplayed() {
        searchActions.verifyNumberOfProductsHeaderIsDisplayed();
    }

    @Then("verify user should see that he number of results matches the count shown")
    public void verifyUserShouldSeeThatHeNumberOfResultsMatchesTheCountShown() {
        searchActions.verifyNumberOfResultsMatchesCountShown();
    }

    @Then("verify user should see that product titles and prices are visible on the results page")
    public void verifyUserShouldSeeThatProductTitlesAndPricesAreVisibleOnTheResultsPage() {
        searchActions.verifyProductTitlesAndPricesAreVisibleOnResultsPage();
    }

    @Then("verify user should see the search box")
    public void verifyUserShouldSeeTheSearchBox() {
        searchActions.verifySearchBoxIsVisible();
    }

    @Then("verify user should see that the search results page persists in different {string}")
    public void verifyUserShouldSeeThatTheSearchResultsPagePersistsInDifferent(String action) {
       searchActions.verifySearchResultsPagePersistsInDifferent(action);
    }

    @When("user types {string} in the search bar")
    public void userTypesInTheSearchBar(String searchTerm) {
        searchActions.typeInSearchBar(searchTerm);
    }

    @And("user clicks on the search icon")
    public void userClicksOnTheSearchIcon() {
        searchActions.clickOnSearchIcon();
    }

    @When("user searches for product by code {string}")
    public void userSearchesForProductByCode(String productCode) {
        searchActions.searchForProductByCode(productCode);
    }
}
