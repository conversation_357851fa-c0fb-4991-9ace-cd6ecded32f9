package steps.hooks;

import actions.AccountActions;
import io.cucumber.java.After;
import io.cucumber.java.Before;
import io.cucumber.java.Scenario;
import lombok.extern.slf4j.Slf4j;
import net.serenitybdd.annotations.Steps;
import utils.storage.Storage;

@Slf4j
public class Hooks {

    @Steps
    private AccountActions accountActions;

    @Before(order = 0)
    public void initialization() {
        Storage.instantiateStorage();
    }

    /**
     * Hook to clean up newly added addresses after scenarios that add addresses with different countries.
     * This hook runs after scenarios tagged with @remove_newly_added_address (scenarios 0066 and 0067).
     * It navigates to the account page and deletes the newly added billing address (last in the list).
     */
    @After("@remove_newly_added_address")
    public void removeNewlyAddedAddress(Scenario scenario) {
        try {
            log.info("Cleaning up newly added address after scenario: {}", scenario.getName());

            // Navigate to account details page where addresses are managed
            accountActions.navigateToAccountDetails();

            // Delete the newly added billing address (both scenarios 0066 and 0067 add billing addresses)
            accountActions.deleteNewlyAddedBillingAddress();


        } catch (Exception e) {
            log.warn("Failed to remove newly added address after scenario: {}. Error: {}",
                    scenario.getName(), e.getMessage());
            // Don't fail the test if cleanup fails - just log the warning
        }
    }

}
