package steps.account_steps;

import actions.AccountActions;
import actions.ProductPageActions;
import actions.SearchActions;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import net.serenitybdd.annotations.Steps;

import java.util.UUID;

public class AccountSteps {
    @Steps
    private AccountActions accountActions;
    @Steps
    private SearchActions searchActions;
    @Steps
    private ProductPageActions productPageActions;

    @When("navigate to order history")
    public void navigateToOrderHistory() {
        accountActions.navigateToOrderHistory();
    }

    @Then("verify the recent order is displayed in order history")
    public void verifyTheRecentOrderIsDisplayedInOrderHistory() {
        accountActions.verifyTheRecentOrderIsDisplayedInOrderHistory();
    }

    @And("verify order details match the placed order")
    public void verifyOrderDetailsMatchThePlacedOrder() {
        accountActions.verifyOrderDetailsMatchPlacedOrder();
    }

    @Then("verify user is logged out")
    public void verifyUserIsLoggedOut() {
        accountActions.verifyUserIsLoggedOut();
    }

    // Ticket creation step definitions
    @And("click on contact with the store link")
    public void clickOnContactWithTheStoreLink() {
        accountActions.clickOnContactWithTheStoreLink();
    }

    @And("click on create a ticket link")
    public void clickOnCreateATicketLink() {
        accountActions.clickOnCreateATicketLink();
    }

    @And("click on reason for contact dropdown")
    public void clickOnReasonForContactDropdown() {
        accountActions.clickOnReasonForContactDropdown();
    }

    @And("select a random reason for contact")
    public void selectARandomReasonForContact() {
        accountActions.selectRandomReasonForContact();
    }

    @And("click on order to create a ticket dropdown")
    public void clickOnOrderToCreateATicketDropdown() {
        accountActions.clickOnOrderToCreateATicketDropdown();
    }

    @And("verify the first order matches the recently created order")
    public void verifyTheFirstOrderMatchesTheRecentlyCreatedOrder() {
        accountActions.verifyFirstOrderMatchesRecentlyCreatedOrder();
    }

    @And("select the first order from dropdown")
    public void selectTheFirstOrderFromDropdown() {
        accountActions.selectFirstOrderFromDropdown();
    }

    @And("enter ticket message {string}")
    public void enterTicketMessage(String message) {
        accountActions.enterTicketMessage(message);
    }

    @And("click on submit request button")
    public void clickOnSubmitRequestButton() {
        accountActions.clickOnSubmitRequestButton();
    }

    @Then("verify request submitted text contains {string}")
    public void verifyRequestSubmittedTextContains(String expectedText) {
        accountActions.verifyRequestSubmittedText(expectedText);
    }

    @Then("verify ticket created date is current date")
    public void verifyTicketCreatedDateIsCurrentDate() {
        accountActions.verifyTicketCreatedDateIsCurrentDate();
    }

    @And("verify ticket header contains order information")
    public void verifyTicketHeaderContainsOrderInformation() {
        accountActions.verifyTicketHeaderContainsOrderInformation();
    }

    @And("verify ticket last message header is {string}")
    public void verifyTicketLastMessageHeaderIs(String expectedHeader) {
        accountActions.verifyTicketLastMessageHeader(expectedHeader);
    }

    @And("verify ticket last message is {string}")
    public void verifyTicketLastMessageIs(String expectedMessage) {
        accountActions.verifyTicketLastMessage(expectedMessage);
    }

    // --- Account Page Feature Steps ---

    @When("user logs in with alternative credentials")
    public void userLogsInWithAlternativeCredentials() {
        accountActions.loginWithAlternativeCredentials();
    }

    @Then("verify user is logged in")
    public void verifyUserIsLoggedIn() {
        accountActions.verifyUserIsLoggedIn();
    }

    @When("user navigates to account details")
    public void userNavigatesToAccountDetails() {
        accountActions.navigateToAccountDetails();
    }

    @When("user changes name to {string} and last name to {string}")
    public void userChangesNameAndLastName(String name, String lastName) {
        accountActions.changeUserNameAndLastNameWithParameters(name, lastName);
    }

    @When("user saves the changes")
    public void userSavesTheChanges() {
        accountActions.saveProfileChanges();
    }

    @Then("user profile header should contain {string}")
    public void userProfileHeaderShouldContain(String expected) {
        accountActions.verifyUserProfileHeader(expected);
    }

    @Then("name and last name header should contain {string}")
    public void nameAndLastNameHeaderShouldContain(String expected) {
        accountActions.verifyNameAndLastNameHeader(expected);
    }

    @Then("name and last name value should contain {string}")
    public void nameAndLastNameValueShouldContain(String expected) {
        // If the placeholder is used, use the dedicated verification method
        if ("<RANDOM_FULL_NAME>".equals(expected)) {
            accountActions.verifyNameAndLastNameIsUpdated();
        } else {
            accountActions.verifyNameAndLastNameValue(expected);
        }
    }

    @Then("password header should contain {string}")
    public void passwordHeaderShouldContain(String expected) {
        accountActions.verifyPasswordHeader(expected);
    }

    @Then("password value should contain {string}")
    public void passwordValueShouldContain(String expected) {
        accountActions.verifyPasswordValue(expected);
    }

    @When("user navigates to billing address")
    public void userNavigatesToBillingAddress() {
        accountActions.navigateToBillingAddress();
    }

    @When("user edits billing address with valid data")
    public void userEditsBillingAddressWithValidData() {
        accountActions.editBillingAddressWithValidData();
    }

    @Then("verify billing address is updated")
    public void verifyBillingAddressIsUpdated() {
        accountActions.verifyBillingAddressIsUpdated();
    }

    @When("user navigates to shipping address")
    public void userNavigatesToShippingAddress() {
        accountActions.navigateToShippingAddress();
    }

    @When("user edits shipping address with valid data")
    public void userEditsShippingAddressWithValidData() {
        accountActions.editShippingAddressWithValidData();
    }

    @Then("verify shipping address is updated")
    public void verifyShippingAddressIsUpdated() {
        accountActions.verifyShippingAddressIsUpdated();
    }

    @When("user clicks add new shipping address")
    public void userClicksAddNewShippingAddress() {
        accountActions.clickAddNewShippingAddress();
    }

    @When("user enters valid shipping address data")
    public void userEntersValidShippingAddressData() {
        accountActions.enterValidShippingAddressData();
    }

    @When("user saves the new address")
    public void userSavesTheNewAddress() {
        accountActions.saveNewAddress();
    }

    @Then("verify new shipping address is added")
    public void verifyNewShippingAddressIsAdded() {
        accountActions.verifyNewShippingAddressIsAdded();
    }

    @When("user clicks add new billing address")
    public void userClicksAddNewBillingAddress() {
        accountActions.clickAddNewBillingAddress();
    }

    @When("user selects company billing address option")
    public void userSelectsCompanyBillingAddressOption() {
        accountActions.selectCompanyBillingAddressOption();
    }

    @When("user enters valid company billing address data")
    public void userEntersValidCompanyBillingAddressData() {
        accountActions.enterValidCompanyBillingAddressData();
    }

    @Then("verify new company billing address is added")
    public void verifyNewCompanyBillingAddressIsAdded() {
        accountActions.verifyNewCompanyBillingAddressIsAdded();
    }

    @When("user selects individual billing address option")
    public void userSelectsIndividualBillingAddressOption() {
        accountActions.selectIndividualBillingAddressOption();
    }

    @When("user enters valid individual billing address data")
    public void userEntersValidIndividualBillingAddressData() {
        accountActions.enterValidIndividualBillingAddressData();
    }

    @Then("verify new individual billing address is added")
    public void verifyNewIndividualBillingAddressIsAdded() {
        accountActions.verifyNewIndividualBillingAddressIsAdded();
    }

    @When("user enters shipping address data without phone number")
    public void userEntersShippingAddressDataWithoutPhoneNumber() {
        accountActions.enterShippingAddressDataWithoutPhoneNumber();
    }

    @Then("required field error should be displayed for phone number")
    public void requiredFieldErrorShouldBeDisplayedForPhoneNumber() {
        accountActions.verifyRequiredFieldErrorForPhoneNumber();
    }

    // ========== SHIPPING ADDRESS VALIDATION STEP DEFINITIONS ==========

    @When("user enters shipping address data without name")
    public void userEntersShippingAddressDataWithoutName() {
        accountActions.enterShippingAddressDataWithoutName();
    }

    @When("user enters shipping address data without last name")
    public void userEntersShippingAddressDataWithoutLastName() {
        accountActions.enterShippingAddressDataWithoutLastName();
    }

    @When("user enters shipping address data without address")
    public void userEntersShippingAddressDataWithoutAddress() {
        accountActions.enterShippingAddressDataWithoutAddress();
    }

    @When("user enters shipping address data without city")
    public void userEntersShippingAddressDataWithoutCity() {
        accountActions.enterShippingAddressDataWithoutCity();
    }

    @When("user enters shipping address data without post code")
    public void userEntersShippingAddressDataWithoutPostCode() {
        accountActions.enterShippingAddressDataWithoutPostCode();
    }

    @Then("required field error should be displayed for name")
    public void requiredFieldErrorShouldBeDisplayedForName() {
        accountActions.verifyRequiredFieldErrorForName();
    }

    @Then("required field error should be displayed for last name")
    public void requiredFieldErrorShouldBeDisplayedForLastName() {
        accountActions.verifyRequiredFieldErrorForLastName();
    }

    @Then("required field error should be displayed for address")
    public void requiredFieldErrorShouldBeDisplayedForAddress() {
        accountActions.verifyRequiredFieldErrorForAddress();
    }

    @Then("required field error should be displayed for city")
    public void requiredFieldErrorShouldBeDisplayedForCity() {
        accountActions.verifyRequiredFieldErrorForCity();
    }

    @Then("required field error should be displayed for post code")
    public void requiredFieldErrorShouldBeDisplayedForPostCode() {
        accountActions.verifyRequiredFieldErrorForPostCode();
    }

    // ========== BILLING ADDRESS VALIDATION STEP DEFINITIONS ==========

    @When("user enters billing address data without name")
    public void userEntersBillingAddressDataWithoutName() {
        accountActions.enterBillingAddressDataWithoutName();
    }

    @When("user enters billing address data without last name")
    public void userEntersBillingAddressDataWithoutLastName() {
        accountActions.enterBillingAddressDataWithoutLastName();
    }

    @When("user enters billing address data without address")
    public void userEntersBillingAddressDataWithoutAddress() {
        accountActions.enterBillingAddressDataWithoutAddress();
    }

    @When("user enters billing address data without city")
    public void userEntersBillingAddressDataWithoutCity() {
        accountActions.enterBillingAddressDataWithoutCity();
    }

    @When("user enters billing address data without post code")
    public void userEntersBillingAddressDataWithoutPostCode() {
        accountActions.enterBillingAddressDataWithoutPostCode();
    }

    @When("user adds product to cart")
    public void userAddsProductToCart() {
        searchActions.searchForProduct("Fotel");
        searchActions.openTheFirstProduct();
        productPageActions.addTheProductToCart();
    }

    @Then("email value should be {string}")
    public void emailValueShouldBe(String expectedEmail) {
        accountActions.verifyEmailValue(expectedEmail);
    }

    @And("user deletes the newly added shipping address")
    public void userDeletesTheNewlyAddedShippingAddress() {
        accountActions.deleteNewlyAddedShippingAddress();
    }

    @And("user deletes the newly added billing address")
    public void userDeletesTheNewlyAddedBillingAddress() {
        accountActions.deleteNewlyAddedBillingAddress();
    }

    // Chat functionality step definitions
    @When("user opens chat history")
    public void userOpensChatHistory() {
        accountActions.openChatHistory();
    }

    @When("user types random automation test message in chat")
    public void userTypesRandomAutomationTestMessageInChat() {
        String randomMessage = "Automation test message " + UUID.randomUUID().toString().substring(0, 8);
        accountActions.typeAndSendChatMessage(randomMessage);
    }

    @Then("verify correct user name is displayed in chat")
    public void verifyCorrectUserNameIsDisplayedInChat() {
        accountActions.verifyChatUserName();
    }

    @Then("verify date and time is displayed in chat")
    public void verifyDateAndTimeIsDisplayedInChat() {
        accountActions.verifyChatDateAndTime();
    }

    @Then("verify the message has been sent in chat")
    public void verifyTheMessageHasBeenSentInChat() {
        accountActions.verifyChatSentMessage();
    }

    @Then("verify message received header is displayed")
    public void verifyMessageReceivedHeaderIsDisplayed() {
        accountActions.verifyMessageReceivedHeader();
    }

    // ========== NEWSLETTER AGREEMENT CHECKBOX STEP DEFINITIONS ==========

    @When("user checks the newsletter agreement checkbox")
    public void userChecksTheNewsletterAgreementCheckbox() {
        accountActions.checkNewsletterAgreementCheckbox();
    }

    @When("user unchecks the newsletter agreement checkbox")
    public void userUnchecksTheNewsletterAgreementCheckbox() {
        accountActions.uncheckNewsletterAgreementCheckbox();
    }

    @Then("verify newsletter agreement checkbox label is displayed correctly")
    public void verifyNewsletterAgreementCheckboxLabelIsDisplayedCorrectly() {
        accountActions.verifyNewsletterAgreementCheckboxLabel();
    }

    @Then("verify newsletter agreement checkbox is checked")
    public void verifyNewsletterAgreementCheckboxIsChecked() {
        accountActions.verifyNewsletterAgreementCheckboxIsChecked();
    }

    @Then("verify newsletter agreement checkbox is unchecked")
    public void verifyNewsletterAgreementCheckboxIsUnchecked() {
        accountActions.verifyNewsletterAgreementCheckboxIsUnchecked();
    }

    // ========== FILE ATTACHMENT STEP DEFINITIONS ==========

    @When("user types message and attaches {string} file")
    public void userTypesMessageAndAttachesFile(String fileName) {
        String message = "Automation test message with file attachment";
        accountActions.typeMessageAndAttachFile(message, fileName);
    }

    @Then("verify attached file name {string} is displayed")
    public void verifyAttachedFileNameIsDisplayed(String fileName) {
        accountActions.verifyAttachedFileName(fileName);
    }

    @When("user sends the message with attached file")
    public void userSendsTheMessageWithAttachedFile() {
        accountActions.sendMessageWithAttachedFile();
    }

    @Then("verify attached file appears in chat")
    public void verifyAttachedFileAppearsInChat() {
        accountActions.verifyAttachedFileInChat();
    }

    @Then("verify attached file can be downloaded")
    public void verifyAttachedFileCanBeDownloaded() {
        accountActions.verifyAttachedFileDownload();
    }

    @When("user tries to attach unsupported {string} file")
    public void userTriesToAttachUnsupportedFile(String fileName) {
        String message = "Automation test message with unsupported file";
        accountActions.attachUnsupportedFile(message, fileName);
    }

    @Then("verify invalid file type error is displayed")
    public void verifyInvalidFileTypeErrorIsDisplayed() {
        accountActions.verifyInvalidFileTypeError();
    }

    @Given("cleanup any downloaded test files")
    public void cleanupAnyDownloadedTestFiles() {
        accountActions.cleanupDownloadedTestFiles();
    }

    @When("perform pre-test cleanup")
    public void performPreTestCleanup() {
        accountActions.performPreTestCleanup();
    }

    @Then("perform post-test cleanup")
    public void performPostTestCleanup() {
        accountActions.performPostTestCleanup();
    }
    @When("user attempts to submit ticket request without selecting reason and without entering message")
    public void userAttemptsToSubmitTicketRequestWithoutSelectingReasonAndWithoutEnteringMessage() {
        accountActions.attemptToSubmitEmptyTicketRequest();
    }

    @Then("verify empty message field error is displayed")
    public void verifyEmptyMessageFieldErrorIsDisplayed() {
        accountActions.verifyEmptyMessageFieldError();
    }

    @Then("verify reason for contact not selected error is displayed")
    public void verifyReasonForContactNotSelectedErrorIsDisplayed() {
        accountActions.verifyReasonForContactNotSelectedError();
    }
    @When("user enters company billing address data without company name")
    public void userEntersCompanyBillingAddressDataWithoutCompanyName() {
        accountActions.enterCompanyBillingAddressDataWithoutCompanyName();
    }
    @Then("required field error should be displayed for company name")
    public void requiredFieldErrorShouldBeDisplayedForCompanyName() {
        accountActions.verifyRequiredFieldErrorForCompanyName();
    }
    @Then("verify shipping address can be saved after fixing errors")
    public void verifyShippingAddressCanBeSavedAfterFixingErrors() {
        accountActions.verifyShippingAddressCanBeSavedAfterFixingErrors();
    }

    @Then("verify personal billing address can be saved after fixing errors")
    public void verifyPersonalBillingAddressCanBeSavedAfterFixingErrors() {
        accountActions.verifyPersonalBillingAddressCanBeSavedAfterFixingErrors();
    }

    @Then("verify company billing address can be saved after fixing errors")
    public void verifyCompanyBillingAddressCanBeSavedAfterFixingErrors() {
        accountActions.verifyCompanyBillingAddressCanBeSavedAfterFixingErrors();
    }
    @When("user enters company billing address data without phone number")
    public void userEntersCompanyBillingAddressDataWithoutPhoneNumber() {
        accountActions.enterCompanyBillingAddressDataWithoutPhone();
    }

    @When("user enters personal billing address data without phone number")
    public void userEntersPersonalBillingAddressDataWithoutPhoneNumber() {
        accountActions.enterPersonalBillingAddressDataWithoutPhone();
    }

    @When("user enters company billing address data with phone number")
    public void userEntersCompanyBillingAddressDataWithPhoneNumber() {
        accountActions.enterCompanyBillingAddressDataWithPhone();
    }

    @When("user enters personal billing address data with phone number")
    public void userEntersPersonalBillingAddressDataWithPhoneNumber() {
        accountActions.enterPersonalBillingAddressDataWithPhone();
    }

    @Then("verify company billing address can be saved without phone number")
    public void verifyCompanyBillingAddressCanBeSavedWithoutPhoneNumber() {
        accountActions.verifyCompanyBillingAddressCanBeSavedWithoutPhone();
    }

    @Then("verify personal billing address can be saved without phone number")
    public void verifyPersonalBillingAddressCanBeSavedWithoutPhoneNumber() {
        accountActions.verifyPersonalBillingAddressCanBeSavedWithoutPhone();
    }

    @Then("verify company billing address can be saved with phone number")
    public void verifyCompanyBillingAddressCanBeSavedWithPhoneNumber() {
        accountActions.verifyCompanyBillingAddressCanBeSavedWithPhone();
    }

    @Then("verify personal billing address can be saved with phone number")
    public void verifyPersonalBillingAddressCanBeSavedWithPhoneNumber() {
        accountActions.verifyPersonalBillingAddressCanBeSavedWithPhone();
    }

    @When("user enters company billing address data without NIP")
    public void userEntersCompanyBillingAddressDataWithoutNip() {
        accountActions.enterCompanyBillingAddressDataWithoutNip();
    }

    @When("user enters company billing address data without address")
    public void userEntersCompanyBillingAddressDataWithoutAddress() {
        accountActions.enterCompanyBillingAddressDataWithoutAddress();
    }

    @When("user enters company billing address data without city")
    public void userEntersCompanyBillingAddressDataWithoutCity() {
        accountActions.enterCompanyBillingAddressDataWithoutCity();
    }

    @When("user enters company billing address data without post code")
    public void userEntersCompanyBillingAddressDataWithoutPostCode() {
        accountActions.enterCompanyBillingAddressDataWithoutPostCode();
    }

    @Then("required field error should be displayed for NIP")
    public void requiredFieldErrorShouldBeDisplayedForNip() {
        accountActions.verifyRequiredFieldErrorForNip();
    }

    // ========== INVALID PHONE NUMBER PREFIX VALIDATION STEP DEFINITIONS ==========

    @When("user enters shipping address data with invalid phone number prefix")
    public void userEntersShippingAddressDataWithInvalidPhoneNumberPrefix() {
        accountActions.enterShippingAddressDataWithInvalidPhonePrefix();
    }

    @When("user enters personal billing address data with invalid phone number prefix")
    public void userEntersPersonalBillingAddressDataWithInvalidPhoneNumberPrefix() {
        accountActions.enterPersonalBillingAddressDataWithInvalidPhonePrefix();
    }

    @When("user enters company billing address data with invalid phone number prefix")
    public void userEntersCompanyBillingAddressDataWithInvalidPhoneNumberPrefix() {
        accountActions.enterCompanyBillingAddressDataWithInvalidPhonePrefix();
    }

    @Then("verify invalid phone number error is displayed for shipping address")
    public void verifyInvalidPhoneNumberErrorIsDisplayedForShippingAddress() {
        accountActions.verifyInvalidPhoneNumberErrorForShippingAddress();
    }

    @Then("verify invalid phone number error is displayed for billing address")
    public void verifyInvalidPhoneNumberErrorIsDisplayedForBillingAddress() {
        accountActions.verifyInvalidPhoneNumberErrorForBillingAddress();
    }
}
