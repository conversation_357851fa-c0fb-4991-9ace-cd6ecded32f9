package steps.country_language_steps;

import actions.CountryAndLanguageActions;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import net.serenitybdd.annotations.Steps;


public class CountryAndLanguageSteps {
    @Steps
    private CountryAndLanguageActions countryAndLanguageActions;

    @When("user opens country selection modal")
    public void userOpensCountrySelectionModal() {
        countryAndLanguageActions.openCountrySelectionModal();
    }

    @When("user selects {string} country")
    public void userSelectsCountry(String countryName) {
        countryAndLanguageActions.selectCountry(countryName);
    }

    @When("user selects {string} language")
    public void userSelectsLanguage(String languageName) {
        countryAndLanguageActions.selectLanguage(languageName);
    }

    @When("user clicks save and go to shop")
    public void userClicksSaveAndGoToShop() {
        countryAndLanguageActions.saveAndGoToShop();
    }

    @When("user closes country selection modal")
    public void userClosesCountrySelectionModal() {
        countryAndLanguageActions.closeCountrySelectionModal();
    }

    @When("user selects {string} country with {string} language")
    public void userSelectsCountryWithLanguage(String countryName, String languageName) {
        countryAndLanguageActions.selectCountryAndLanguage(countryName, languageName);
    }

    @Then("verify country selection modal is open")
    public void verifyCountrySelectionModalIsOpen() {
        countryAndLanguageActions.verifyCountrySelectionModalIsOpen();
    }

    @Then("verify country selection modal is closed")
    public void verifyCountrySelectionModalIsClosed() {
        countryAndLanguageActions.verifyCountrySelectionModalIsClosed();
    }

    @Then("verify country selection modal header")
    public void verifyCountrySelectionModalHeader() {
        countryAndLanguageActions.verifyCountrySelectionModalHeader();
    }

    @Then("verify country selection modal description")
    public void verifyCountrySelectionModalDescription() {
        countryAndLanguageActions.verifyCountrySelectionModalDescription();
    }

    @Then("verify language dropdown is enabled")
    public void verifyLanguageDropdownIsEnabled() {
        countryAndLanguageActions.verifyLanguageDropdownIsEnabled();
    }

    @Then("verify language dropdown is disabled")
    public void verifyLanguageDropdownIsDisabled() {
        countryAndLanguageActions.verifyLanguageDropdownIsDisabled();
    }

    @Then("verify go to store button is disabled")
    public void verifyGoToStoreButtonIsDisabled() {
        countryAndLanguageActions.verifyGoToStoreButtonIsDisabled();
    }

    @Then("verify country name {string} is displayed in header")
    public void verifyCountryNameIsDisplayedInHeader(String expectedCountryName) {
        countryAndLanguageActions.verifyCountryNameInHeader(expectedCountryName);
    }

    @Then("verify URL contains country code {string} and language code {string}")
    public void verifyUrlContainsCountryCodeAndLanguageCode(String countryCode, String languageCode) {
        countryAndLanguageActions.verifyUrlContainsCountryAndLanguageCodes(countryCode, languageCode);
    }

    @Then("verify footer displays {string}")
    public void verifyFooterDisplays(String expectedFooterText) {
        countryAndLanguageActions.verifyFooterDisplay(expectedFooterText);
    }

    @Then("verify {string} country is selected with {string} language")
    public void verifyCountryIsSelectedWithLanguage(String countryName, String languageName) {
        countryAndLanguageActions.verifyCountryAndLanguageSelection(countryName, languageName);
    }

    @When("save product SKU from PDP")
    public void saveProductSkuFromPdp() {
        countryAndLanguageActions.saveProductSkuFromPdp();
    }

    @Then("verify product SKU matches in cart")
    public void verifyProductSkuMatchesInCart() {
        countryAndLanguageActions.verifyProductSkuMatchesInCart();
    }

    @Then("verify go to store button is enabled")
    public void verifyGoToStoreButtonIsEnabled() {
        countryAndLanguageActions.verifyGoToStoreButtonIsEnabled();
    }
}
