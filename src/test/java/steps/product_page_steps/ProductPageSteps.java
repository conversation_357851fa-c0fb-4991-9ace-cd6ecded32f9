package steps.product_page_steps;

import actions.ProductListPageActions;
import actions.ProductPageActions;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import net.serenitybdd.annotations.Steps;

public class ProductPageSteps {
    @Steps
    private ProductPageActions productPageActions;
    @Steps
    private ProductListPageActions productListPageActions;

    @And("user adds the product to cart")
    public void userAddsTheProductToCart() {
        productPageActions.addTheProductToCart();
    }

    @Then("user should be on product detail page")
    public void userShouldBeOnProductDetailPage() {
        productPageActions.verifyProductDetailPage();
    }

    @Then("verify product image is displayed")
    public void verifyProductImageIsDisplayed() {
        productPageActions.verifyProductImageIsDisplayed();
    }

    @Then("verify delivery price header shows {string}")
    public void verifyDeliveryPriceHeaderShows(String expectedDeliveryPrice) {
        productPageActions.verifyDeliveryPriceHeaderShows(expectedDeliveryPrice);
    }

    @Then("verify dimension and details button is displayed")
    public void verifyDimensionAndDetailsButtonIsDisplayed() {
        productPageActions.verifyDimensionAndDetailsButtonIsDisplayed();
    }

    @When("user clicks on dimension and details button")
    public void userClicksOnDimensionAndDetailsButton() {
        productPageActions.clickOnDimensionAndDetailsButton();
    }

    @Then("verify dimension and details header shows {string}")
    public void verifyDimensionAndDetailsHeaderShows(String expectedHeader) {
        productPageActions.verifyDimensionAndDetailsHeaderShows(expectedHeader);
    }

    @And("verify dimension and details content is displayed with correct information")
    public void verifyDimensionAndDetailsContentIsDisplayedWithCorrectInformation() {
        productPageActions.verifyDimensionAndDetailsContentIsDisplayed();
    }

    @Then("verify product added view cart button is displayed")
    public void verifyProductAddedViewCartButtonIsDisplayed() {
        productPageActions.verifyProductAddedViewCartButtonIsDisplayed();
    }

    @When("user clicks on product added view cart button")
    public void userClicksOnProductAddedViewCartButton() {
        productPageActions.clickOnProductAddedViewCartButton();
    }

    @Then("verify cart is opened")
    public void verifyCartIsOpened() {
        productPageActions.verifyCartIsOpened();
    }

    @Then("verify product amount input shows {string}")
    public void verifyProductAmountInputShows(String expectedAmount) {
        productPageActions.verifyProductAmountInputShows(expectedAmount);
    }

    @When("user clicks plus button randomly between {int} and {int} times")
    public void userClicksPlusButtonRandomlyBetweenAndTimes(int minClicks, int maxClicks) {
        productPageActions.clickPlusButtonRandomly(minClicks, maxClicks);
    }

    @Then("verify product quantity in cart matches the selected amount")
    public void verifyProductQuantityInCartMatchesTheSelectedAmount() {
        productPageActions.verifyProductQuantityInCartMatchesSelectedAmount();
    }

    @Then("verify product amount input is updated correctly")
    public void verifyProductAmountInputIsUpdatedCorrectly() {
        productPageActions.verifyProductAmountInputIsUpdatedCorrectly();
    }

    @When("user clicks minus button randomly between {int} and {int} times")
    public void userClicksMinusButtonRandomlyBetweenAndTimes(int minClicks, int maxClicks) {
        productPageActions.clickMinusButtonRandomly(minClicks, maxClicks);
    }

    @Then("verify product amount input is updated correctly after minus clicks")
    public void verifyProductAmountInputIsUpdatedCorrectlyAfterMinusClicks() {
        productPageActions.verifyProductAmountInputIsUpdatedCorrectlyAfterMinusClicks();
    }

    @When("user clicks minus button {int} times")
    public void userClicksMinusButtonTimes(int times) {
        productPageActions.clickMinusButtonTimes(times);
    }

    @When("user clicks plus button {int} times")
    public void userClicksPlusButtonTimes(int times) {
        productPageActions.clickPlusButtonTimes(times);
    }

    @Then("verify product amount input does not exceed reasonable limit")
    public void verifyProductAmountInputDoesNotExceedReasonableLimit() {
        productPageActions.verifyProductAmountInputDoesNotExceedReasonableLimit();
    }

    @And("verify plus button behavior at maximum quantity")
    public void verifyPlusButtonBehaviorAtMaximumQuantity() {
        productPageActions.verifyPlusButtonBehaviorAtMaximumQuantity();
    }

    @And("verify maximum quantity error message is displayed")
    public void verifyMaximumQuantityErrorMessageIsDisplayed() {
        productPageActions.verifyMaximumQuantityErrorMessageIsDisplayed();
    }

    @And("verify product quantity in cart matches the maximum allowed quantity")
    public void verifyProductQuantityInCartMatchesTheMaximumAllowedQuantity() {
        productPageActions.verifyProductQuantityInCartMatchesMaximumAllowedQuantity();
    }

    @And("save product details to storage")
    public void saveProductDetailsToStorage() {
        productListPageActions.saveProductDetailsToStorage();
    }

    @When("user clicks on ask question button")
    public void userClicksOnAskQuestionButton() {
        productPageActions.clickOnAskQuestionButton();
    }

    @When("user enters email {string} for question")
    public void userEntersEmailForQuestion(String email) {
        productPageActions.enterEmailForQuestion(email);
    }

    @When("user enters message {string} for question")
    public void userEntersMessageForQuestion(String message) {
        productPageActions.enterMessageForQuestion(message);
    }

    @When("user checks agree to privacy policy checkbox")
    public void userChecksAgreeToPrivacyPolicyCheckbox() {
        productPageActions.checkAgreeToPrivacyPolicyCheckbox();
    }

    @When("user clicks send message button")
    public void userClicksSendMessageButton() {
        productPageActions.clickSendMessageButton();
    }

    @Then("verify message sent confirmation is displayed")
    public void verifyMessageSentConfirmationIsDisplayed() {
        productPageActions.verifyMessageSentConfirmationIsDisplayed();
    }

    @Then("verify error message for unselected privacy policy checkbox shows {string}")
    public void verifyErrorMessageForUnselectedPrivacyPolicyCheckboxShows(String expectedErrorMessage) {
        productPageActions.verifyErrorMessageForUnselectedPrivacyPolicyCheckbox(expectedErrorMessage);
    }

    @When("user enters random email for question")
    public void userEntersRandomEmailForQuestion() {
        productPageActions.enterRandomEmailForQuestion();
    }

    @When("user types quantity {string} in input field")
    public void userTypesQuantityInInputField(String quantity) {
        productPageActions.typeQuantityInInput(quantity);
    }

    @Then("verify product quantity input shows {string}")
    public void verifyProductQuantityInputShows(String expectedQuantity) {
        productPageActions.verifyProductQuantityInputShowsValue(expectedQuantity);
    }

    @Then("verify maximum quantity error message is displayed for input")
    public void verifyMaximumQuantityErrorMessageIsDisplayedForInput() {
        productPageActions.verifyMaximumQuantityErrorMessageForInput();
    }

    @When("user clicks minus button after typing quantity")
    public void userClicksMinusButtonAfterTypingQuantity() {
        productPageActions.clickMinusButtonAfterTypingQuantity();
    }

    @When("user clicks plus button after typing quantity")
    public void userClicksPlusButtonAfterTypingQuantity() {
        productPageActions.clickPlusButtonAfterTypingQuantity();
    }

    @Then("verify quantity is updated correctly after button click")
    public void verifyQuantityIsUpdatedCorrectlyAfterButtonClick() {
        productPageActions.verifyQuantityAfterButtonClick();
    }

    @Then("verify related products header shows {string}")
    public void verifyRelatedProductsHeaderShows(String expectedHeader) {
        productPageActions.verifyRelatedProductsHeader();
    }

    @Then("verify related products are listed")
    public void verifyRelatedProductsAreListed() {
        productPageActions.verifyRelatedProductsAreListed();
    }

    @Then("verify related product prices are displayed")
    public void verifyRelatedProductPricesAreDisplayed() {
        productPageActions.verifyRelatedProductPricesAreDisplayed();
    }

    @Then("verify related product names are displayed")
    public void verifyRelatedProductNamesAreDisplayed() {
        productPageActions.verifyRelatedProductNamesAreDisplayed();
    }

    @Then("verify breadcrumbs are displayed")
    public void verifyBreadcrumbsAreDisplayed() {
        productPageActions.verifyBreadcrumbsAreDisplayed();
    }

    @Then("verify breadcrumb links navigation works correctly")
    public void verifyBreadcrumbLinksNavigationWorksCorrectly() {
        productPageActions.verifyBreadcrumbLinksNavigation();
    }

    @Then("verify product SKU is displayed")
    public void verifyProductSkuIsDisplayed() {
        productPageActions.verifyProductSkuIsDisplayed();
    }

    @Then("verify includes VAT text shows {string}")
    public void verifyIncludesVatTextShows(String expectedText) {
        productPageActions.verifyIncludesVatTextShows(expectedText);
    }

    @When("user clicks on cost without delivery link")
    public void userClicksOnCostWithoutDeliveryLink() {
        productPageActions.clickOnCostWithoutDeliveryLink();
    }

    @Then("verify product description is displayed and contains content")
    public void verifyProductDescriptionIsDisplayedAndContainsContent() {
        productPageActions.verifyProductDescriptionIsDisplayedAndContainsContent();
    }

    @When("user clicks on delivery details link in product description")
    public void userClicksOnDeliveryDetailsLinkInProductDescription() {
        productPageActions.clickOnDeliveryDetailsLinkInProductDescription();
    }

    @Then("verify graphic dimension detail is displayed")
    public void verifyGraphicDimensionDetailIsDisplayed() {
        productPageActions.verifyGraphicDimensionDetailIsDisplayed();
    }

    @When("user clicks on question and answers button")
    public void userClicksOnQuestionAndAnswersButton() {
        productPageActions.clickOnQuestionAndAnswersButton();
    }

    @Then("verify question and answers section displays correct count and content")
    public void verifyQuestionAndAnswersSectionDisplaysCorrectCountAndContent() {
        productPageActions.verifyQuestionAndAnswersSectionDisplaysCorrectCountAndContent();
    }

    @Then("verify question and answers list items are expandable and closable when clicked")
    public void verifyQuestionAndAnswersListItemsAreExpandableAndClosableWhenClicked() {
        productPageActions.verifyQuestionAndAnswersListItemsAreExpandableAndClosableWhenClicked();
    }
}
