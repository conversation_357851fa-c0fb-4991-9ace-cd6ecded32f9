package steps.products_list_page;

import actions.ProductListPageActions;
import actions.ProductPageActions;
import io.cucumber.java.en.And;

public class ProductsListPageSteps {
    private ProductListPageActions productListPageActions;
    private ProductPageActions productPageActions;

    @And("opens the first product")
    public void opensTheFirstProduct() {
        productListPageActions.openTheFirstProduct();
    }

    @And("user opens the first product")
    public void userOpensTheFirstProduct() {
        productListPageActions.openTheFirstProduct();
    }
    @And("user opens the second product")
    public void userOpensTheSecondProduct() {
        productListPageActions.openTheSecondProduct();
    }

}
