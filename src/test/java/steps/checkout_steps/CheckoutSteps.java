package steps.checkout_steps;

import actions.CheckoutActions;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import net.serenitybdd.annotations.Steps;

public class CheckoutSteps {
    @Steps
    CheckoutActions checkoutActions;

    @Then("user should see that user is on the payment page as a {string}")
    public void userShouldSeeThatUserIsOnThePaymentPageAsA(String userType) {
        checkoutActions.verifyThatUserIsOnThePaymentPage(userType);
    }

    @And("user fills recipient information as {string}")
    public void userFillsRecipientInformationAs(String recipientType) {
        checkoutActions.fillInShippingData(recipientType);
    }

    @And("click on continue button")
    public void clickOnContinueButton() {
        checkoutActions.clickOnContinueButton();
    }

    @And("user selects delivery method {string}")
    public void userSelectsDeliveryMethod(String deliveryMethod) {
        checkoutActions.selectDeliveryMethod(deliveryMethod);
    }

    @And("user agrees to terms and conditions")
    public void userAgreesToTermsAndConditions() {
        checkoutActions.clickOnAgreeToTermsCheckbox();
    }

    @And("user chooses to pay with {string}")
    public void userChoosesToPayWith(String paymentMethod) {
        checkoutActions.chooseToPayWith(paymentMethod);
    }

    @Then("verify order confirmation page is displayed")
    public void verifyOrderConfirmationPageIsDisplayed() {
        checkoutActions.verifyOrderSuccessPage();
    }

    @Then("verify order is not created")
    public void verifyOrderIsNotCreated() {
        checkoutActions.verifyOrderIsNotCreated();
    }

    @And("verify the delivery cost is zero at checkout")
    public void verifyTheDeliveryCostIsZeroAtCheckout() {
        checkoutActions.verifyDeliveryCostIsZeroAtCheckout();
    }

    @And("verify the delivery cost is zero on the right side of {string}")
    public void verifyTheDeliveryCostIsZeroOnTheRightSideOf(String deliveryMethod) {
        checkoutActions.verifyDeliveryCostIsZeroOnTheRightSideOf(deliveryMethod);
    }

    @And("click on the same as delivery checkbox")
    public void clickOnTheSameAsDeliveryCheckbox() {
        checkoutActions.clickOnTheSameAsDeliveryCheckbox();
    }

    @And("user fills billing information as {string}")
    public void userFillsBillingInformationAs(String billingType) {
        checkoutActions.fillInBillingData(billingType);
    }

    @And("user enters invalid blik code")
    public void userEntersInvalidBlikCode() {
        checkoutActions.enterInvalidBlikCode();
    }

    @And("verify error message is displayed indicating invalid blik code")
    public void verifyErrorMessageIsDisplayedIndicatingInvalidBlikCode() {
        checkoutActions.verifyErrorMessageForInvalidBlikCode();
    }

    @And("user selects payment method {string}")
    public void userSelectsPaymentMethod(String paymentMethod) {
        checkoutActions.selectPaymentMethod(paymentMethod);
    }

    @And("user selects online bank payment option")
    public void userSelectsOnlineBankPaymentOption() {
        checkoutActions.selectOnlineBankPaymentOption();
    }

    @And("navigate back to checkout page")
    public void navigateBackToCheckoutPage() {
        checkoutActions.navigateBackToCheckoutPage();
    }

    @And("user enters invalid card number")
    public void userEntersInvalidCardNumber() {
        checkoutActions.payWithInvalidCardNumber();
    }

    @And("verify error message is displayed indicating invalid card number")
    public void verifyErrorMessageIsDisplayedIndicatingInvalidCardNumber() {
        checkoutActions.verifyErrorMessageForInvalidCardNumber();
    }

    @And("click on change order details button")
    public void clickOnChangeOrderDetailsButton() {
        checkoutActions.clickOnChangeOrderDetailsButton();
    }

    @And("select one saved shipping address from the list")
    public void selectOneSavedShippingAddressFromTheList() {
        checkoutActions.selectOneSavedShippingAddressFromTheList();
    }

    @And("select one saved billing address from the list as a {string}")
    public void selectOneSavedBillingAddressFromTheList(String customerType) {
        checkoutActions.selectOneSavedBillingAddressFromTheList(customerType);

    }

    @Then("verify the shipping and billing addresses are the same")
    public void verifyTheShippingAndBillingAddressesAreTheSame() {
        checkoutActions.verifyTheShippingAndBillingAddressesAreTheSame();
    }

    @And("uncheck the same as delivery checkbox")
    public void uncheckTheSameAsDeliveryCheckbox() {
        checkoutActions.uncheckTheSameAsDeliveryCheckbox();
    }

    @Then("verify the shipping and billing addresses are different")
    public void verifyTheShippingAndBillingAddressesAreDifferent() {
        checkoutActions.verifyTheShippingAndBillingAddressesAreDifferent();
    }

    @And("verify the newly selected shipping address is different from the previous one")
    public void verifyTheNewlySelectedShippingAddressIsDifferentFromThePreviousOne() {
        checkoutActions.verifyTheNewlySelectedShippingAddressIsDifferentFromThePreviousOne();
    }

    @And("verify the newly selected billing address is different from the previous one")
    public void verifyTheNewlySelectedBillingAddressIsDifferentFromThePreviousOne() {
        checkoutActions.verifyTheNewlySelectedBillingAddressIsDifferentFromThePreviousOne();
    }

    @And("verify the newly selected shipping and billing addresses are different from the previous ones")
    public void verifyTheNewlySelectedShippingAndBillingAddressesAreDifferentFromThePreviousOnes() {
        checkoutActions.verifyTheNewlySelectedShippingAndBillingAddressesAreDifferentFromThePreviousOnes();
    }

    @And("check the same as delivery checkbox")
    public void checkTheSameAsDeliveryCheckbox() {
        checkoutActions.checkTheSameAsDeliveryCheckbox();
    }

    @And("click on add new shipping address button")
    public void clickOnAddNewShippingAddressButton() {
        checkoutActions.clickOnAddNewShippingAddressButton();
    }

    @And("user adds new shipping address as {string}")
    public void userAddsNewShippingAddressAs(String customerType) {
        checkoutActions.addNewShippingAddress(customerType);
    }

    @And("click on add new billing address button")
    public void clickOnAddNewBillingAddressButton() {
        checkoutActions.clickOnAddNewBillingAddressButton();
    }

    @And("user adds new billing address as {string}")
    public void userAddsNewBillingAddressAs(String customerType) {
        checkoutActions.addNewBillingAddress(customerType);
    }

    @And("select different country for billing address")
    public void selectDifferentCountryForBillingAddress() {
        checkoutActions.selectDifferentCountryForBillingAddress();
    }

    @And("user adds new billing address as {string} with a different country")
    public void userAddsNewBillingAddressAsWithADifferentCountry(String customerType) {
        checkoutActions.addNewBillingAddressWithDifferentCountry(customerType);
    }

    @And("user applies coupon code {string} at checkout")
    public void userAppliesCouponCodeAtCheckout(String couponCode) {
        checkoutActions.applyCouponCodeAtCheckout(couponCode);
    }

    @Then("verify coupon code is applied successfully at checkout")
    public void verifyCouponCodeIsAppliedSuccessfullyAtCheckout() {
        checkoutActions.verifyCouponCodeIsAppliedAtCheckout();
    }

    @Then("verify coupon code error message is displayed at checkout")
    public void verifyCouponCodeErrorMessageIsDisplayedAtCheckout() {
        checkoutActions.verifyCouponCodeErrorAtCheckout();
    }

    @Then("verify coupon code is preserved at checkout")
    public void verifyCouponCodeIsPreservedAtCheckout() {
        checkoutActions.verifyCouponCodeIsPreservedAtCheckout();
    }

    @And("verify discount amount is displayed correctly at checkout")
    public void verifyDiscountAmountIsDisplayedCorrectlyAtCheckout() {
        checkoutActions.verifyDiscountAmountIsDisplayedCorrectlyAtCheckout();
    }

    @Then("verify subtotal is calculated correctly at checkout")
    public void verifySubtotalIsCalculatedCorrectlyAtCheckout() {
        checkoutActions.verifySubtotalIsCalculatedCorrectlyAtCheckout();
    }

    @And("verify final total is calculated correctly at checkout")
    public void verifyFinalTotalIsCalculatedCorrectlyAtCheckout() {
        checkoutActions.verifyFinalTotalIsCalculatedCorrectlyAtCheckout();
    }

    @Then("verify no coupon code is applied at checkout")
    public void verifyNoCouponCodeIsAppliedAtCheckout() {
        checkoutActions.verifyNoCouponCodeIsAppliedAtCheckout();
    }

    @And("verify subtotal equals final total at checkout")
    public void verifySubtotalEqualsFinalTotalAtCheckout() {
        checkoutActions.verifySubtotalEqualsFinalTotalAtCheckout();
    }

    @And("user goes back to delivery method selection")
    public void userGoesBackToDeliveryMethodSelection() {
        checkoutActions.goBackToDeliveryMethodSelection();
    }

    @And("user removes coupon code at checkout")
    public void userRemovesCouponCodeAtCheckout() {
        checkoutActions.removeCouponCodeAtCheckout();
    }

    @Then("verify coupon code is removed at checkout")
    public void verifyCouponCodeIsRemovedAtCheckout() {
        checkoutActions.verifyCouponCodeIsRemovedAtCheckout();
    }

    @And("user clicks on login link at checkout")
    public void userClicksOnLoginLinkAtCheckout() {
        checkoutActions.clickOnLoginLinkAtCheckout();
    }

    @And("user logs in at checkout with account test credentials")
    public void userLogsInAtCheckoutWithAccountTestCredentials() {
        checkoutActions.loginAtCheckoutWithAccountTestCredentials();
    }

    @Then("verify user is logged in at checkout")
    public void verifyUserIsLoggedInAtCheckout() {
        checkoutActions.verifyUserIsLoggedInAtCheckout();
    }

    @And("verify saved shipping addresses are listed")
    public void verifySavedShippingAddressesAreListed() {
        checkoutActions.verifySavedShippingAddressesAreListed();
    }

    @And("verify saved billing addresses are listed")
    public void verifySavedBillingAddressesAreListed() {
        checkoutActions.verifySavedBillingAddressesAreListed();
    }


    @And("user fills shipping form with empty {string} field")
    public void userFillsShippingFormWithEmptyField(String fieldName) {
        switch (fieldName.toLowerCase()) {
            case "email" -> checkoutActions.fillShippingFormWithEmptyEmail();
            case "first name" -> checkoutActions.fillShippingFormWithEmptyFirstName();
            case "last name" -> checkoutActions.fillShippingFormWithEmptyLastName();
            case "street address" -> checkoutActions.fillShippingFormWithEmptyStreetAddress();
            case "city" -> checkoutActions.fillShippingFormWithEmptyCity();
            case "postal code" -> checkoutActions.fillShippingFormWithEmptyPostalCode();
            case "phone number" -> checkoutActions.fillShippingFormWithEmptyPhoneNumber();
            default -> throw new IllegalArgumentException("Unknown field: " + fieldName);
        }
    }

    @And("user fills shipping form with invalid {string}")
    public void userFillsShippingFormWithInvalidField(String fieldName) {
        switch (fieldName.toLowerCase()) {
            case "phone number" -> checkoutActions.fillShippingFormWithInvalidPhoneNumber();
            case "postal code" -> checkoutActions.fillShippingFormWithInvalidPostalCode();
            default -> throw new IllegalArgumentException("Unknown field for invalid data: " + fieldName);
        }
    }

    @And("user fills complete shipping form with valid data")
    public void userFillsCompleteShippingFormWithValidData() {
        checkoutActions.fillCompleteShippingFormWithValidData();
    }

    @Then("verify coupon code is preserved at initial checkout step")
    public void verifyCouponCodeIsPreservedAtInitialCheckoutStep() {
        checkoutActions.verifyCouponCodeIsPreservedAtCheckout();
    }

    @Then("verify coupon code is preserved after address form completion")
    public void verifyCouponCodeIsPreservedAfterAddressFormCompletion() {
        checkoutActions.verifyCouponCodeIsPreservedAtCheckout();
    }

    @Then("verify coupon code is preserved after delivery method selection")
    public void verifyCouponCodeIsPreservedAfterDeliveryMethodSelection() {
        checkoutActions.verifyCouponCodeIsPreservedAtCheckout();
    }

    @Then("verify coupon code is preserved after login at checkout")
    public void verifyCouponCodeIsPreservedAfterLoginAtCheckout() {
        checkoutActions.verifyCouponCodeIsPreservedAtCheckout();
    }

    @Then("verify all payment icons are displayed at checkout")
    public void verifyAllPaymentIconsAreDisplayedAtCheckout() {
        checkoutActions.verifyAllPaymentIconsAreDisplayedAtCheckout();
    }

    @Then("verify user can select all payment methods")
    public void verifyUserCanSelectAllPaymentMethods() {
        checkoutActions.verifyAllPaymentMethodsCanBeSelected();
    }

    @Then("verify discount value is updated correctly after quantity change")
    public void verifyDiscountValueIsUpdatedCorrectlyAfterQuantityChange() {
        checkoutActions.verifyDiscountValueIsUpdatedCorrectlyAfterQuantityChange();
    }

    @And("user selects Apple Pay payment method")
    public void userSelectsApplePayPaymentMethod() {
        checkoutActions.selectApplePayPaymentMethod();
    }

    @And("user clicks on Apple Pay button")
    public void userClicksOnApplePayButton() {
        checkoutActions.clickApplePayButton();
    }

    @And("user cancels the Apple Pay payment window")
    public void userCancelsTheApplePayPaymentWindow() {
        checkoutActions.cancelApplePayPaymentWindow();
    }

    @And("user selects Google Pay payment method")
    public void userSelectsGooglePayPaymentMethod() {
        checkoutActions.selectGooglePayPaymentMethod();
    }

    @And("user clicks on Google Pay button")
    public void userClicksOnGooglePayButton() {
        checkoutActions.clickGooglePayButton();
    }

    @And("user cancels the Google Pay payment window")
    public void userCancelsTheGooglePayPaymentWindow() {
        checkoutActions.cancelGooglePayPaymentWindow();
    }

    @Then("verify payment cancelled error message is displayed")
    public void verifyPaymentCancelledErrorMessageIsDisplayed() {
        checkoutActions.verifyPaymentCancelledErrorMessage();
    }

    @And("user fills complete billing form with valid data")
    public void userFillsCompleteBillingFormWithValidData() {
        checkoutActions.fillCompleteBillingFormWithValidData();
    }

    @And("user fills billing form with empty {string} field")
    public void userFillsBillingFormWithEmptyField(String fieldName) {
        switch (fieldName.toLowerCase()) {
            case "company name" -> checkoutActions.fillBillingFormWithEmptyCompanyName();
            case "street address" -> checkoutActions.fillBillingFormWithEmptyStreetAddress();
            case "city" -> checkoutActions.fillBillingFormWithEmptyCity();
            case "nip" -> checkoutActions.fillBillingFormWithEmptyNIP();
            case "postal code" -> checkoutActions.fillBillingFormWithEmptyPostalCode();
            default -> throw new IllegalArgumentException("Unknown billing field: " + fieldName);
        }
    }

    @And("user fills billing form with invalid {string}")
    public void userFillsBillingFormWithInvalidField(String fieldName) {
        if (fieldName.equalsIgnoreCase("postal code")) {
            checkoutActions.fillBillingFormWithInvalidPostalCode();
        } else {
            throw new IllegalArgumentException("Unknown billing field for invalid data: " + fieldName);
        }
    }

    @Then("verify empty required field error message is displayed")
    public void verifyEmptyRequiredFieldErrorMessageIsDisplayed() {
        checkoutActions.verifyEmptyRequiredFieldErrorMessage();
    }

    @Then("verify error message before continue button is displayed")
    public void verifyErrorMessageBeforeContinueButtonIsDisplayed() {
        checkoutActions.verifyErrorMessageBeforeContinueButton();
    }

    @Then("verify invalid postal code error message is displayed")
    public void verifyInvalidPostalCodeErrorMessageIsDisplayed() {
        checkoutActions.verifyInvalidPostalCodeErrorMessage();
    }

    @Then("verify invalid phone number error message is displayed")
    public void verifyInvalidPhoneNumberErrorMessageIsDisplayed() {
        checkoutActions.verifyInvalidPhoneNumberErrorMessage();
    }

    @Then("verify invalid email error message is displayed")
    public void verifyInvalidEmailErrorMessageIsDisplayed() {
        checkoutActions.verifyInvalidEmailErrorMessage();
    }

    @Then("verify continue button is disabled")
    public void verifyContinueButtonIsDisabled() {
        checkoutActions.verifyContinueButtonIsDisabled();
    }

    @Then("verify continue button is enabled")
    public void verifyContinueButtonIsEnabled() {
        checkoutActions.verifyContinueButtonIsEnabled();
    }

    @And("try to click continue button for validation")
    public void tryToClickContinueButtonForValidation() {
        checkoutActions.tryClickContinueButtonForValidation();
    }

    @And("user selects register at checkout checkbox")
    public void userSelectsRegisterAtCheckoutCheckbox() {
        checkoutActions.selectRegisterAtCheckoutCheckbox();
    }

    @And("user fills registration information at checkout")
    public void userFillsRegistrationInformationAtCheckout() {
        checkoutActions.fillRegistrationInformationAtCheckout();
    }

    @And("user fills registration form with invalid password {string}")
    public void userFillsRegistrationFormWithInvalidPassword(String password) {
        checkoutActions.fillRegistrationFormWithInvalidPassword(password);
    }

    @Then("verify invalid or empty password error message is displayed")
    public void verifyInvalidOrEmptyPasswordErrorMessageIsDisplayed() {
        checkoutActions.verifyInvalidOrEmptyPasswordErrorMessage();
    }

    @And("verify minimum password length label is displayed")
    public void verifyMinimumPasswordLengthLabelIsDisplayed() {
        checkoutActions.verifyMinimumPasswordLengthLabel();
    }

    @When("user fixes invalid password with valid password")
    public void userFixesInvalidPasswordWithValidPassword() {
        checkoutActions.fixInvalidPasswordWithValidPassword();
    }

    @Then("verify register at checkout checkbox is not displayed")
    public void verifyRegisterAtCheckoutCheckboxIsNotDisplayed() {
        checkoutActions.verifyRegisterAtCheckoutCheckboxIsNotDisplayed();
    }

    @Then("verify login link at checkout is not displayed")
    public void verifyLoginLinkAtCheckoutIsNotDisplayed() {
        checkoutActions.verifyLoginLinkAtCheckoutIsNotDisplayed();
    }

    @Then("verify shipping country selector is disabled")
    public void verifyShippingCountrySelectorIsDisabled() {
        checkoutActions.verifyShippingCountrySelectorIsDisabled();
    }

    @When("user clicks on product link at checkout")
    public void userClicksOnProductLinkAtCheckout() {
        checkoutActions.clickProductLinkAtCheckout();
    }

    @When("user clicks on show cart link at checkout")
    public void userClicksOnShowCartLinkAtCheckout() {
        checkoutActions.clickShowCartLinkAtCheckout();
    }

    @Then("verify user is on PDP from checkout")
    public void verifyUserIsOnPDPFromCheckout() {
        checkoutActions.verifyUserIsOnPDPFromCheckout();
    }

    @Then("verify user is on cart from checkout")
    public void verifyUserIsOnCartFromCheckout() {
        checkoutActions.verifyUserIsOnCartFromCheckout();
    }

    @Then("verify free shipping threshold progress bar shows correct percentage at checkout")
    public void verifyFreeShippingThresholdProgressBarShowsCorrectPercentageAtCheckout() {
        checkoutActions.verifyFreeShippingThresholdProgressBarShowsCorrectPercentageAtCheckout();
    }

    @Then("verify cart details are displayed correctly at checkout")
    public void verifyCartDetailsAreDisplayedCorrectlyAtCheckout() {
        checkoutActions.verifyCartDetailsAreDisplayedCorrectlyAtCheckout();
    }

    @Then("verify remaining amount for free shipping is correct at checkout")
    public void verifyRemainingAmountForFreeShippingIsCorrectAtCheckout() {
        checkoutActions.verifyRemainingAmountForFreeShippingIsCorrectAtCheckout();
    }
}
