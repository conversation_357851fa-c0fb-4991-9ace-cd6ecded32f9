package steps.sort_steps;

import actions.SortActions;
import common.constants.Constants;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;

public class SortSteps {
    private SortActions sortActions;

    @When("user applies {string} sort")
    public void userAppliesSort(String sortOption) {
        sortActions.applySortOption(sortOption);
    }

    @Then("verify {string} sort is applied")
    public void verifySortIsApplied(String sortOption) {
        sortActions.verifyAppliedSort(sortOption);
    }

    @Then("verify URL contains {string} sort parameter")
    public void verifyUrlContainsSortParameter(String urlParam) {
        sortActions.verifySortInUrl(urlParam);
    }

    @Then("verify products are sorted by price in ascending order")
    public void verifyProductsAreSortedByPriceInAscendingOrder() {
        sortActions.verifyPriceSortingAscending();
    }

    @Then("verify products are sorted by price in descending order")
    public void verifyProductsAreSortedByPriceInDescendingOrder() {
        sortActions.verifyPriceSortingDescending();
    }

    @Then("verify products are sorted by name in ascending order")
    public void verifyProductsAreSortedByNameInAscendingOrder() {
        sortActions.verifyNameSortingAscending();
    }

    @Then("verify products are sorted by name in descending order")
    public void verifyProductsAreSortedByNameInDescendingOrder() {
        sortActions.verifyNameSortingDescending();
    }

    @When("user navigates back to the previous page")
    public void userNavigatesBackToThePreviousPage() {
        sortActions.navigateBack();
    }

    // Specific sort option steps
    @When("user applies recommended sort")
    public void userAppliesRecommendedSort() {
        sortActions.applySortOption(Constants.SortOptions.RECOMMENDED);
    }

    @When("user applies price low to high sort")
    public void userAppliesPriceLowToHighSort() {
        sortActions.applySortOption(Constants.SortOptions.PRICE_LOW_TO_HIGH);
    }

    @When("user applies price high to low sort")
    public void userAppliesPriceHighToLowSort() {
        sortActions.applySortOption(Constants.SortOptions.PRICE_HIGH_TO_LOW);
    }

    @When("user applies name A to Z sort")
    public void userAppliesNameAToZSort() {
        sortActions.applySortOption(Constants.SortOptions.NAME_A_TO_Z);
    }

    @When("user applies name Z to A sort")
    public void userAppliesNameZToASort() {
        sortActions.applySortOption(Constants.SortOptions.NAME_Z_TO_A);
    }

    // Verification steps for specific sorts
    @Then("verify recommended sort is applied")
    public void verifyRecommendedSortIsApplied() {
        sortActions.verifyAppliedSort(Constants.SortOptions.RECOMMENDED);
    }

    @Then("verify price low to high sort is applied")
    public void verifyPriceLowToHighSortIsApplied() {
        sortActions.verifyAppliedSort(Constants.SortOptions.PRICE_LOW_TO_HIGH);
    }

    @Then("verify price high to low sort is applied")
    public void verifyPriceHighToLowSortIsApplied() {
        sortActions.verifyAppliedSort(Constants.SortOptions.PRICE_HIGH_TO_LOW);
    }

    @Then("verify name A to Z sort is applied")
    public void verifyNameAToZSortIsApplied() {
        sortActions.verifyAppliedSort(Constants.SortOptions.NAME_A_TO_Z);
    }

    @Then("verify name Z to A sort is applied")
    public void verifyNameZToASortIsApplied() {
        sortActions.verifyAppliedSort(Constants.SortOptions.NAME_Z_TO_A);
    }

    // URL parameter verification steps
    @Then("verify URL contains price ascending parameter")
    public void verifyUrlContainsPriceAscendingParameter() {
        sortActions.verifySortInUrl(Constants.SortUrlParams.PRICE_ASC);
    }

    @Then("verify URL contains price descending parameter")
    public void verifyUrlContainsPriceDescendingParameter() {
        sortActions.verifySortInUrl(Constants.SortUrlParams.PRICE_DESC);
    }

    @Then("verify URL contains name ascending parameter")
    public void verifyUrlContainsNameAscendingParameter() {
        sortActions.verifySortInUrl(Constants.SortUrlParams.NAME_ASC);
    }

    @Then("verify URL contains name descending parameter")
    public void verifyUrlContainsNameDescendingParameter() {
        sortActions.verifySortInUrl(Constants.SortUrlParams.NAME_DESC);
    }

    @And("user loads all available products after sorting")
    public void userLoadsAllAvailableProductsAfterSorting() {
        sortActions.clickSeeMoreProductsUntilAllLoaded();
    }
}
