package steps.category_steps;

import actions.CategoryPageActions;
import io.cucumber.java.en.And;

public class CategorySteps {
    private CategoryPageActions categoryPageActions;

    @And("user opens the first product from category page")
    public void userOpensTheFirstProductFromCategoryPage() {
        categoryPageActions.openProductFromCategoryPage();
    }

    @And("user loads all available products")
    public void userLoadsAllAvailableProducts() {
        categoryPageActions.loadAllProductsInCategory();
    }
}
