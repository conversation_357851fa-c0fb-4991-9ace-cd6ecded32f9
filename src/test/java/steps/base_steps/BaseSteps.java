package steps.base_steps;

import actions.BaseActions;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import net.serenitybdd.annotations.Steps;
import pages.BasePage;

public class BaseSteps {
    private BasePage basePage;
    @Steps
    private BaseActions baseActions;


    @Given("that user is on the {string}")
    public void thatUserIsOnThe(String pageName) {
        basePage.openPage(pageName);
    }

    @Then("verify user is on payment and delivery page")
    public void verifyUserIsOnPaymentAndDeliveryPage() {
        baseActions.verifyUserIsOnPaymentAndDeliveryPage();
    }

    @And("verify payment and delivery page content matches expected text")
    public void verifyPaymentAndDeliveryPageContentMatchesExpectedText() {
        baseActions.verifyPaymentAndDeliveryPageContentMatchesExpectedText();
    }
}
