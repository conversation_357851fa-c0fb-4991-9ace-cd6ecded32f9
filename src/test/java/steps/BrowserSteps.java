package steps;

import actions.BrowserActions;
import io.cucumber.java.en.And;
import io.cucumber.java.en.When;

public class BrowserSteps {
    private BrowserActions browserActions;

    @When("user returns to the previous category page")
    public void userReturnsToThePreviousCategoryPage() {
        browserActions.clickOnBrowserNavigationButton("back");
    }

    @And("user refreshes the page")
    public void userRefreshesThePage() {
        browserActions.refreshPage();
    }
}
