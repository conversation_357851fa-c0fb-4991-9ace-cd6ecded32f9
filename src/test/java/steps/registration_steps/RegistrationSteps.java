package steps.registration_steps;

import actions.RegistrationActions;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import net.serenitybdd.annotations.Steps;

public class RegistrationSteps {

    @Steps
    RegistrationActions registrationActions;

    @And("user enters valid registration information with first name last name email and password")
    public void userEntersValidRegistrationInformationWithFirstNameLastNameEmailAndPassword() {
        registrationActions.enterValidRegistrationInformationWithAllFields();
    }

    @And("user enters invalid email format for registration")
    public void userEntersInvalidEmailFormatForRegistration() {
        registrationActions.enterInvalidEmailFormatForRegistration();
    }

    @And("user enters valid email for registration")
    public void userEntersValidEmailForRegistration() {
        registrationActions.enterValidEmailForRegistration();
    }

    @And("user enters valid password for registration")
    public void userEntersValidPasswordForRegistration() {
        registrationActions.enterValidPasswordForRegistration();
    }

    @And("user leaves password field empty for registration")
    public void userLeavesPasswordFieldEmptyForRegistration() {
        registrationActions.leavePasswordFieldEmptyForRegistration();
    }

    @And("user enters short password for registration")
    public void userEntersShortPasswordForRegistration() {
        registrationActions.enterShortPasswordForRegistration();
    }

    @And("user leaves email field empty for registration")
    public void userLeavesEmailFieldEmptyForRegistration() {
        registrationActions.leaveEmailFieldEmptyForRegistration();
    }

    @And("user accepts the registration agreement")
    public void userAcceptsTheRegistrationAgreement() {
        registrationActions.acceptRegistrationAgreement();
    }

    @And("user does not accept the registration agreement")
    public void userDoesNotAcceptTheRegistrationAgreement() {
        registrationActions.doNotAcceptRegistrationAgreement();
    }

    @And("user clicks on registration button")
    public void userClicksOnRegistrationButton() {
        registrationActions.clickRegistrationButton();
    }


    @Then("verify registration success message header is displayed")
    public void verifyRegistrationSuccessMessageHeaderIsDisplayed() {
        registrationActions.verifyRegistrationSuccessMessageHeader();
    }

    @And("verify registration success message is displayed")
    public void verifyRegistrationSuccessMessageIsDisplayed() {
        registrationActions.verifyRegistrationSuccessMessage();
    }

    @When("user closes registration success modal")
    public void userClosesRegistrationSuccessModal() {
        registrationActions.closeRegistrationSuccessModal();
    }

    @Then("verify registration modal is closed")
    public void verifyRegistrationModalIsClosed() {
        registrationActions.verifyRegistrationModalIsClosed();
    }

    @Then("verify incorrect email format error message is displayed")
    public void verifyIncorrectEmailFormatErrorMessageIsDisplayed() {
        registrationActions.verifyIncorrectEmailFormatErrorMessage();
    }

    @Then("verify empty password error message is displayed for registration")
    public void verifyEmptyPasswordErrorMessageIsDisplayedForRegistration() {
        registrationActions.verifyEmptyPasswordErrorMessageForRegistration();
    }

    @Then("verify password too short error message is displayed for registration")
    public void verifyPasswordTooShortErrorMessageIsDisplayedForRegistration() {
        registrationActions.verifyPasswordTooShortErrorMessageForRegistration();
    }

    @Then("verify agreement checkbox not checked error message is displayed")
    public void verifyAgreementCheckboxNotCheckedErrorMessageIsDisplayed() {
        registrationActions.verifyAgreementCheckboxNotCheckedErrorMessage();
    }

    @Then("verify registration agreement checkbox label text is displayed")
    public void verifyRegistrationAgreementCheckboxLabelTextIsDisplayed() {
        registrationActions.verifyRegistrationAgreementCheckboxLabelText();
    }

    @Then("verify registration modal header is displayed")
    public void verifyRegistrationModalHeaderIsDisplayed() {
        registrationActions.verifyRegistrationModalHeaderIsDisplayed();
    }

    @And("verify registration first name input is displayed")
    public void verifyRegistrationFirstNameInputIsDisplayed() {
        registrationActions.verifyRegistrationFirstNameInputIsDisplayed();
    }

    @And("verify registration last name input is displayed")
    public void verifyRegistrationLastNameInputIsDisplayed() {
        registrationActions.verifyRegistrationLastNameInputIsDisplayed();
    }

    @And("verify registration email input is displayed")
    public void verifyRegistrationEmailInputIsDisplayed() {
        registrationActions.verifyRegistrationEmailInputIsDisplayed();
    }

    @And("verify registration password input is displayed")
    public void verifyRegistrationPasswordInputIsDisplayed() {
        registrationActions.verifyRegistrationPasswordInputIsDisplayed();
    }

    @And("verify registration agreement checkbox is displayed")
    public void verifyRegistrationAgreementCheckboxIsDisplayed() {
        registrationActions.verifyRegistrationAgreementCheckboxIsDisplayed();
    }

    @And("verify registration button is displayed")
    public void verifyRegistrationButtonIsDisplayed() {
        registrationActions.verifyRegistrationButtonIsDisplayed();
    }

    @And("verify do you have an account text is displayed")
    public void verifyDoYouHaveAnAccountTextIsDisplayed() {
        registrationActions.verifyDoYouHaveAnAccountTextIsDisplayed();
    }

    @And("user enters already registered email for registration")
    public void userEntersAlreadyRegisteredEmailForRegistration() {
        registrationActions.enterAlreadyRegisteredEmailForRegistration();
    }

    @And("user leaves all registration fields empty")
    public void userLeavesAllRegistrationFieldsEmpty() {
        registrationActions.leaveAllRegistrationFieldsEmpty();
    }

    @And("user enters very long email for registration")
    public void userEntersVeryLongEmailForRegistration() {
        registrationActions.enterVeryLongEmailForRegistration();
    }

    @And("user enters very long password for registration")
    public void userEntersVeryLongPasswordForRegistration() {
        registrationActions.enterVeryLongPasswordForRegistration();
    }

    @And("user enters first name with special characters for registration")
    public void userEntersFirstNameWithSpecialCharactersForRegistration() {
        registrationActions.enterFirstNameWithSpecialCharactersForRegistration();
    }

    @And("user enters last name with special characters for registration")
    public void userEntersLastNameWithSpecialCharactersForRegistration() {
        registrationActions.enterLastNameWithSpecialCharactersForRegistration();
    }

    @And("user enters whitespace-only values in registration fields")
    public void userEntersWhitespaceOnlyValuesInRegistrationFields() {
        registrationActions.enterWhitespaceOnlyValuesInRegistrationFields();
    }

    @And("user enters numeric-only password for registration")
    public void userEntersNumericOnlyPasswordForRegistration() {
        registrationActions.enterNumericOnlyPasswordForRegistration();
    }

    @And("user enters letters-only password for registration")
    public void userEntersLettersOnlyPasswordForRegistration() {
        registrationActions.enterLettersOnlyPasswordForRegistration();
    }

    @And("user enters email with plus sign for registration")
    public void userEntersEmailWithPlusSignForRegistration() {
        registrationActions.enterEmailWithPlusSignForRegistration();
    }

    @When("user closes the registration modal")
    public void userClosesTheRegistrationModal() {
        registrationActions.closeRegistrationModal();
    }


    @Then("verify multiple field validation errors are displayed")
    public void verifyMultipleFieldValidationErrorsAreDisplayed() {
        registrationActions.verifyMultipleFieldValidationErrorsDisplayed();
    }

    @Then("verify appropriate validation error messages are displayed for registration")
    public void verifyAppropriateValidationErrorMessagesAreDisplayedForRegistration() {
        registrationActions.verifyAppropriateValidationErrorMessagesForRegistration();
    }

    @Then("verify registration agreement checkbox is unchecked by default")
    public void verifyRegistrationAgreementCheckboxIsUncheckedByDefault() {
        registrationActions.verifyRegistrationAgreementCheckboxIsUncheckedByDefault();
    }

    @When("user clicks on registration agreement checkbox")
    public void userClicksOnRegistrationAgreementCheckbox() {
        registrationActions.clickOnRegistrationAgreementCheckbox();
    }

    @Then("verify registration agreement checkbox is checked")
    public void verifyRegistrationAgreementCheckboxIsChecked() {
        registrationActions.verifyRegistrationAgreementCheckboxIsChecked();
    }

    @When("user clicks on registration agreement checkbox again")
    public void userClicksOnRegistrationAgreementCheckboxAgain() {
        registrationActions.clickOnRegistrationAgreementCheckbox();
    }

    @Then("verify registration agreement checkbox is unchecked")
    public void verifyRegistrationAgreementCheckboxIsUnchecked() {
        registrationActions.verifyRegistrationAgreementCheckboxIsUnchecked();
    }

    @Then("verify password validation error message is displayed")
    public void verifyPasswordValidationErrorMessageIsDisplayed() {
        registrationActions.verifyPasswordValidationErrorMessage();
    }

    @And("verify close registration success modal button is displayed")
    public void verifyCloseRegistrationSuccessModalButtonIsDisplayed() {
        registrationActions.verifyCloseRegistrationSuccessModalButtonIsDisplayed();
    }

    @And("user navigates through registration form using tab key")
    public void userNavigatesThroughRegistrationFormUsingTabKey() {
        registrationActions.navigateThroughRegistrationFormUsingTabKey();
    }

    @Then("verify all registration form elements are accessible via keyboard")
    public void verifyAllRegistrationFormElementsAreAccessibleViaKeyboard() {
        registrationActions.verifyAllRegistrationFormElementsAccessibleViaKeyboard();
    }

    @And("verify user can submit registration form using enter key")
    public void verifyUserCanSubmitRegistrationFormUsingEnterKey() {
        registrationActions.verifyUserCanSubmitRegistrationFormUsingEnterKey();
    }

    @Then("verify first registration input field has focus")
    public void verifyFirstRegistrationInputFieldHasFocus() {
        registrationActions.verifyFirstRegistrationInputFieldHasFocus();
    }

    @When("user enters data in first name field and tabs")
    public void userEntersDataInFirstNameFieldAndTabs() {
        registrationActions.enterDataInFirstNameFieldAndTab();
    }

    @Then("verify focus moves to last name field")
    public void verifyFocusMovesToLastNameField() {
        registrationActions.verifyFocusMovesToLastNameField();
    }

    @When("user enters data in last name field and tabs")
    public void userEntersDataInLastNameFieldAndTabs() {
        registrationActions.enterDataInLastNameFieldAndTab();
    }

    @Then("verify focus moves to email field")
    public void verifyFocusMovesToEmailField() {
        registrationActions.verifyFocusMovesToEmailField();
    }

}
