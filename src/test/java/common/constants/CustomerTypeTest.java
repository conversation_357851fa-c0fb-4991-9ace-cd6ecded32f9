package common.constants;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class to verify CustomerType enum functionality
 */
public class CustomerTypeTest {

    @Test
    public void testFromValue_Individual() {
        CustomerType result = CustomerType.fromValue("individual");
        assertEquals(CustomerType.INDIVIDUAL, result);
    }

    @Test
    public void testFromValue_Company() {
        CustomerType result = CustomerType.fromValue("company");
        assertEquals(CustomerType.COMPANY, result);
    }

    @Test
    public void testFromValue_Person_MapsToIndividual() {
        CustomerType result = CustomerType.fromValue("person");
        assertEquals(CustomerType.INDIVIDUAL, result);
    }

    @Test
    public void testFromValue_CaseInsensitive() {
        assertEquals(CustomerType.INDIVIDUAL, CustomerType.fromValue("INDIVIDUAL"));
        assertEquals(CustomerType.INDIVIDUAL, CustomerType.fromValue("Person"));
        assertEquals(CustomerType.COMPANY, CustomerType.fromValue("COMPANY"));
    }

    @Test
    public void testFromValue_WithWhitespace() {
        assertEquals(CustomerType.INDIVIDUAL, CustomerType.fromValue(" person "));
        assertEquals(CustomerType.COMPANY, CustomerType.fromValue(" company "));
    }

    @Test
    public void testFromValue_InvalidValue_ThrowsException() {
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> CustomerType.fromValue("invalid")
        );
        
        assertTrue(exception.getMessage().contains("Unknown customer type"));
        assertTrue(exception.getMessage().contains("invalid"));
    }

    @Test
    public void testIsIndividual() {
        assertTrue(CustomerType.INDIVIDUAL.isIndividual());
        assertFalse(CustomerType.COMPANY.isIndividual());
    }

    @Test
    public void testIsCompany() {
        assertTrue(CustomerType.COMPANY.isCompany());
        assertFalse(CustomerType.INDIVIDUAL.isCompany());
    }
}
