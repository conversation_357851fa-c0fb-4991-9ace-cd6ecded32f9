import org.junit.platform.suite.api.*;

import static io.cucumber.junit.platform.engine.Constants.*;

/**
 * These should be always commented in main branch: @IncludeTags, @ExcludeTags, @ConfigurationParameter(key = FILTER_TAGS_PROPERTY_NAME)
 * When executing through command line, it overrides the tags there
 */
@Suite
@IncludeEngines("cucumber")
@SelectClasspathResource("/features")
@ConfigurationParameter(key = PLUGIN_PROPERTY_NAME, value = "io.cucumber.core.plugin.SerenityReporterParallel,pretty,rerun:target/rerun/rerun.txt,html:target/cucumber-report/cucumber.html")
@ConfigurationParameter(key = GLUE_PROPERTY_NAME, value = "steps")
@ConfigurationParameter(key = FEATURES_PROPERTY_NAME, value = "src/test/resources/features")
@ConfigurationParameter(key = EXECUTION_DRY_RUN_PROPERTY_NAME, value = "false")
//@ConfigurationParameter(key = FILTER_TAGS_PROPERTY_NAME, value = "@tagId")

//@IncludeTags("0001")
//@ExcludeTags("sequential")
public class RunnerTestSuite {
}
