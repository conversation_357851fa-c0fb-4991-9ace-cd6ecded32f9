package utils;

import org.openqa.selenium.WebElement;

import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Utility class for handling file upload operations
 */
public class FileUploadUtils {

    /**
     * Gets the absolute path to a test file in the resources/files directory
     * @param fileName the name of the file (e.g., "automation_pdf.pdf")
     * @return the absolute path to the file
     */
    public static String getTestFilePath(String fileName) {
        // Get the path to the test resources directory
        String resourcesPath = System.getProperty("user.dir") + "/src/test/resources/files/";
        Path filePath = Paths.get(resourcesPath, fileName);
        
        File file = filePath.toFile();
        if (!file.exists()) {
            throw new RuntimeException("Test file not found: " + filePath.toString());
        }
        
        return file.getAbsolutePath();
    }

    /**
     * Uploads a file using a file input element
     * @param fileInputElement the file input WebElement
     * @param fileName the name of the file to upload from test resources
     */
    public static void uploadFile(WebElement fileInputElement, String fileName) {
        String filePath = getTestFilePath(fileName);
        fileInputElement.sendKeys(filePath);
    }

    /**
     * Gets the file extension from a filename
     * @param fileName the filename
     * @return the file extension (without the dot)
     */
    public static String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == fileName.length() - 1) {
            return "";
        }
        
        return fileName.substring(lastDotIndex + 1).toLowerCase();
    }

    /**
     * Checks if a file type is supported for upload
     * @param fileName the filename to check
     * @param supportedTypes array of supported file extensions
     * @return true if the file type is supported, false otherwise
     */
    public static boolean isFileTypeSupported(String fileName, String[] supportedTypes) {
        String extension = getFileExtension(fileName);
        
        for (String supportedType : supportedTypes) {
            if (supportedType.equalsIgnoreCase(extension)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Downloads a file by clicking on a download link with cleanup
     * @param downloadElement the download link WebElement
     * @param expectedFileName the expected name of the downloaded file
     * @return true if download was initiated successfully
     */
    public static boolean downloadFileWithCleanup(WebElement downloadElement, String expectedFileName) {
        try {
            // Clean up any existing file with the same name before downloading
            cleanupDownloadedFile(expectedFileName);

            // Click to download the file
            downloadElement.click();

            // Wait for download to complete
            Thread.sleep(4000);

            // Verify the file was downloaded
            boolean downloadSuccess = isFileDownloaded(expectedFileName);

            // Clean up the downloaded file after verification
            if (downloadSuccess) {
                cleanupDownloadedFile(expectedFileName);
            }

            return downloadSuccess;
        } catch (Exception e) {
            System.err.println("Error downloading file: " + e.getMessage());
            return false;
        }
    }

    /**
     * Downloads a file by clicking on a download link (legacy method)
     * @param downloadElement the download link WebElement
     * @return true if download was initiated successfully
     */
    public static boolean downloadFile(WebElement downloadElement) {
        try {
            downloadElement.click();
            // Add a small wait to allow download to start
            Thread.sleep(3000);
            return true;
        } catch (Exception e) {
            System.err.println("Error downloading file: " + e.getMessage());
            return false;
        }
    }

    /**
     * Gets the expected display text for an attached file
     * @param fileName the filename
     * @param prefix the prefix text (e.g., "Wybrany plik: ")
     * @return the expected display text
     */
    public static String getExpectedAttachedFileText(String fileName, String prefix) {
        return prefix + fileName;
    }

    /**
     * Gets the default download directory path
     * @return the path to the default download directory
     */
    public static String getDownloadDirectory() {
        String userHome = System.getProperty("user.home");
        return userHome + File.separator + "Downloads";
    }

    /**
     * Checks if a file exists in the download directory
     * @param fileName the name of the file to check
     * @return true if the file exists, false otherwise
     */
    public static boolean isFileDownloaded(String fileName) {
        String downloadPath = getDownloadDirectory();
        File downloadedFile = new File(downloadPath, fileName);
        return downloadedFile.exists();
    }

    /**
     * Deletes a file from the download directory if it exists
     * @param fileName the name of the file to delete
     * @return true if the file was deleted or didn't exist, false if deletion failed
     */
    public static boolean cleanupDownloadedFile(String fileName) {
        try {
            String downloadPath = getDownloadDirectory();
            File fileToDelete = new File(downloadPath, fileName);

            if (fileToDelete.exists()) {
                boolean deleted = fileToDelete.delete();
                if (deleted) {
                    System.out.println("Successfully cleaned up downloaded file: " + fileName);
                } else {
                    System.err.println("Failed to delete downloaded file: " + fileName);
                }
                return deleted;
            } else {
                // File doesn't exist, so cleanup is successful
                return true;
            }
        } catch (Exception e) {
            System.err.println("Error during file cleanup: " + e.getMessage());
            return false;
        }
    }

    /**
     * Cleans up multiple downloaded files
     * @param fileNames array of file names to clean up
     * @return true if all files were cleaned up successfully
     */
    public static boolean cleanupMultipleDownloadedFiles(String... fileNames) {
        boolean allCleaned = true;
        for (String fileName : fileNames) {
            if (!cleanupDownloadedFile(fileName)) {
                allCleaned = false;
            }
        }
        return allCleaned;
    }

    /**
     * Waits for a file to be downloaded and then verifies its existence
     * @param fileName the name of the file to wait for
     * @param maxWaitTimeSeconds maximum time to wait in seconds
     * @return true if the file was downloaded within the wait time
     */
    public static boolean waitForFileDownload(String fileName, int maxWaitTimeSeconds) {
        int waitTime = 0;
        int checkInterval = 500; // Check every 500ms

        while (waitTime < maxWaitTimeSeconds * 1000) {
            if (isFileDownloaded(fileName)) {
                return true;
            }

            try {
                Thread.sleep(checkInterval);
                waitTime += checkInterval;
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        }

        return false;
    }
}
