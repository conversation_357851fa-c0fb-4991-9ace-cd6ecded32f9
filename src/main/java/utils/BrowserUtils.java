package utils;

import lombok.SneakyThrows;
import lombok.experimental.UtilityClass;
import net.serenitybdd.core.Serenity;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebDriver;

import java.util.ArrayList;


@UtilityClass
public class BrowserUtils {

    @SneakyThrows
    public static void switchToNewWindow() {
        try {
            for (String winHandle : Serenity.getDriver().getWindowHandles()) {
                Serenity.getDriver().switchTo().window(winHandle);
            }
        } catch (Exception e) {
            throw new Exception("Unable to switch to new window", e);
        }
    }

    public static void refreshPage() {
        Serenity.getDriver().navigate().refresh();
    }

    @SneakyThrows
    public static void waitInMillis(int millis) {
        Thread.sleep(millis);
    }

    public static String getCurrentUrl() {
        return Serenity.getDriver().getCurrentUrl();
    }

    public static String pageTitle() {
        return Serenity.getDriver().getTitle();
    }

    public static void clickOnBrowserNavigationButton(String buttonType) {
        final WebDriver driver = Serenity.getDriver();
        final JavascriptExecutor javascriptExecutor = (JavascriptExecutor) driver;
        int typeValue = "back".equalsIgnoreCase(buttonType) ? -1 : 1;
        javascriptExecutor.executeScript("window.history.go('" + typeValue + "')");
    }

    //navigate back
    public static void navigateBack() {
        clickOnBrowserNavigationButton("back");
    }

    public static ArrayList<String> allOpenWindows() {
        return new ArrayList<>(Serenity.getDriver().getWindowHandles());
    }

    public static void changeTab(final int tabIndex) {
        final ArrayList<String> tabs = allOpenWindows();
        Serenity.getDriver().switchTo().window(tabs.get(tabIndex));
    }
    public static void openNewTab(final String url) {
        final WebDriver driver = Serenity.getDriver();
        final String originalWindow = driver.getWindowHandle();
        final JavascriptExecutor javascriptExecutor = (JavascriptExecutor) driver;
        javascriptExecutor.executeScript("window.open('" + url + "')");
        final String newTab = driver.getWindowHandles()
                .stream()
                .filter(handle -> !handle.equals(originalWindow))
                .findFirst()
                .get();
        driver.switchTo().window(newTab);
    }
}
