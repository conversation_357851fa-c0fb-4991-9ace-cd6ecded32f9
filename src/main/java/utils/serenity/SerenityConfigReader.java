package utils.serenity;

import com.typesafe.config.Config;
import net.serenitybdd.core.di.SerenityInfrastructure;
import net.serenitybdd.model.environment.EnvironmentSpecificConfiguration;
import net.thucydides.model.util.EnvironmentVariables;

import java.util.List;

public class SerenityConfigReader {

    /**
     * Reads from serenity.conf file
     */
    public static String getProperty(String propertyName) {
        EnvironmentVariables environmentVariables = SerenityInfrastructure.getEnvironmentVariables();
        return EnvironmentSpecificConfiguration.from(environmentVariables).getProperty(propertyName);
    }

    public static List<String> getListOfValues(String propertyName) {
        EnvironmentVariables environmentVariables = SerenityInfrastructure.getEnvironmentVariables();
        Config config = EnvironmentSpecificConfiguration.from(environmentVariables).getConfig(propertyName.substring(0, propertyName.lastIndexOf(".")));
        return config.getStringList(propertyName.substring(propertyName.lastIndexOf(".") + 1));
    }

}
