package utils;

import common.test_data.BillingAddress;
import common.test_data.PersonalInfo;
import common.test_data.ShippingAddress;

import java.util.Random;

/**
 * Utility class for generating random test data
 * Ensures that each test uses different data to properly verify changes
 */
public class RandomDataGenerator {

    private static final Random random = new Random();

    // Polish first names
    private static final String[] FIRST_NAMES = {
            "<PERSON>", "<PERSON>", "Kat<PERSON><PERSON><PERSON>", "<PERSON>ł<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>z<PERSON>", "Krystyna", "<PERSON>", "E<PERSON>", "Elżbie<PERSON>", "Zof<PERSON>",
            "<PERSON>ot<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"
    };

    // Polish last names
    private static final String[] LAST_NAMES = {
            "Now<PERSON>", "<PERSON>wal<PERSON>", "W<PERSON>śniewski", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>wski", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>rawcz<PERSON>", "Piotrowski", "Grabowski"
    };

    // Polish cities
    private static final String[] CITIES = {
            "Warszawa", "Kraków", "Łódź", "Wrocław", "Poznań", "Gdańsk", "Szczecin", "Bydgoszcz", "Lublin", "Białystok",
            "Katowice", "Gdynia", "Częstochowa", "Radom", "Sosnowiec", "Toruń", "Kielce", "Gliwice", "Zabrze", "Bytom"
    };

    // Polish street names
    private static final String[] STREET_NAMES = {
            "Marszałkowska", "Aleje Jerozolimskie", "Nowy Świat", "Krakowskie Przedmieście", "Floriańska", "Grodzka",
            "Piotrkowska", "Świdnicka", "Półwiejska", "Długa", "Szeroka", "Kościuszki", "Mickiewicza", "Słowackiego",
            "Sienkiewicza", "Piłsudskiego", "3 Maja", "Wolności", "Armii Krajowej", "Jana Pawła II"
    };

    // Company names
    private static final String[] COMPANY_NAMES = {
            "Tech Solutions Sp. z o.o.", "Digital Innovation S.A.", "Modern Business Ltd", "Advanced Systems Sp. z o.o.",
            "Creative Agency S.A.", "Professional Services Ltd", "Quality Solutions Sp. z o.o.", "Expert Consulting S.A.",
            "Dynamic Enterprise Ltd", "Smart Technology Sp. z o.o.", "Global Partners S.A.", "Innovative Solutions Ltd"
    };

    /**
     * Generates a random Polish first name
     */
    public static String generateFirstName() {
        return FIRST_NAMES[random.nextInt(FIRST_NAMES.length)];
    }

    /**
     * Generates a random Polish last name
     */
    public static String generateLastName() {
        return LAST_NAMES[random.nextInt(LAST_NAMES.length)];
    }

    /**
     * Generates a random Polish city name
     */
    public static String generateCity() {
        return CITIES[random.nextInt(CITIES.length)];
    }

    /**
     * Generates a random Polish street address
     */
    public static String generateAddress() {
        String streetName = STREET_NAMES[random.nextInt(STREET_NAMES.length)];
        int streetNumber = random.nextInt(200) + 1;
        return streetName + " " + streetNumber;
    }

    /**
     * Generates a random Polish postal code (XX-XXX format)
     */
    public static String generatePostalCode() {
        int firstPart = random.nextInt(90) + 10;
        int secondPart = random.nextInt(900) + 100;
        return String.format("%02d-%03d", firstPart, secondPart);
    }

    /**
     * Generates a random Polish phone number (9 digits)
     */
    public static String generatePhoneNumber() {
        // Valid Polish mobile prefixes
        String[] validPrefixes = {"50", "51", "53", "57", "60", "66", "69", "72", "73", "79", "88", "89"};

        // Select a random prefix
        String prefix = validPrefixes[random.nextInt(validPrefixes.length)];

        // Generate remaining 7 digits
        StringBuilder phone = new StringBuilder(prefix);
        for (int i = 0; i < 7; i++) {
            phone.append(random.nextInt(10));
        }

        return phone.toString();
    }

    /**
     * Generates a random email address
     */
    public static String generateEmail() {
        String[] domains = {"gmail.com", "yahoo.com", "outlook.com", "test.com", "example.com"};
        String username = "patio.test.automation." + random.nextInt(10000);
        String domain = domains[random.nextInt(domains.length)];
        return username + "@" + domain;
    }

    /**
     * Generates a random company name
     */
    public static String generateCompanyName() {
        return COMPANY_NAMES[random.nextInt(COMPANY_NAMES.length)];
    }

    /**
     * Generates a random valid NIP (10 digits)
     */
    public static String generateNip() {
        StringBuilder nip = new StringBuilder();
        for (int i = 0; i < 10; i++) {
            nip.append(random.nextInt(10));
        }
        return nip.toString();
    }

    /**
     * Generates random personal info
     */
    public static PersonalInfo generateRandomPersonalInfo() {
        return PersonalInfo.builder()
                .firstName(generateFirstName())
                .lastName(generateLastName())
                .email(generateEmail())
                .phone(generatePhoneNumber())
                .build();
    }

    /**
     * Generates random shipping address
     */
    public static ShippingAddress generateRandomShippingAddress() {
        return ShippingAddress.builder()
                .personalInfo(generateRandomPersonalInfo())
                .address(generateAddress())
                .city(generateCity())
                .postalCode(generatePostalCode())
                .companyName(null)
                .build();
    }

    /**
     * Generates random company billing address
     */
    public static BillingAddress generateRandomCompanyBilling() {
        return BillingAddress.builder()
                .companyName(generateCompanyName())
                .nip(generateNip())
                .address(generateAddress())
                .city(generateCity())
                .postalCode(generatePostalCode())
                .phone(generatePhoneNumber())
                .build();
    }

    /**
     * Generates random individual billing address
     */
    public static BillingAddress generateRandomIndividualBilling() {
        PersonalInfo personalInfo = generateRandomPersonalInfo();
        return BillingAddress.builder()
                .companyName(personalInfo.getFirstName() + " " + personalInfo.getLastName())
                .nip(null)
                .address(generateAddress())
                .city(generateCity())
                .postalCode(generatePostalCode())
                .phone(personalInfo.getPhone())
                .build();
    }

    // ========== INVALID DATA GENERATION FOR VALIDATION TESTING ==========

    /**
     * Generates an invalid Polish phone number for validation testing
     * Returns phone numbers with wrong length (too short or too long)
     * Valid Polish mobile numbers should be exactly 9 digits
     */
    public static String generateInvalidPhoneNumber() {
        String[] invalidPhones = {
                "12345678",
                "1234567",
                "123456",
                "12345",
                "1234567890",
                "***********",
                "***********2",
                "***********23",
                "***********234",
                "***********2345"
        };
        return invalidPhones[random.nextInt(invalidPhones.length)];
    }

    /**
     * Generates an invalid Polish phone number with invalid prefix for validation testing
     * Returns 9-digit phone numbers that start with invalid prefixes
     * Valid Polish mobile prefixes are: 50, 51, 53, 57, 60, 66, 69, 72, 73, 79, 88, 89
     * Invalid prefixes include: 80, 90, 99, 11, 97, etc.
     */
    public static String generateInvalidPrefixPhoneNumber() {
        // Invalid Polish mobile prefixes
        String[] invalidPrefixes = {"80", "90", "99", "11", "97", "10", "20", "30", "40", "95", "96", "98"};

        // Select a random invalid prefix
        String prefix = invalidPrefixes[random.nextInt(invalidPrefixes.length)];

        // Generate remaining 7 digits to make it 9 digits total
        StringBuilder phone = new StringBuilder(prefix);
        for (int i = 0; i < 7; i++) {
            phone.append(random.nextInt(10));
        }

        return phone.toString();
    }

    /**
     * Generates an invalid Polish postal code for validation testing
     * Returns postal codes that don't follow the correct xx-xxx format
     * Valid Polish postal codes should be exactly in format: 2 digits + dash + 3 digits (e.g., 00-000)
     */
    public static String generateInvalidPostalCode() {
        String[] invalidPostalCodes = {
                "12-3",
                "1-234",
                "123-45",
                "12-3456",
                "12345",
                "123",
                "1234567890",
                "12 345",
                "12_345",
                "12.345",
                "12/345",
                "12345-",
                "-12345",
                "1-2-345",
                "ab-cde",
                "12-ab3",
                "",
                "   ",
                "--",
                "12--34"
        };
        return invalidPostalCodes[random.nextInt(invalidPostalCodes.length)];
    }

    /**
     * Generates an invalid email address for validation testing
     * Returns emails with wrong format
     */
    public static String generateInvalidEmail() {
        String[] invalidEmails = {
                "invalid",
                "@domain.com",
                "user@",
                "user@domain",
                "user.domain.com",
                "user@@domain.com",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>.",
                "user <EMAIL>",
                "user@domain .com",
                "user@domain,com",
                "user@domain@com",
                "",
                "   ",
                "user@",
                "user@domain."
        };
        return invalidEmails[random.nextInt(invalidEmails.length)];
    }
}
