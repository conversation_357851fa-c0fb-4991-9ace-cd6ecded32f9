package utils.storage;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Storage class is a simple memory class where you can store any data you need.
 * This class should be initialized before use with instantiateStorage() method.
 */
@Getter
@Setter
@NoArgsConstructor
public class Storage {

    private static ThreadLocal<Storage> storage = new ThreadLocal<>();
    //***general***
    private Map<String, Object> mapStorage;

    public static Storage getStorage() {
        return storage.get();
    }

    public static void instantiateStorage() {
        storage.set(new Storage());
        storage.get().mapStorage = new HashMap<>();
    }

    public void saveValue(String key, String value) {
        mapStorage.put(key, value);
    }

    public String getValue(String key) {
        var value = mapStorage.get(key);
        if (value != null) {
            return String.valueOf(value);
        }
        return null;
    }

    public void saveObjectValue(String key, Object value) {
        mapStorage.put(key, value);
    }

    public Object getObjectValue(String key) {
        return mapStorage.get(key);
    }

    public Boolean existsKey(String key) {
        return mapStorage.containsKey(key);
    }

    public void saveBooleanValue(String key, boolean value) {
        mapStorage.put(key, value);
    }

    public boolean getBooleanValue(String key) {
        return (boolean) mapStorage.get(key);
    }

    public void saveListString(String key, List<String> list) {
        mapStorage.put(key, list);
    }

    public List<String> getListString(String key) {
        final Object objectData = mapStorage.get(key);
        List<String> result = new ArrayList<>();
        if (objectData instanceof List) {
            final int size = ((List<?>) objectData).size();
            for (int i = 0; i < size; i++) {
                Object item = ((List<?>) objectData).get(i);
                if (item instanceof String) {
                    result.add((String) item);
                }
            }
        }
        return result;
    }

    public <T> void saveListObjects(String key, List<T> list) {
        mapStorage.put(key, list);
    }

    public List<Object> getListObjects(String key) {
        final var objectData = mapStorage.get(key);
        List<Object> result = new ArrayList<>();
        if (objectData instanceof List) {
            final int size = ((List<?>) objectData).size();
            for (int i = 0; i < size; i++) {
                Object item = ((List<?>) objectData).get(i);
                result.add(item);
            }
        }
        return result;
    }
}