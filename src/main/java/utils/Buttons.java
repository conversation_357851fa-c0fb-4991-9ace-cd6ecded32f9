package utils;

import net.serenitybdd.core.pages.PageObject;
import objects_behaviors.implementation.Button;
import objects_behaviors.rules.IButton;
import org.openqa.selenium.By;

public class But<PERSON> extends PageObject {

    private static final String BUTTON_BY_TEXT = "//button[.//span[contains(@class, 'body-m_bold') and contains(normalize-space(text()), '%')]]";
 
    public IButton buttonWithText(String text) {
        By buttonXpath = By.xpath(BUTTON_BY_TEXT.replace("%", text));
        return new Button($(buttonXpath));
    }
    
}
