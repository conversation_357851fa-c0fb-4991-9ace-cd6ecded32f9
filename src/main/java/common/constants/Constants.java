package common.constants;

/**
 * Centralized constants class for all messages, UI text, and string literals
 */
public class Constants {

    // ========== BUTTON TEXTS ==========
    public static final class Buttons {
        public static final String CONTINUE = "Dalej";
        public static final String ADD_TO_CART = "Do koszyka";
        public static final String TO_CHECKOUT = "Do kasy";
        public static final String PAY = "<PERSON><PERSON><PERSON><PERSON>";
        public static final String CLOSE = "Zamknij";
        public static final String BACK = "Wstecz";
        public static final String CONFIRM = "Potwierdź";
        public static final String CANCEL = "Anuluj";
        public static final String SAVE = "Zapisz";
        public static final String EDIT = "Edytuj";
        public static final String DELETE = "Usuń";
        public static final String SEARCH = "Szukaj";
        public static final String SEE_MORE_PRODUCTS = "Zobacz więcej";

    }

    // ========== PAYMENT METHODS ==========
    public static final class PaymentMethods {
        public static final String BLIK_WITH_CODE = "BLIK z kodem";
        public static final String BANK_CARD = "Karta bankowa";
        public static final String ONLINE_TRANSFER = "Przelewy online";
        public static final String BLIK = "BLIK";
        public static final String BANK_MILLENNIUM = "Bank Millennium";
        public static final String APPLE_PAY = "Apple Pay";
        public static final String GOOGLE_PAY = "Google Pay";
    }

    // ========== DELIVERY METHODS ==========
    public static final class DeliveryMethods {
        public static final String PERSONAL_PICKUP = "Odbiór osobisty";
        public static final String COURIER = "Kurier";
        public static final String INPOST_PARCEL_LOCKER = "InPost Paczkomat";
    }
    // ========== CART MESSAGES ==========
    public static final class CartMessages {
        public static final String EMPTY_CART_HEADER = "Koszyk jest pusty";
        public static final String EMPTY_CART_DESCRIPTION = "Zainspiruj się produktami PATIO i znajdź coś, co odmieni Twój taras lub ogród. Sprawdź nasze bestsellery lub skorzystaj z wyszukiwarki, aby znaleźć to, czego szukasz.";
        public static final String CART_WARNING = "Nie zwlekaj, produkty w koszyku nie są rezerwowane.";
        public static final String FREE_DELIVERY_MESSAGE = "Zamówienie z darmową dostawą";
    }


    // ========== ERROR MESSAGES ==========
    public static final class ErrorMessages {
        public static final String INVALID_BLIK_CODE = "Nieprawidłowy kod BLIK";
        public static final String INVALID_DISCOUNT_CODE = "Nieprawidłowy kod rabatowy";
        public static final String INVALID_CARD_NUMBER_ERROR = "Płatność nieudana. Spróbuj ponownie.";
        public static final String FAILED_CARD_PAYMENT = "Failed to complete card payment";
        public static final String FAILED_FRAME_SWITCH = "Failed to switch to card frame: ";
        public static final String NO_PRODUCT_WITH_ADD_TO_CART = "No product with 'Do koszyka' button found after trying all products";
        public static final String INVALID_LOGIN = "Nieprawidłowy login lub hasło";
        public static final String MAXIMUM_QUANTITY_ERROR = "Aktualnie można dodać do koszyka maksymalnie 50 sztuk.";
        public static final String PAYMENT_CANCELLED_ERROR = "Płatność zaostała anulowana. Spróbuj ponownie lub skorzystaj z innej metody płatności.";

    }

    // ========== SUCCESS MESSAGES ==========
    public static final class SuccessMessages {
        public static final String ORDER_PLACED_SUCCESSFULLY = "Zamówienie zostało złożone pomyślnie";
        public static final String PRODUCT_ADDED_TO_CART = "Produkt został dodany do koszyka";
        public static final String FREE_DELIVERY_ORDER = "Zamówienie z darmową dostawą";
    }

    // ========== PRICE AND CURRENCY ==========
    public static final class PriceFormats {
        public static final String ZERO_PRICE = "0,00 zł";
        public static final String DELIVERY_HEADER_WITH_ZERO = "Dostawa\n0,00 zł";
        public static final String VAT_COST_HEADER = "Podatek VAT";
        public static final String TOTAL_PRICE_HEADER = "Wartość koszyka";
        public static final String CURRENCY_SUFFIX = " zł";
        public static final String DECIMAL_SEPARATOR = ",";
        public static final double FREE_SHIPPING_THRESHOLD = 300.0;
    }

    // ========== CUSTOMER TYPES ==========
    public static final class CustomerTypes {
        public static final String INDIVIDUAL = "individual";
        public static final String COMPANY = "company";
        public static final String PERSON = "person"; // Legacy support
    }

    // ========== CARD PAYMENT FRAME ATTRIBUTES ==========
    public static final class CardFrameAttributes {
        public static final String ENCRYPTED_CARD_NUMBER = "encryptedCardNumber";
        public static final String ENCRYPTED_EXPIRY_DATE = "encryptedExpiryDate";
        public static final String ENCRYPTED_SECURITY_CODE = "encryptedSecurityCode";
    }

    // ========== UI LABELS AND HEADERS ==========
    public static final class UILabels {
        public static final String CART = "Koszyk";
        public static final String DELIVERY = "Dostawa";
        public static final String PAYMENT = "Płatność";
        public static final String SUMMARY = "Podsumowanie";
        public static final String PERSONAL_DATA = "Dane osobowe";
        public static final String BILLING_DATA = "Dane do faktury";
        public static final String SHIPPING_ADDRESS = "Adres dostawy";
        public static final String ORDER_SUMMARY = "Podsumowanie zamówienia";
    }

    // ========== VALIDATION MESSAGES ==========
    public static final class ValidationMessages {
        public static final String FIELD_REQUIRED = "To pole jest wymagane";
        public static final String INVALID_EMAIL = "Podaj poprawny adres email";
        public static final String INVALID_PHONE = "Nieprawidłowy numer telefonu";
        public static final String INVALID_POSTAL_CODE = "Nieprawidłowy kod pocztowy";
        public static final String INVALID_NIP = "Nieprawidłowy numer NIP";
        public static final String PASSWORD_TOO_SHORT = "Hasło jest za krótkie";
        public static final String PASSWORDS_DO_NOT_MATCH = "Hasła nie są identyczne";
        public static final String DATA_ERRORS_SAVE_MESSAGE = "W danych są błędy. Popraw je, aby kontynuować.";
        public static final String EMPTY_PASSWORD_ERROR = "Pole nie może być puste";
        public static final String INVALID_LOGIN_CREDENTIALS = "*Nieprawidłowy adres e-mail lub hasło. Spróbuj ponownie.";
        public static final String INVALID_PHONE_PREFIX = "Nieprawidłowy numer telefonu";
        public static final String INVALID_OR_EMPTY_PASSWORD_ERROR = "Hasło jest za krótkie";
        public static final String MINIMUM_PASSWORD_LENGTH_LABEL = "Ustaw hasło (minimum 8 znaków)";
    }

    // ========== LOGIN MODAL MESSAGES ==========
    public static final class LoginModal {
        public static final String LOGIN_HEADER = "Zaloguj się";
        public static final String LOGIN_SUB_HEADER = "Zaloguj się, aby uzyskać dostęp do konta.";
        public static final String REGISTER_HEADER = "Zarejestruj się";
        public static final String YOU_DONT_HAVE_ACCOUNT_TEXT = "Nie masz konta?";
        public static final String FORGOT_PASSWORD_HEADER = "Nie pamiętasz hasła?";
        public static final String FORGOT_PASSWORD_DESCRIPTION = "Podaj poniżej adres e-mail użyty podczas rejestracji, aby otrzymać link do zresetowania hasła.";
        public static final String RESET_PASSWORD_SUCCESS_HEADER = "Prosimy o potwierdzenie";
        public static final String RESET_PASSWORD_SUCCESS_MESSAGE = "Aby dokończyć proces, sprawdź swoją skrzynkę odbiorczą i kliknij w link potwierdzający, który wysłaliśmy na podany adres e-mail.\n\nJeśli nie widzisz wiadomości, sprawdź folder “Spam”.";
        public static final String NON_REGISTERED_EMAIL_MESSAGE = "Jeśli podany adres e-mail jest zarejestrowany w naszym sklepie, otrzymasz wiadomość z linkiem do zresetowania hasła.";
        public static final String DO_YOU_HAVE_AN_ACCOUNT_TEXT = "Masz już konto?\n"+"Zaloguj się";
    }

    // ========== REGISTRATION MODAL MESSAGES ==========
    public static final class RegistrationModal {
        public static final String REGISTRATION_HEADER = "Zarejestruj się";
        public static final String REGISTRATION_SUCCESS_HEADER = "Potwierdź swój adres email";
        public static final String REGISTRATION_SUCCESS_MESSAGE = "Konto zostało utworzone. Na Twój adres email został wysłany link aktywacyjny.";
        public static final String AGREEMENT_CHECKBOX_LABEL = "Zapoznałem/am się z Regulaminem oraz Polityką Prywatności i akceptuję ich warunki.";
        public static final String AGREEMENT_NOT_CHECKED_ERROR = "Zaznaczenie tego pola jest niezbędne do założenia konta";
        public static final String INCORRECT_EMAIL_FORMAT_ERROR = "Nieprawidłowy format adresu email";
    }

    // ========== TEST DATA ==========
    public static final class TestData {
        public static final String WHITESPACE_EMAIL = "   ";
        public static final String WHITESPACE_PASSWORD = "   ";
        public static final String VERY_LONG_EMAIL = "<EMAIL>";
        public static final String VERY_LONG_PASSWORD = "verylongpasswordthatexceedsnormallimitsandisusedfortestingpurposesonlyandcontainsmanycharacters";
        public static final String NON_REGISTERED_EMAIL = "<EMAIL>";
        public static final String REGISTRATION_FIRST_NAME = "Jan";
        public static final String REGISTRATION_LAST_NAME = "Kowalski";
        public static final String REGISTRATION_EMAIL = "<EMAIL>";
        public static final String REGISTRATION_PASSWORD = "TestPassword123!";
        public static final String INVALID_EMAIL_FORMAT = "invalid.email.format";
        public static final String SHORT_PASSWORD = "123";
        public static final String EXISTING_EMAIL = "<EMAIL>";
        public static final String SPECIAL_CHARS_NAME = "Ąń@#$%";
        public static final String NUMERIC_ONLY_PASSWORD = "123456789";
        public static final String LETTERS_ONLY_PASSWORD = "abcdefghijk";
        public static final String EMAIL_WITH_PLUS = "<EMAIL>";

        // Password validation test data
        public static final String EMPTY_PASSWORD = "";
        public static final String ONE_CHAR_PASSWORD = "1";
        public static final String TWO_CHAR_PASSWORD = "12";
        public static final String THREE_CHAR_PASSWORD = "123";
        public static final String FOUR_CHAR_PASSWORD = "1234";
        public static final String FIVE_CHAR_PASSWORD = "12345";
        public static final String SIX_CHAR_PASSWORD = "123456";
        public static final String SEVEN_CHAR_PASSWORD = "1234567";
        public static final String VALID_EIGHT_CHAR_PASSWORD = "12345678";
    }

    // ========== XPATH PATTERNS ==========
    public static final class XPathPatterns {
        public static final String BUTTON_BY_TEXT = "//button[.//span[contains(@class, 'body-m_bold') and contains(normalize-space(text()), '%s')]]";
        public static final String CARD_FRAME_PATTERN = "//span[@data-cse=\"%s\"]//iframe";
        public static final String RADIO_BUTTON_BY_TEXT = "//input[@type='radio' and following-sibling::label[contains(text(), '%s')]]";
    }

    // ========== FORM FIELD LABELS ==========
    public static final class FormLabels {
        public static final String FIRST_NAME = "Imię";
        public static final String LAST_NAME = "Nazwisko";
        public static final String EMAIL = "Email";
        public static final String PHONE = "Telefon";
        public static final String ADDRESS = "Adres";
        public static final String CITY = "Miasto";
        public static final String POSTAL_CODE = "Kod pocztowy";
        public static final String COMPANY_NAME = "Nazwa firmy";
        public static final String NIP = "NIP";
        public static final String PASSWORD = "Hasło";
        public static final String CONFIRM_PASSWORD = "Potwierdź hasło";
        public static final String DISCOUNT_CODE = "Kod rabatowy";
        public static final String COMMENTS = "Uwagi";
    }

    // ========== INVOICE TYPES ==========
    public static final class InvoiceTypes {
        public static final String PRIVATE = "private";
        public static final String COMPANY = "company";
        public static final String RECEIPT = "receipt";
    }
    public static class UserTypes {
        public static final String INDIVIDUAL = "individual";
        public static final String COMPANY = "company";
        public static final String ADMIN = "admin";
        public static final String PREMIUM = "premium";
    }

    // ========== ACCOUNT PAGE CONSTANTS ==========
    public static final class AccountPage {
        // Order status messages
        public static final String ORDER_STATUS_ACCEPTED = "Przyjęto zamówienie";
        public static final String SHIPPING_STATUS_WAITING = "Czeka na zatwierdzenie";

        // Ticket related messages
        public static final String TICKET_ORDER_PREFIX = "Zamówienie nr";
        public static final String MESSAGE_RECEIVED_HEADER = "Otrzymaliśmy Twoją wiadomość, odpowiemy w godzinach pracy BOK";

        // Profile headers
        public static final String USER_PROFILE_HEADER = "Profil użytkownika";
        public static final String NAME_AND_LASTNAME_HEADER = "Imię i nazwisko";
        public static final String PASSWORD_HEADER = "Hasło";
        public static final String PASSWORD_VALUE = "********";

        // Company indicators for address validation
        public static final String[] COMPANY_INDICATORS = {
                "sp. z o.o.", "s.a.", "ltd", "company", "solutions", "tech", "business"
        };

        // Chat related constants
        public static final String CHAT_RECEIVED_MESSAGE = "Otrzymaliśmy Twoją wiadomość, odpowiemy w godzinach pracy BOK";

        // Ticket creation validation messages
        public static final String EMPTY_MESSAGE_FIELD_ERROR = "Wypełnij treść wiadomości.";
        public static final String REASON_FOR_CONTACT_NOT_SELECTED_ERROR = "Wybierz temat wiadomości";

        // File attachment constants
        public static final String INVALID_FILE_TYPE_ERROR = "Zły format pliku (obsługiwane formaty: jpg, pdf, doc)";
        public static final String ATTACHED_FILE_PREFIX = "Wybrany plik: ";

        // Supported file types
        public static final String[] SUPPORTED_FILE_TYPES = {"pdf", "jpg", "docx"};
        public static final String[] UNSUPPORTED_FILE_TYPES = {"png"};

        // Polish month names
        public static final String[] POLISH_MONTHS = {
                "", "stycznia", "lutego", "marca", "kwietnia", "maja", "czerwca",
                "lipca", "sierpnia", "września", "października", "listopada", "grudnia"
        };

        // Wait times
        public static final int DEFAULT_WAIT_TIME = 3000;
        public static final int SAVE_OPERATION_WAIT = 2000;
        public static final int ADDRESS_DELETE_WAIT = 3000;
        public static final int STABILITY_WAIT = 1000;

        // Newsletter agreement
        public static final String NEWSLETTER_AGREEMENT_LABEL = "Wyrażam zgodę na otrzymywanie newslettera zawierającego informacje handlowe i promocyjne od Dajar Sp. z o.o. na podany adres e-mail.";
    }

    // ========== SORT OPTIONS ==========
    public static final class SortOptions {
        public static final String RECOMMENDED = "Polecane";
        public static final String PRICE_LOW_TO_HIGH = "Cena od najniższej";
        public static final String PRICE_HIGH_TO_LOW = "Cena od najwyższej";
        public static final String NAME_A_TO_Z = "Wg nazwy A-Z";
        public static final String NAME_Z_TO_A = "Wg nazwy Z-A";
    }

    // ========== SORT URL PARAMETERS ==========
    public static final class SortUrlParams {
        public static final String PRICE_ASC = "sort=minimal_price-asc";
        public static final String PRICE_DESC = "sort=minimal_price-desc";
        public static final String NAME_ASC = "sort=name-asc";
        public static final String NAME_DESC = "sort=name-desc";
    }

    // ========== HEADERS ==========
    public static final class Headers {
        public static final String ACCOUNT_HEADER = "Konto";
        public static final String RELATED_PRODUCTS_HEADER = "Powiązane produkty";
        public static final String QUESTION_AND_ANSWERS_HEADER = "Pytania i odpowiedzi";
    }
    // ========== COUNTRY AND LANGUAGE ==========
    public static final class CountryLanguage {
        // Button texts
        public static final String SAVE_AND_GO_TO_SHOP = "Zapisz i przejdź do sklepu";

        // Language names
        public static final String POLISH = "Polski";
        public static final String ENGLISH = "English";
        public static final String GERMAN = "Deutsch";

        // Common country names in different languages
        public static final String AUSTRIA_GERMAN = "Österreich";
        public static final String DEUTSCHLAND_GERMAN = "Deutschland";
        public static final String POLAND_POLISH = "Polska";
        public static final String POLAND_ENGLISH = "Poland";

        // Modal texts
        public static final String MODAL_HEADER = "Wybierz kraj i język";
        public static final String MODAL_DESCRIPTION = "Co to oznacza?\nTwoje zamówienia będą dostarczane na terenie wybranego kraju.";

        // URL patterns
        public static final String URL_PATTERN_COUNTRY_LANGUAGE = "/%s/%s";

        // Footer separator
        public static final String FOOTER_SEPARATOR = " • ";

        // Wait times for country/language operations
        public static final int MODAL_OPEN_WAIT = 2000;
        public static final int COUNTRY_SELECTION_WAIT = 1000;
        public static final int LANGUAGE_SELECTION_WAIT = 1000;
        public static final int PAGE_RELOAD_WAIT = 3000;
    }
}
