package common.constants;

import lombok.Getter;

/**
 * Enum representing different customer types for a checkout process
 */
@Getter
public enum CustomerType {
    INDIVIDUAL("individual", "Individual Customer"),
    COMPANY("company", "Company Customer");

    private final String value;
    private final String displayName;

    CustomerType(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    public static CustomerType fromValue(String value) {
        String normalizedValue = value.trim().toLowerCase();

        for (CustomerType customerType : values()) {
            if (customerType.value.equalsIgnoreCase(normalizedValue)) {
                return customerType;
            }
        }
        // Handle "person" mapping to INDIVIDUAL
        if ("person".equals(normalizedValue)) {
            return INDIVIDUAL;
        }

        throw new IllegalArgumentException("Unknown customer type: '" + value + "'. " +
                "Supported types: individual, company, person");
    }

    public boolean isIndividual() {
        return this == INDIVIDUAL;
    }

    public boolean isCompany() {
        return this == COMPANY;
    }
}
