package common.constants;

import lombok.Getter;

import java.util.Arrays;
import java.util.stream.Collectors;

@Getter
public enum PageType {
    HOME_PAGE("home page", "/"),
    CATEGORY_PAGE("category page", "/c/hustawki"),
    PRODUCT_PAGE("product page", "/p/hustawka-ogrodowa-z-moskitiera-venezia"),
    CART_PAGE("cart page", "/cart"),
    CONTACT_PAGE("contact page", "/pg/kontakt"),
    ABOUT_PAGE("about page", "/pg/poznaj-patio"),
    INSPIRATIONS_PAGE("inspirations page", "/pc/inspiracje-ogrodowe"),
    REGULATIONS_PAGE("privacy policy page", "/pg/regulamin"),
    PRIVACY_POLICY_PAGE("privacy policy page", "/pg/polityka-prywatnosci"),
    COOKIES_POLICY_PAGE("cookies policy page", "/pg/polityka-cookies"),
    COPYRIGHT_PAGE("copyright page", "/pg/prawa-autorskie"),
    ERROR_PAGE("error page", "/404");

    private final String displayName;
    private final String url;

    PageType(String displayName, String url) {
        this.displayName = displayName;
        this.url = url;
    }

    public static PageType fromDisplayName(String displayName) {
        for (PageType pageType : values()) {
            if (pageType.displayName.equalsIgnoreCase(displayName.trim())) {
                return pageType;
            }
        }
        throw new IllegalArgumentException("Unknown page: '" + displayName + "'. " +
                "Supported pages: " + Arrays.stream(values())
                .map(PageType::getDisplayName)
                .collect(Collectors.joining(", ")));
    }
}
