package common.constants;

import lombok.Getter;

/**
 * Enum representing different address types used in a checkout process
 */
@Getter
public enum AddressType {
    SHIPPING("shipping", "Shipping Address"),
    BILLING("billing", "Billing Address");

    private final String value;
    private final String displayName;

    AddressType(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    public static AddressType fromValue(String value) {
        for (AddressType addressType : values()) {
            if (addressType.value.equalsIgnoreCase(value.trim())) {
                return addressType;
            }
        }
        throw new IllegalArgumentException("Unknown address type: '" + value + "'. " +
                "Supported types: shipping, billing");
    }

    public boolean isShipping() {
        return this == SHIPPING;
    }

    public boolean isBilling() {
        return this == BILLING;
    }
}
