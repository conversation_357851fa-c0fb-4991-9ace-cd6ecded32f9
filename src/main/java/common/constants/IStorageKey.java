package common.constants;


//Here contains values that are used to store data in the storage
public interface IStorageKey {
    String SEARCH_TERM = "search_term";
    String FIRST_PRODUCT_NAME = "first_product_name";
    String FIRST_PRODUCT_PRICE = "first_product_price";
    String FIRST_PRODUCT_QUANTITY = "first_product_quantity";
    String SECOND_PRODUCT_NAME = "second_product_name";
    String SECOND_PRODUCT_PRICE = "second_product_price";
    String SECOND_PRODUCT_QUANTITY = "second_product_quantity";
    String TOTAL_PRODUCT_QUANTITY  = "total_product_quantity";
    String TOTAL_CART_PRICE_BEFORE = "total_cart_price_before";
    String PRODUCT_NAME_IN_CART = "product_name_in_cart";
    String TOTAL_CART_PRICE_BEFORE_DISCOUNT = "total_cart_price_before_discount";
    String PRODUCT_QUANTITY = "product_quantity";
    String APPLIED_DISCOUNT_CODE = "applied_discount_code";
    String DISCOUNT_VALUE_BEFORE_QUANTITY_CHANGE = "discount_value_before_quantity_change";
    String EXPECTED_QUANTITY_KEY = "EXPECTED_QUANTITY";
    String INITIAL_QUANTITY_KEY = "INITIAL_QUANTITY";
    String PLUS_CLICKS_KEY = "PLUS_CLICKS";
    String MINUS_CLICKS_KEY = "MINUS_CLICKS";
    String PRODUCT_SKU_FROM_PDP = "product_sku_from_pdp";

    // User account related keys
    String USER_FIRST_NAME = "user_first_name";
    String USER_LAST_NAME = "user_last_name";
    String USER_EMAIL = "user_email";
    String USER_PASSWORD = "user_password";
    String EXISTING_USER_EMAIL = "existing_user_email";
    String TEST_USER_EMAIL = "test_user_email";
    String TEST_USER_PASSWORD = "test_user_password";
    String VALID_USER_EMAIL = "valid_user_email";
    String VALID_USER_PASSWORD = "valid_user_password";
    String PRODUCT_ADDED_AS_GUEST = "product_added_as_guest";
    // User profile related keys
    String OLD_PASSWORD = "old_password";
    String NEW_PASSWORD = "new_password";
    String UPDATED_FIRST_NAME = "updated_first_name";
    String UPDATED_LAST_NAME = "updated_last_name";
    String UPDATED_PHONE = "updated_phone";
    String ADDRESS_NAME = "address_name";
    String ADDRESS_STREET = "address_street";
    String ADDRESS_CITY = "address_city";
    String ADDRESS_POSTAL_CODE = "address_postal_code";
    String ADDRESS_COUNTRY = "address_country";
    String SELECTED_SHIPPING_ADDRESS = "selected_shipping_address";
    String SELECTED_BILLING_ADDRESS = "selected_billing_address";
    String PREVIOUS_SHIPPING_ADDRESS = "PREVIOUS_SHIPPING_ADDRESS";
    String PREVIOUS_BILLING_ADDRESS = "PREVIOUS_BILLING_ADDRESS";
    String BILLING_COUNTRY = "billing_country";
    // Add these new keys for order verification
    String ORDER_NUMBER = "order_number";
    String PRODUCT_NAME_FOR_ORDER = "product_name_for_order";
    String PRODUCT_PRICE_FOR_ORDER = "product_price_for_order";
    String PAYMENT_METHOD = "payment_method";
    // Ticket creation related keys
    String TICKET_MESSAGE = "ticket_message";
    String SELECTED_REASON = "selected_reason";
    String SELECTED_ORDER_FOR_TICKET = "selected_order_for_ticket";
    // Chat related keys
    String CHAT_MESSAGE = "chat_message";
    String USER_PROFILE_NAME = "user_profile_name";

    // Address management keys - Updated addresses
    String UPDATED_BILLING_ADDRESS = "updated_billing_address";
    String UPDATED_BILLING_CITY = "updated_billing_city";
    String UPDATED_BILLING_COMPANY = "updated_billing_company";
    String UPDATED_BILLING_PHONE = "updated_billing_phone";
    String UPDATED_BILLING_NIP = "updated_billing_nip";
    String UPDATED_SHIPPING_ADDRESS = "updated_shipping_address";
    String UPDATED_SHIPPING_CITY = "updated_shipping_city";
    String UPDATED_SHIPPING_FIRST_NAME = "updated_shipping_first_name";
    String UPDATED_SHIPPING_LAST_NAME = "updated_shipping_last_name";
    String UPDATED_SHIPPING_PHONE = "updated_shipping_phone";
    String UPDATED_SHIPPING_POSTAL_CODE = "updated_shipping_postal_code";
    String UPDATED_SHIPPING_COMPANY = "updated_shipping_company";

    // Address management keys - New addresses
    String NEW_BILLING_ADDRESS = "new_billing_address";
    String NEW_BILLING_CITY = "new_billing_city";
    String NEW_BILLING_COMPANY = "new_billing_company";
    String NEW_BILLING_PHONE = "new_billing_phone";
    String NEW_BILLING_NIP = "new_billing_nip";
    String NEW_SHIPPING_ADDRESS = "new_shipping_address";
    String NEW_SHIPPING_CITY = "new_shipping_city";
    String NEW_SHIPPING_FIRST_NAME = "new_shipping_first_name";
    String NEW_SHIPPING_LAST_NAME = "new_shipping_last_name";
    String NEW_SHIPPING_PHONE = "new_shipping_phone";
    String NEW_SHIPPING_POSTAL_CODE = "new_shipping_postal_code";
    String NEW_SHIPPING_COMPANY = "new_shipping_company";

    // Profile update keys
    String UPDATED_FULL_NAME = "updated_full_name";

    // File attachment keys
    String ATTACHED_FILE_NAME = "attached_file_name";
    String ATTACHED_FILE_PATH = "attached_file_path";
    String CHAT_MESSAGE_WITH_FILE = "chat_message_with_file";

    // Registration at checkout keys
    String REGISTRATION_EMAIL = "registration_email";
    String REGISTRATION_PASSWORD = "registration_password";

}
