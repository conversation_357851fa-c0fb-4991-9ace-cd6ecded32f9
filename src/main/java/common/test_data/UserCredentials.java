package common.test_data;

import lombok.Builder;
import lombok.Getter;

/**
 * Represents user login credentials for test data
 */
@Getter
@Builder
public class UserCredentials {
    private final String email;
    private final String password;
    private final String userType;

    public UserCredentials(String email, String password, String userType) {
        this.email = email;
        this.password = password;
        this.userType = userType;
    }

    /**
     * Creates default user credentials
     */
    public static UserCredentials defaultCredentials() {
        return UserCredentials.builder()
                .email("<EMAIL>")
                .password("Test12345!")
                .userType("individual")
                .build();
    }

    /**
     * Creates company user credentials
     */
    public static UserCredentials companyCredentials() {
        return UserCredentials.builder()
                .email("<EMAIL>")
                .password("Test12345!")
                .userType("company")
                .build();
    }

    /**
     * Creates alternative user credentials
     */
    public static UserCredentials alternativeCredentials() {
        return UserCredentials.builder()
                .email("<EMAIL>")
                .password("Test12345!")
                .userType("individual")
                .build();
    }

    /**
     * Creates user credentials for logged in order tests
     */
    public static UserCredentials loggedInOrderCredentials() {
        return UserCredentials.builder()
                .email("<EMAIL>")
                .password("Test12345!")
                .userType("individual")
                .build();
    }

    /**
     * Creates user credentials for logged in order tests - second account
     */
    public static UserCredentials loggedInOrderCredentials2() {
        return UserCredentials.builder()
                .email("<EMAIL>")
                .password("Test12345!")
                .userType("individual")
                .build();
    }

    /**
     * Creates user credentials for account tests
     */
    public static UserCredentials accountTestCredentials() {
        return UserCredentials.builder()
                .email("<EMAIL>")
                .password("Test12345!")
                .userType("individual")
                .build();
    }

    /**
     * Creates user credentials for account tests - second account
     */
    public static UserCredentials accountTestCredentials2() {
        return UserCredentials.builder()
                .email("<EMAIL>")
                .password("Test12345!")
                .userType("individual")
                .build();
    }

}