package common.test_data;

import lombok.Builder;
import lombok.Getter;

/**
 * Represents billing address information for test data
 */
@Getter
@Builder
public class BillingAddress {
    private final String companyName;
    private final String nip;
    private final String address;
    private final String postalCode;
    private final String city;
    private final String phone;

    public BillingAddress(String companyName, String nip, String address, 
                         String postalCode, String city, String phone) {
        this.companyName = companyName;
        this.nip = nip;
        this.address = address;
        this.postalCode = postalCode;
        this.city = city;
        this.phone = phone;
    }

    /**
     * Creates a default company billing address
     */
    public static BillingAddress defaultCompanyBilling() {
        return BillingAddress.builder()
                .companyName("Test Automation Company")
                .nip("*********0")
                .address("Test Billing Address")
                .postalCode("00-100")
                .city("Billing City")
                .phone("*********")
                .build();
    }

    /**
     * Creates an alternative company billing address for different test scenarios
     */
    public static BillingAddress alternativeCompanyBilling() {
        return BillingAddress.builder()
                .companyName("Alternative Billing Corp")
                .nip("0987654321")
                .address("200 Invoice Avenue")
                .postalCode("00-200")
                .city("Invoice City")
                .phone("*********")
                .build();
    }

    /**
     * Creates a billing address for individual (personal) billing
     * Uses personal name as "company name" and no NIP
     */
    public static BillingAddress individualBilling(PersonalInfo personalInfo) {
        return BillingAddress.builder()
                .companyName(personalInfo.getFirstName() + " " + personalInfo.getLastName())
                .nip(null) // No NIP for individual
                .address("Personal Billing Address")
                .postalCode("00-300")
                .city("Personal City")
                .phone(personalInfo.getPhone())
                .build();
    }

    /**
     * Creates a billing address for individual with optional company name
     * This is for cases where individual wants to bill to a company but without NIP
     */
    public static BillingAddress individualWithCompanyBilling(PersonalInfo personalInfo, String companyName) {
        return BillingAddress.builder()
                .companyName(companyName != null ? companyName : (personalInfo.getFirstName() + " " + personalInfo.getLastName()))
                .nip(null) // No NIP for individual
                .address("Company Billing Address")
                .postalCode("00-350")
                .city("Company City")
                .phone(personalInfo.getPhone())
                .build();
    }

    /**
     * Creates a billing address with invalid NIP for negative testing
     */
    public static BillingAddress invalidNipBilling() {
        return BillingAddress.builder()
                .companyName("Invalid NIP Company")
                .nip("123") // Invalid NIP format
                .address("Invalid Street")
                .postalCode("00-400")
                .city("Invalid City")
                .phone("*********")
                .build();
    }

    // Convenience methods
    public boolean hasValidNip() {
        return nip != null && nip.matches("\\d{10}"); // Simple NIP validation
    }

    public boolean isCompanyBilling() {
        return nip != null && !nip.trim().isEmpty();
    }

    public boolean isIndividualBilling() {
        return !isCompanyBilling();
    }
}
