package common.test_data;

import common.constants.IStorageKey;
import utils.RandomDataGenerator;
import utils.storage.Storage;

/**
 * Test data factory class for Account Page related test data generation and management
 */
public class AccountPageTestData {

    /**
     * Creates a shipping address with empty phone number for validation testing
     */
    public static ShippingAddress createShippingAddressWithoutPhone() {
        return ShippingAddress.builder()
                .personalInfo(PersonalInfo.builder()
                        .email("<EMAIL>")
                        .firstName("NoPhone")
                        .lastName("User")
                        .phone("")
                        .build())
                .address("No Phone Address")
                .city("NoPhone City")
                .postalCode("00-000")
                .companyName("")
                .build();
    }

    /**
     * Creates a shipping address with empty first name for validation testing
     */
    public static ShippingAddress createShippingAddressWithoutName() {
        return ShippingAddress.builder()
                .personalInfo(PersonalInfo.builder()
                        .email("<EMAIL>")
                        .firstName("")
                        .lastName("User")
                        .phone("*********")
                        .build())
                .address("No Name Address")
                .city("NoName City")
                .postalCode("00-000")
                .companyName("")
                .build();
    }

    /**
     * Creates a shipping address with empty last name for validation testing
     */
    public static ShippingAddress createShippingAddressWithoutLastName() {
        return ShippingAddress.builder()
                .personalInfo(PersonalInfo.builder()
                        .email("<EMAIL>")
                        .firstName("NoLastName")
                        .lastName("")
                        .phone("*********")
                        .build())
                .address("No LastName Address")
                .city("NoLastName City")
                .postalCode("00-000")
                .companyName("")
                .build();
    }

    /**
     * Creates a shipping address with empty address for validation testing
     */
    public static ShippingAddress createShippingAddressWithoutAddress() {
        return ShippingAddress.builder()
                .personalInfo(PersonalInfo.builder()
                        .email("<EMAIL>")
                        .firstName("NoAddress")
                        .lastName("User")
                        .phone("*********")
                        .build())
                .address("")
                .city("NoAddress City")
                .postalCode("00-000")
                .companyName("")
                .build();
    }

    /**
     * Creates a shipping address with empty city for validation testing
     */
    public static ShippingAddress createShippingAddressWithoutCity() {
        return ShippingAddress.builder()
                .personalInfo(PersonalInfo.builder()
                        .email("<EMAIL>")
                        .firstName("NoCity")
                        .lastName("User")
                        .phone("*********")
                        .build())
                .address("No City Address")
                .city("")
                .postalCode("00-000")
                .companyName("")
                .build();
    }

    /**
     * Creates a shipping address with empty postal code for validation testing
     */
    public static ShippingAddress createShippingAddressWithoutPostCode() {
        return ShippingAddress.builder()
                .personalInfo(PersonalInfo.builder()
                        .email("<EMAIL>")
                        .firstName("NoPostCode")
                        .lastName("User")
                        .phone("*********")
                        .build())
                .address("No PostCode Address")
                .city("NoPostCode City")
                .postalCode("")
                .companyName("")
                .build();
    }

    /**
     * Creates a billing address with empty first name for validation testing
     */
    public static BillingAddress createBillingAddressWithoutName() {
        return BillingAddress.builder()
                .companyName(" User") // Empty first name, only last name
                .address("No Name Billing Address")
                .city("NoName City")
                .postalCode("00-000")
                .phone("*********")
                .nip("")
                .build();
    }

    /**
     * Creates a billing address with empty last name for validation testing
     */
    public static BillingAddress createBillingAddressWithoutLastName() {
        return BillingAddress.builder()
                .companyName("NoLastName ") // Only first name, empty last name
                .address("No LastName Billing Address")
                .city("NoLastName City")
                .postalCode("00-000")
                .phone("*********")
                .nip("")
                .build();
    }

    /**
     * Creates a billing address with empty address for validation testing
     */
    public static BillingAddress createBillingAddressWithoutAddress() {
        return BillingAddress.builder()
                .companyName("NoAddress User")
                .address("")
                .city("NoAddress City")
                .postalCode("00-000")
                .phone("*********")
                .nip("")
                .build();
    }

    /**
     * Creates a billing address with empty city for validation testing
     */
    public static BillingAddress createBillingAddressWithoutCity() {
        return BillingAddress.builder()
                .companyName("NoCity User")
                .address("No City Billing Address")
                .city("")
                .postalCode("00-000")
                .phone("*********")
                .nip("")
                .build();
    }

    /**
     * Creates a billing address with empty postal code for validation testing
     */
    public static BillingAddress createBillingAddressWithoutPostCode() {
        return BillingAddress.builder()
                .companyName("NoPostCode User")
                .address("No PostCode Billing Address")
                .city("NoPostCode City")
                .postalCode("")
                .phone("*********")
                .nip("")
                .build();
    }

    /**
     * Generates random shipping address data for testing
     */
    public static ShippingAddress generateRandomShippingAddress() {
        return ShippingAddress.builder()
                .personalInfo(PersonalInfo.builder()
                        .email(RandomDataGenerator.generateEmail())
                        .firstName(RandomDataGenerator.generateFirstName())
                        .lastName(RandomDataGenerator.generateLastName())
                        .phone(RandomDataGenerator.generatePhoneNumber())
                        .build())
                .address(RandomDataGenerator.generateAddress())
                .city(RandomDataGenerator.generateCity())
                .postalCode(RandomDataGenerator.generatePostalCode())
                .companyName("")
                .build();
    }

    /**
     * Generates random company billing address data for testing
     */
    public static BillingAddress generateRandomCompanyBillingAddress() {
        return BillingAddress.builder()
                .companyName(RandomDataGenerator.generateCompanyName())
                .nip(RandomDataGenerator.generateNip())
                .address(RandomDataGenerator.generateAddress())
                .city(RandomDataGenerator.generateCity())
                .postalCode(RandomDataGenerator.generatePostalCode())
                .phone(RandomDataGenerator.generatePhoneNumber())
                .build();
    }

    /**
     * Generates random individual billing address data for testing
     */
    public static BillingAddress generateRandomIndividualBillingAddress() {
        return BillingAddress.builder()
                .companyName(RandomDataGenerator.generateFirstName() + " " + RandomDataGenerator.generateLastName())
                .nip("") // Empty NIP for individual billing
                .address(RandomDataGenerator.generateAddress())
                .city(RandomDataGenerator.generateCity())
                .postalCode(RandomDataGenerator.generatePostalCode())
                .phone(RandomDataGenerator.generatePhoneNumber())
                .build();
    }

    // ========== INVALID DATA FOR VALIDATION TESTING ==========

    /**
     * Creates a shipping address with invalid email for validation testing
     */
    public static ShippingAddress createShippingAddressWithInvalidEmail() {
        return ShippingAddress.builder()
                .personalInfo(PersonalInfo.builder()
                        .email(RandomDataGenerator.generateInvalidEmail())
                        .firstName("ValidFirst")
                        .lastName("ValidLast")
                        .phone("*********")
                        .build())
                .address("Valid Address 123")
                .city("Valid City")
                .postalCode("00-000")
                .companyName("")
                .build();
    }

    /**
     * Creates a shipping address with invalid phone number for validation testing
     */
    public static ShippingAddress createShippingAddressWithInvalidPhone() {
        return ShippingAddress.builder()
                .personalInfo(PersonalInfo.builder()
                        .email("<EMAIL>")
                        .firstName("ValidFirst")
                        .lastName("ValidLast")
                        .phone(RandomDataGenerator.generateInvalidPhoneNumber())
                        .build())
                .address("Valid Address 123")
                .city("Valid City")
                .postalCode("00-000")
                .companyName("")
                .build();
    }

    /**
     * Creates a shipping address with invalid postal code for validation testing
     */
    public static ShippingAddress createShippingAddressWithInvalidPostalCode() {
        return ShippingAddress.builder()
                .personalInfo(PersonalInfo.builder()
                        .email("<EMAIL>")
                        .firstName("ValidFirst")
                        .lastName("ValidLast")
                        .phone("*********")
                        .build())
                .address("Valid Address 123")
                .city("Valid City")
                .postalCode(RandomDataGenerator.generateInvalidPostalCode())
                .companyName("")
                .build();
    }

    /**
     * Creates a billing address with invalid postal code for validation testing
     */
    public static BillingAddress createBillingAddressWithInvalidPostalCode() {
        return BillingAddress.builder()
                .companyName("Valid Company Name")
                .address("Valid Billing Address 123")
                .city("Valid City")
                .postalCode(RandomDataGenerator.generateInvalidPostalCode())
                .phone("*********")
                .nip("*********0")
                .build();
    }

    // ========== ADDRESS DATA STORAGE METHODS ==========

    /**
     * Stores shipping address data for editing scenarios
     */
    public static void storeShippingAddressDataForEditing(ShippingAddress shippingData) {
        Storage.getStorage().saveValue(IStorageKey.UPDATED_SHIPPING_ADDRESS, shippingData.getAddress());
        Storage.getStorage().saveValue(IStorageKey.UPDATED_SHIPPING_CITY, shippingData.getCity());
        Storage.getStorage().saveValue(IStorageKey.UPDATED_SHIPPING_FIRST_NAME, shippingData.getFirstName());
        Storage.getStorage().saveValue(IStorageKey.UPDATED_SHIPPING_LAST_NAME, shippingData.getLastName());
        Storage.getStorage().saveValue(IStorageKey.UPDATED_SHIPPING_PHONE, shippingData.getPhone());
        Storage.getStorage().saveValue(IStorageKey.UPDATED_SHIPPING_POSTAL_CODE, shippingData.getPostalCode());
        Storage.getStorage().saveValue(IStorageKey.UPDATED_SHIPPING_COMPANY, shippingData.hasCompanyName() ? shippingData.getCompanyName() : "");
    }

    /**
     * Stores billing address data for editing scenarios
     */
    public static void storeBillingAddressDataForEditing(BillingAddress billingData) {
        Storage.getStorage().saveValue(IStorageKey.UPDATED_BILLING_ADDRESS, billingData.getAddress());
        Storage.getStorage().saveValue(IStorageKey.UPDATED_BILLING_CITY, billingData.getCity());
        Storage.getStorage().saveValue(IStorageKey.UPDATED_BILLING_COMPANY, billingData.getCompanyName());
        Storage.getStorage().saveValue(IStorageKey.UPDATED_BILLING_PHONE, billingData.getPhone());
        if (billingData.isCompanyBilling()) {
            Storage.getStorage().saveValue(IStorageKey.UPDATED_BILLING_NIP, billingData.getNip());
        }
    }

    /**
     * Stores shipping address data for new address scenarios
     */
    public static void storeShippingAddressDataForNewAddress(ShippingAddress shippingData) {
        Storage.getStorage().saveValue(IStorageKey.NEW_SHIPPING_ADDRESS, shippingData.getAddress());
        Storage.getStorage().saveValue(IStorageKey.NEW_SHIPPING_CITY, shippingData.getCity());
        Storage.getStorage().saveValue(IStorageKey.NEW_SHIPPING_FIRST_NAME, shippingData.getFirstName());
        Storage.getStorage().saveValue(IStorageKey.NEW_SHIPPING_LAST_NAME, shippingData.getLastName());
        Storage.getStorage().saveValue(IStorageKey.NEW_SHIPPING_PHONE, shippingData.getPhone());
        Storage.getStorage().saveValue(IStorageKey.NEW_SHIPPING_POSTAL_CODE, shippingData.getPostalCode());
        Storage.getStorage().saveValue(IStorageKey.NEW_SHIPPING_COMPANY, shippingData.hasCompanyName() ? shippingData.getCompanyName() : "");
    }

    /**
     * Stores billing address data for new address scenarios
     */
    public static void storeBillingAddressDataForNewAddress(BillingAddress billingData) {
        Storage.getStorage().saveValue(IStorageKey.NEW_BILLING_ADDRESS, billingData.getAddress());
        Storage.getStorage().saveValue(IStorageKey.NEW_BILLING_CITY, billingData.getCity());
        Storage.getStorage().saveValue(IStorageKey.NEW_BILLING_COMPANY, billingData.getCompanyName());
        Storage.getStorage().saveValue(IStorageKey.NEW_BILLING_PHONE, billingData.getPhone());
        if (billingData.isCompanyBilling()) {
            Storage.getStorage().saveValue(IStorageKey.NEW_BILLING_NIP, billingData.getNip());
        }
    }
    /**
     * Creates a company billing address with empty company name for validation testing
     */
    public static BillingAddress createCompanyBillingAddressWithoutCompanyName() {
        return BillingAddress.builder()
                .companyName("")
                .address("Valid Company Address 123")
                .city("Valid City")
                .postalCode("12-345")
                .phone("*********")
                .nip("*********0")
                .build();
    }
    /**
     * Creates a company billing address without phone number (optional field testing)
     */
    public static BillingAddress createCompanyBillingAddressWithoutPhone() {
        return BillingAddress.builder()
                .companyName("Valid Company Name")
                .address("Valid Company Address 123")
                .city("Valid City")
                .postalCode("12-345")
                .phone("")
                .nip("*********0")
                .build();
    }

    /**
     * Creates a personal billing address without phone number (optional field testing)
     */
    public static BillingAddress createPersonalBillingAddressWithoutPhone() {
        return BillingAddress.builder()
                .companyName("Valid Personal Name")
                .address("Valid Personal Address 123")
                .city("Valid City")
                .postalCode("12-345")
                .phone("")
                .nip("")
                .build();
    }

    /**
     * Creates a company billing address with phone number (optional field testing)
     */
    public static BillingAddress createCompanyBillingAddressWithPhone() {
        return BillingAddress.builder()
                .companyName("Valid Company Name")
                .address("Valid Company Address 123")
                .city("Valid City")
                .postalCode("12-345")
                .phone("*********")
                .nip("*********0")
                .build();
    }

    /**
     * Creates a personal billing address with phone number (optional field testing)
     */
    public static BillingAddress createPersonalBillingAddressWithPhone() {
        return BillingAddress.builder()
                .companyName("Valid Personal Name")
                .address("Valid Personal Address 123")
                .city("Valid City")
                .postalCode("12-345")
                .phone("*********")
                .nip("")
                .build();
    }

    /**
     * Creates a company billing address with empty NIP for validation testing
     */
    public static BillingAddress createCompanyBillingAddressWithoutNip() {
        return BillingAddress.builder()
                .companyName("Valid Company Name")
                .address("Valid Company Address 123")
                .city("Valid City")
                .postalCode("12-345")
                .phone("*********")
                .nip("")
                .build();
    }

    /**
     * Creates a company billing address with empty address for validation testing
     */
    public static BillingAddress createCompanyBillingAddressWithoutAddress() {
        return BillingAddress.builder()
                .companyName("Valid Company Name")
                .address("")
                .city("Valid City")
                .postalCode("12-345")
                .phone("*********")
                .nip("*********0")
                .build();
    }

    /**
     * Creates a company billing address with empty city for validation testing
     */
    public static BillingAddress createCompanyBillingAddressWithoutCity() {
        return BillingAddress.builder()
                .companyName("Valid Company Name")
                .address("Valid Company Address 123")
                .city("")
                .postalCode("12-345")
                .phone("*********")
                .nip("*********0")
                .build();
    }

    /**
     * Creates a company billing address with empty post code for validation testing
     */
    public static BillingAddress createCompanyBillingAddressWithoutPostCode() {
        return BillingAddress.builder()
                .companyName("Valid Company Name")
                .address("Valid Company Address 123")
                .city("Valid City")
                .postalCode("")
                .phone("*********")
                .nip("*********0")
                .build();
    }

    // ========== INVALID PHONE PREFIX VALIDATION METHODS ==========

    /**
     * Creates a shipping address with invalid phone number prefix for validation testing
     */
    public static ShippingAddress createShippingAddressWithInvalidPhonePrefix() {
        return ShippingAddress.builder()
                .personalInfo(PersonalInfo.builder()
                        .email("<EMAIL>")
                        .firstName("ValidFirst")
                        .lastName("ValidLast")
                        .phone(RandomDataGenerator.generateInvalidPrefixPhoneNumber())
                        .build())
                .address("Valid Address 123")
                .city("Valid City")
                .postalCode("12-345")
                .companyName("")
                .build();
    }

    /**
     * Creates a personal billing address with invalid phone number prefix for validation testing
     */
    public static BillingAddress createPersonalBillingAddressWithInvalidPhonePrefix() {
        return BillingAddress.builder()
                .companyName("Valid Personal Name")
                .address("Valid Personal Address 123")
                .city("Valid City")
                .postalCode("12-345")
                .phone(RandomDataGenerator.generateInvalidPrefixPhoneNumber())
                .nip("")
                .build();
    }

    /**
     * Creates a company billing address with invalid phone number prefix for validation testing
     */
    public static BillingAddress createCompanyBillingAddressWithInvalidPhonePrefix() {
        return BillingAddress.builder()
                .companyName("Valid Company Name")
                .address("Valid Company Address 123")
                .city("Valid City")
                .postalCode("12-345")
                .phone(RandomDataGenerator.generateInvalidPrefixPhoneNumber())
                .nip("*********0")
                .build();
    }

}