package common.test_data;

import lombok.Builder;
import lombok.Getter;

/**
 * Represents shipping address information for test data
 * Includes all fields needed for a personal shipping form including optional company name
 */
@Getter
@Builder
public class ShippingAddress {
    private final PersonalInfo personalInfo;
    private final String address;
    private final String city;
    private final String postalCode;
    private final String companyName; // Optional - for individuals who want to include company name

    public ShippingAddress(PersonalInfo personalInfo, String address, String city, 
                          String postalCode, String companyName) {
        this.personalInfo = personalInfo;
        this.address = address;
        this.city = city;
        this.postalCode = postalCode;
        this.companyName = companyName;
    }

    /**
     * Creates a default individual shipping address (no company name)
     */
    public static ShippingAddress defaultIndividualShipping() {
        return ShippingAddress.builder()
                .personalInfo(PersonalInfo.defaultPersonalInfo())
                .address("123 Main Street")
                .city("Test City")
                .postalCode("12-345")
                .companyName(null) // No company name for individual
                .build();
    }

    /**
     * Creates a shipping address for individual with company name
     */
    public static ShippingAddress individualWithCompanyShipping() {
        return ShippingAddress.builder()
                .personalInfo(PersonalInfo.defaultPersonalInfo())
                .address("456 Business Avenue")
                .city("Business City")
                .postalCode("67-890")
                .companyName("Test Company Ltd") // Individual with company name
                .build();
    }

    /**
     * Creates a company shipping address
     */
    public static ShippingAddress defaultCompanyShipping() {
        return ShippingAddress.builder()
                .personalInfo(PersonalInfo.defaultPersonalInfo())
                .address("789 Corporate Street")
                .city("Corporate City")
                .postalCode("11-111")
                .companyName("Automation Testing Corp")
                .build();
    }

    /**
     * Creates an alternative shipping address for different test scenarios
     */
    public static ShippingAddress alternativeShipping() {
        return ShippingAddress.builder()
                .personalInfo(PersonalInfo.alternativePersonalInfo())
                .address("321 Alternative Road")
                .city("Alternative City")
                .postalCode("99-999")
                .build();
    }

    // Convenience methods
    public String getEmail() {
        return personalInfo.getEmail();
    }

    public String getFirstName() {
        return personalInfo.getFirstName();
    }

    public String getLastName() {
        return personalInfo.getLastName();
    }

    public String getPhone() {
        return personalInfo.getPhone();
    }

    public boolean hasCompanyName() {
        return companyName != null && !companyName.trim().isEmpty();
    }
}
