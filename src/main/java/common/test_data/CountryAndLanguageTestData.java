package common.test_data;

import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CountryAndLanguageTestData {

    /**
     * Inner class to hold complete country and language information
     */
    public static class CountryLanguageInfo {
        @Getter
        private final String englishName;
        @Getter
        private final String polishNameForSelection;
        @Getter
        private final List<String> availableLanguages;
        private final Map<String, String> countryNamesInLanguages;
        @Getter
        private final String currency;
        @Getter
        private final String urlCountryCode;
        private final Map<String, String> footerFormats;

        public CountryLanguageInfo(String englishName, String polishNameForSelection, List<String> availableLanguages,
                                 Map<String, String> countryNamesInLanguages, String currency,
                                 String urlCountryCode, Map<String, String> footerFormats) {
            this.englishName = englishName;
            this.polishNameForSelection = polishNameForSelection;
            this.availableLanguages = availableLanguages;
            this.countryNamesInLanguages = countryNamesInLanguages;
            this.currency = currency;
            this.urlCountryCode = urlCountryCode;
            this.footerFormats = footerFormats;
        }

        public String getCountryNameInLanguage(String language) {
            return countryNamesInLanguages.getOrDefault(language, englishName);
        }

        public String getFooterFormat(String language) {
            return footerFormats.getOrDefault(language, "");
        }
    }

    // Main data structure holding all country information
    private static final Map<String, CountryLanguageInfo> COUNTRY_DATA = new HashMap<>();

    static {
        // Austria - German only
        COUNTRY_DATA.put("Austria", new CountryLanguageInfo(
            "Austria",
            "Austria",
                List.of("Deutsch"),
            Map.of("Deutsch", "Österreich"),
            "EUR",
            "AT",
            Map.of("Deutsch", "Österreich • EUR • Deutsch")
        ));

        // Belgium - English only
        COUNTRY_DATA.put("Belgium", new CountryLanguageInfo(
            "Belgium",
            "Belgia",
                List.of("English"),
            Map.of("English", "Belgium"),
            "EUR",
            "BE",
            Map.of("English", "Belgium • EUR • English")
        ));

        // Bulgaria - English only
        COUNTRY_DATA.put("Bulgaria", new CountryLanguageInfo(
            "Bulgaria",
            "Bułgaria",
                List.of("English"),
            Map.of("English", "Bulgaria"),
            "BGN",
            "BG",
            Map.of("English", "Bulgaria • BGN • English")
        ));

        // Croatia - English only
        COUNTRY_DATA.put("Croatia", new CountryLanguageInfo(
            "Croatia",
            "Chorwacja",
                List.of("English"),
            Map.of("English", "Croatia"),
            "EUR",
            "HR",
            Map.of("English", "Croatia • EUR • English")
        ));

        // Czech Republic - English only
        COUNTRY_DATA.put("Czech Republic", new CountryLanguageInfo(
            "Czech Republic",
            "Czechy",
                List.of("English"),
            Map.of("English", "Czech Republic"),
            "CZK",
            "CZ",
            Map.of("English", "Czech Republic • CZK • English")
        ));

        // Denmark - English only
        COUNTRY_DATA.put("Denmark", new CountryLanguageInfo(
            "Denmark",
            "Dania",
                List.of("English"),
            Map.of("English", "Denmark"),
            "DKK",
            "DK",
            Map.of("English", "Denmark • DKK • English")
        ));

        // Estonia - English only
        COUNTRY_DATA.put("Estonia", new CountryLanguageInfo(
            "Estonia",
            "Estonia",
                List.of("English"),
            Map.of("English", "Estonia"),
            "EUR",
            "EE",
            Map.of("English", "Estonia • EUR • English")
        ));

        // Finland - English only
        COUNTRY_DATA.put("Finland", new CountryLanguageInfo(
            "Finland",
            "Finlandia",
                List.of("English"),
            Map.of("English", "Finland"),
            "EUR",
            "FI",
            Map.of("English", "Finland • EUR • English")
        ));

        // France - English only
        COUNTRY_DATA.put("France", new CountryLanguageInfo(
            "France",
            "Francja",
                List.of("English"),
            Map.of("English", "France"),
            "EUR",
            "FR",
            Map.of("English", "France • EUR • English")
        ));

        // Germany - German only
        COUNTRY_DATA.put("Deutschland", new CountryLanguageInfo(
            "Deutschland",
            "Niemcy",
                List.of("Deutsch"),
            Map.of("Deutsch", "Deutschland"),
            "EUR",
            "DE",
            Map.of("Deutsch", "Deutschland • EUR • Deutsch")
        ));

        // Greece - English only
        COUNTRY_DATA.put("Greece", new CountryLanguageInfo(
            "Greece",
            "Grecja",
                List.of("English"),
            Map.of("English", "Greece"),
            "EUR",
            "GR",
            Map.of("English", "Greece • EUR • English")
        ));

        // Hungary - English only
        COUNTRY_DATA.put("Hungary", new CountryLanguageInfo(
            "Hungary",
            "Węgry",
                List.of("English"),
            Map.of("English", "Hungary"),
            "HUF",
            "HU",
            Map.of("English", "Hungary • HUF • English")
        ));

        // Ireland - English only
        COUNTRY_DATA.put("Ireland", new CountryLanguageInfo(
            "Ireland",
            "Irlandia",
                List.of("English"),
            Map.of("English", "Ireland"),
            "EUR",
            "IE",
            Map.of("English", "Ireland • EUR • English")
        ));

        // Italy - English only
        COUNTRY_DATA.put("Italy", new CountryLanguageInfo(
            "Italy",
            "Włochy",
                List.of("English"),
            Map.of("English", "Italy"),
            "EUR",
            "IT",
            Map.of("English", "Italy • EUR • English")
        ));

        // Latvia - English only
        COUNTRY_DATA.put("Latvia", new CountryLanguageInfo(
            "Latvia",
            "Łotwa",
                List.of("English"),
            Map.of("English", "Latvia"),
            "EUR",
            "LV",
            Map.of("English", "Latvia • EUR • English")
        ));

        // Lithuania - English only
        COUNTRY_DATA.put("Lithuania", new CountryLanguageInfo(
            "Lithuania",
            "Litwa",
                List.of("English"),
            Map.of("English", "Lithuania"),
            "EUR",
            "LT",
            Map.of("English", "Lithuania • EUR • English")
        ));

        // Luxembourg - English only
        COUNTRY_DATA.put("Luxembourg", new CountryLanguageInfo(
            "Luxembourg",
            "Luksemburg",
                List.of("English"),
            Map.of("English", "Luxembourg"),
            "EUR",
            "LU",
            Map.of("English", "Luxembourg • EUR • English")
        ));

        // Netherlands - English only
        COUNTRY_DATA.put("Netherlands", new CountryLanguageInfo(
            "Netherlands",
            "Holandia",
                List.of("English"),
            Map.of("English", "Netherlands"),
            "EUR",
            "NL",
            Map.of("English", "Netherlands • EUR • English")
        ));

        // Poland - Polish and English
        COUNTRY_DATA.put("Poland", new CountryLanguageInfo(
            "Poland",
            "Polska",
            Arrays.asList("Polski", "English"),
            Map.of("Polski", "Polska", "English", "Poland"),
            "PLN",
            "PL",
            Map.of("Polski", "Polska • PLN • Polski", "English", "Poland • PLN • English")
        ));

        // Portugal - English only
        COUNTRY_DATA.put("Portugal", new CountryLanguageInfo(
            "Portugal",
            "Portugalia",
                List.of("English"),
            Map.of("English", "Portugal"),
            "EUR",
            "PT",
            Map.of("English", "Portugal • EUR • English")
        ));

        // Romania - English only
        COUNTRY_DATA.put("Romania", new CountryLanguageInfo(
            "Romania",
            "Rumunia",
                List.of("English"),
            Map.of("English", "Romania"),
            "RON",
            "RO",
            Map.of("English", "Romania • RON • English")
        ));

        // Slovakia - English only
        COUNTRY_DATA.put("Slovakia", new CountryLanguageInfo(
            "Slovakia",
            "Słowacja",
                List.of("English"),
            Map.of("English", "Slovakia"),
            "EUR",
            "SK",
            Map.of("English", "Slovakia • EUR • English")
        ));

        // Slovenia - English only
        COUNTRY_DATA.put("Slovenia", new CountryLanguageInfo(
            "Slovenia",
            "Słowenia",
                List.of("English"),
            Map.of("English", "Slovenia"),
            "EUR",
            "SI",
            Map.of("English", "Slovenia • EUR • English")
        ));

        // Spain - English only
        COUNTRY_DATA.put("Spain", new CountryLanguageInfo(
            "Spain",
            "Hiszpania",
                List.of("English"),
            Map.of("English", "Spain"),
            "EUR",
            "ES",
            Map.of("English", "Spain • EUR • English")
        ));

        // Sweden - English only
        COUNTRY_DATA.put("Sweden", new CountryLanguageInfo(
            "Sweden",
            "Szwecja",
                List.of("English"),
            Map.of("English", "Sweden"),
            "SEK",
            "SE",
            Map.of("English", "Sweden • SEK • English")
        ));

        // United Kingdom - English only
        COUNTRY_DATA.put("United Kingdom", new CountryLanguageInfo(
            "United Kingdom",
            "Wielka Brytania",
                List.of("English"),
            Map.of("English", "United Kingdom"),
            "GBP",
            "GB",
            Map.of("English", "United Kingdom • GBP • English")
        ));
    }

    /**
     * Get country information by country name
     */
    public static CountryLanguageInfo getCountryInfo(String countryName) {
        return COUNTRY_DATA.get(countryName);
    }

    /**
     * Get all available countries
     */
    public static String[] getAllCountries() {
        return COUNTRY_DATA.keySet().toArray(new String[0]);
    }

    /**
     * Get countries with multiple language options
     */
    public static String[] getMultiLanguageCountries() {
        return COUNTRY_DATA.entrySet().stream()
            .filter(entry -> entry.getValue().getAvailableLanguages().size() > 1)
            .map(Map.Entry::getKey)
            .toArray(String[]::new);
    }

    /**
     * Get countries with only German language
     */
    public static String[] getGermanOnlyCountries() {
        return new String[]{"Austria", "Deutschland"};
    }

    /**
     * Get countries with only English language
     */
    public static String[] getEnglishOnlyCountries() {
        return COUNTRY_DATA.entrySet().stream()
            .filter(entry -> entry.getValue().getAvailableLanguages().size() == 1 
                && entry.getValue().getAvailableLanguages().contains("English"))
            .map(Map.Entry::getKey)
            .toArray(String[]::new);
    }
}
