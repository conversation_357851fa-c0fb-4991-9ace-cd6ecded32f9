package common.test_data;

import lombok.Builder;
import lombok.Getter;

import java.util.Random;

/**
 * Utility class for generating payment-related test data
 * Provides methods for generating BLIK codes and other payment test data
 */
public class PaymentTestData {

    private static final Random random = new Random();

    /**
     * Represents card payment information for test data
     */
    @Getter
    @Builder
    public static class CardPaymentData {
        private final String cardNumber;
        private final String expirationDate;
        private final String cvc;
        private final String cardType;

        public CardPaymentData(String cardNumber, String expirationDate, String cvc, String cardType) {
            this.cardNumber = cardNumber;
            this.expirationDate = expirationDate;
            this.cvc = cvc;
            this.cardType = cardType;
        }
    }

    /**
     * Generate a valid BLIK code for test environment *
     * accepts any BLIK code starting with "777" followed by 3 random digits
     * @return A 6-digit BLIK code starting with "777"
     */
    public static String generateValidBlikCode() {
        // Generate 3 random digits (000-999)
        int randomDigits = random.nextInt(1000);
        // Format to ensure 3 digits with leading zeros if necessary
        return String.format("777%03d", randomDigits);
    }

    /**
     * Generate an invalid BLIK code for negative testing
     * Returns a 6-digit code that doesn't start with "777"
     *
     * @return A 6-digit invalid BLIK code
     */
    public static String generateInvalidBlikCode() {
        // Generate a code that doesn't start with 777
        // Use ranges 100000-776999 to avoid 777xxx codes
        int invalidCode = random.nextInt(677000) + 100000; // 100000 to 776999
        return String.valueOf(invalidCode);
    }


    // ========== CARD PAYMENT DATA ==========

    /**
     * Get default valid card payment data for testing
     * Uses a test card number from adyen that works in test environments
     *
     * @return CardPaymentData with valid test card information
     */
    public static CardPaymentData getDefaultValidCard() {
        return CardPaymentData.builder()
                .cardNumber("5577 0000 5577 0004")
                .expirationDate("03/30")
                .cvc("737")
                .cardType("MasterCard")
                .build();
    }

    /**
     * Get invalid card data for negative testing
     *
     * @return CardPaymentData with invalid card information
     */
    public static CardPaymentData getInvalidCard() {
        return CardPaymentData.builder()
                .cardNumber("4111 1111 1111 1111")
                .expirationDate("12/25")
                .cvc("123")
                .cardType("Visa")
                .build();
    }
}
