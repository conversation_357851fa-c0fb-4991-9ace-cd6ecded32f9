package common.test_data;

import common.constants.CustomerType;

/**
 * Factory class for creating different combinations of test data
 * Provides predefined scenarios for shipping and billing addresses
 */
public class TestDataFactory {

    // ========== SHIPPING ADDRESS SCENARIOS ==========

    /**
     * Individual customer shipping address (no company name)
     */
    public static ShippingAddress getIndividualShipping() {
        return ShippingAddress.defaultIndividualShipping();
    }

    /**
     * Individual customer shipping address with company name
     */
    public static ShippingAddress getIndividualWithCompanyShipping() {
        return ShippingAddress.individualWithCompanyShipping();
    }

    /**
     * Company customer shipping address
     */
    public static ShippingAddress getCompanyShipping() {
        return ShippingAddress.defaultCompanyShipping();
    }

    /**
     * Alternative shipping address for different test scenarios
     */
    public static ShippingAddress getAlternativeShipping() {
        return ShippingAddress.alternativeShipping();
    }

    // ========== BILLING ADDRESS SCENARIOS ==========

    /**
     * Company billing address with valid NIP
     */
    public static BillingAddress getCompanyBilling() {
        return BillingAddress.defaultCompanyBilling();
    }

    /**
     * Individual billing address (no NIP)
     */
    public static BillingAddress getIndividualBilling() {
        return BillingAddress.individualBilling(PersonalInfo.defaultPersonalInfo());
    }

    /**
     * Alternative company billing address
     */
    public static BillingAddress getAlternativeCompanyBilling() {
        return BillingAddress.alternativeCompanyBilling();
    }

    /**
     * Billing address with invalid NIP for negative testing
     */
    public static BillingAddress getInvalidNipBilling() {
        return BillingAddress.invalidNipBilling();
    }

    // ========== COMBINED SCENARIOS ==========

    /**
     * Complete test data for individual customer
     * Same address for shipping and billing
     */
    public static class IndividualCustomerData {
        public static ShippingAddress getShipping() {
            return getIndividualShipping();
        }

        public static BillingAddress getBilling() {
            return getIndividualBilling();
        }
    }

    /**
     * Complete test data for company customer
     * Different addresses for shipping and billing
     */
    public static class CompanyCustomerData {
        public static ShippingAddress getShipping() {
            return getCompanyShipping();
        }

        public static BillingAddress getBilling() {
            return getCompanyBilling();
        }
    }

    /**
     * Complete test data for individual with company shipping
     * Individual ships to company address but bills personally
     */
    public static class IndividualWithCompanyData {
        public static ShippingAddress getShipping() {
            return getIndividualWithCompanyShipping();
        }

        public static BillingAddress getBilling() {
            return getIndividualBilling();
        }
    }

    /**
     * Complete test data for mixed scenario testing
     * Different addresses for comprehensive testing
     */
    public static class MixedScenarioData {
        public static ShippingAddress getShipping() {
            return getAlternativeShipping();
        }

        public static BillingAddress getBilling() {
            return getAlternativeCompanyBilling();
        }
    }

    /**
     * Get shipping address based on a customer type
     */
    public static ShippingAddress getShippingAddress(CustomerType customerType) {
        return switch (customerType) {
            case INDIVIDUAL -> getIndividualShipping();
            case COMPANY -> getCompanyShipping();
            default -> throw new IllegalArgumentException("Unknown customer type: " + customerType);
        };
    }

    /**
     * Get billing address based on a customer type
     */
    public static BillingAddress getBillingAddress(CustomerType customerType) {
        return switch (customerType) {
            case INDIVIDUAL -> getIndividualBilling();
            case COMPANY -> getCompanyBilling();
            default -> throw new IllegalArgumentException("Unknown customer type: " + customerType);
        };
    }

    /**
     * Get shipping address with a company name option
     */
    public static ShippingAddress getShippingAddress(CustomerType customerType, boolean includeCompanyName) {
        if (customerType == CustomerType.INDIVIDUAL) {
            return includeCompanyName ? getIndividualWithCompanyShipping() : getIndividualShipping();
        } else {
            return getCompanyShipping(); // Company always has company name
        }
    }

    // ========== PAYMENT TEST DATA ==========

    /**
     * Generate a valid BLIK code for test environment *
     * accepts any BLIK code starting with "777" followed by 3 random digits
     */
    public static String generateValidBlikCode() {
        return PaymentTestData.generateValidBlikCode();
    }

    /**
     * Generate an invalid BLIK code for negative testing
     */
    public static String generateInvalidBlikCode() {
        return PaymentTestData.generateInvalidBlikCode();
    }

    /**
     * Get default valid card payment data for testing
     */
    public static PaymentTestData.CardPaymentData getDefaultValidCard() {
        return PaymentTestData.getDefaultValidCard();
    }

    /**
     * Get invalid card data for negative testing
     */
    public static PaymentTestData.CardPaymentData getInvalidCard() {
        return PaymentTestData.getInvalidCard();
    }
    /**
     * Default user credentials
     */
    public static UserCredentials getDefaultUserCredentials() {
        return UserCredentials.defaultCredentials();
    }

    /**
     * Company user credentials
     */
    public static UserCredentials getCompanyUserCredentials() {
        return UserCredentials.companyCredentials();
    }

    /**
     * Get user credentials based on a user type
     */
    public static UserCredentials getUserCredentials(String userType) {
        return switch (userType.toLowerCase()) {
            case "individual" -> UserCredentials.defaultCredentials();
            case "company" -> UserCredentials.companyCredentials();
            case "logged_in_order" -> UserCredentials.loggedInOrderCredentials();
            case "logged_in_order_2" -> UserCredentials.loggedInOrderCredentials2();
            default -> throw new IllegalArgumentException("Unknown user type: " + userType);
        };
    }

    /**
     * Get logged in order credentials - first account
     */
    public static UserCredentials getLoggedInOrderCredentials() {
        return UserCredentials.loggedInOrderCredentials();
    }

    /**
     * Get logged in order credentials - second account
     */
    public static UserCredentials getLoggedInOrderCredentials2() {
        return UserCredentials.loggedInOrderCredentials2();
    }
}
