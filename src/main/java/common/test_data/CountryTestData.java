package common.test_data;

import java.util.HashMap;
import java.util.Map;

/**
 * Provides country-specific test data like phone numbers and postal codes
 * for international testing scenarios
 */
public class CountryTestData {

    // Map of country names to their complete valid phone numbers (with country code)
    private static final Map<String, String> COUNTRY_PHONE_NUMBERS = new HashMap<>();

    static {
        COUNTRY_PHONE_NUMBERS.put("Austria", "+43720116400");
        COUNTRY_PHONE_NUMBERS.put("Belgia", "+32460123456");
        COUNTRY_PHONE_NUMBERS.put("Bułgaria", "+359888123456");
        COUNTRY_PHONE_NUMBERS.put("Chorwacja", "+38598123456");
        COUNTRY_PHONE_NUMBERS.put("Cypr", "+35799123456");
        COUNTRY_PHONE_NUMBERS.put("Czechy", "+420601123456");
        COUNTRY_PHONE_NUMBERS.put("Dania", "+4540123456");
        COUNTRY_PHONE_NUMBERS.put("Estonia", "+37258123456");
        COUNTRY_PHONE_NUMBERS.put("Finlandia", "+35850123456");
        COUNTRY_PHONE_NUMBERS.put("Francja", "+33612345678");
        COUNTRY_PHONE_NUMBERS.put("Grecja", "+306912345678");
        COUNTRY_PHONE_NUMBERS.put("Hiszpania", "+34612345678");
        COUNTRY_PHONE_NUMBERS.put("Holandia", "+31612345678");
        COUNTRY_PHONE_NUMBERS.put("Irlandia", "+353871234567");
        COUNTRY_PHONE_NUMBERS.put("Islandia", "+3546123456");
        COUNTRY_PHONE_NUMBERS.put("Lichtenstein", "+4237661234");
        COUNTRY_PHONE_NUMBERS.put("Litwa", "+37061234567");
        COUNTRY_PHONE_NUMBERS.put("Łotwa", "+37121234567");
        COUNTRY_PHONE_NUMBERS.put("Luksemburg", "+352661234567");
        COUNTRY_PHONE_NUMBERS.put("Malta", "+35699123456");
        COUNTRY_PHONE_NUMBERS.put("Niemcy", "+491721234567");
        COUNTRY_PHONE_NUMBERS.put("Norwegia", "+4791234567");
        COUNTRY_PHONE_NUMBERS.put("Polska", "+48123456789");
        COUNTRY_PHONE_NUMBERS.put("Portugalia", "+351912345678");
        COUNTRY_PHONE_NUMBERS.put("Rumunia", "+40712345678");
        COUNTRY_PHONE_NUMBERS.put("Słowacja", "+421912345678");
        COUNTRY_PHONE_NUMBERS.put("Słowenia", "+38631234567");
        COUNTRY_PHONE_NUMBERS.put("Szwecja", "+46701234567");
        COUNTRY_PHONE_NUMBERS.put("Węgry", "+36201234567");
        COUNTRY_PHONE_NUMBERS.put("Wielka Brytania", "+447123456789");
        COUNTRY_PHONE_NUMBERS.put("Włochy", "+393401234567");
        // Default for other countries
        COUNTRY_PHONE_NUMBERS.put("DEFAULT", "+421912345678");
    }

    // Map of country names to valid sample postal codes
    private static final Map<String, String> COUNTRY_POSTAL_CODES = new HashMap<>();

    static {
        COUNTRY_POSTAL_CODES.put("Austria", "1010");           // Vienna
        COUNTRY_POSTAL_CODES.put("Belgia", "1000");           // Brussels
        COUNTRY_POSTAL_CODES.put("Bułgaria", "1000");         // Sofia
        COUNTRY_POSTAL_CODES.put("Chorwacja", "10000");       // Zagreb
        COUNTRY_POSTAL_CODES.put("Cypr", "1010");             // Nicosia
        COUNTRY_POSTAL_CODES.put("Czechy", "110 00");         // Prague
        COUNTRY_POSTAL_CODES.put("Dania", "1050");            // Copenhagen
        COUNTRY_POSTAL_CODES.put("Estonia", "10001");         // Tallinn
        COUNTRY_POSTAL_CODES.put("Finlandia", "00100");       // Helsinki
        COUNTRY_POSTAL_CODES.put("Francja", "75001");         // Paris
        COUNTRY_POSTAL_CODES.put("Grecja", "104 31");         // Athens
        COUNTRY_POSTAL_CODES.put("Hiszpania", "28001");       // Madrid
        COUNTRY_POSTAL_CODES.put("Holandia", "1012");         // Amsterdam
        COUNTRY_POSTAL_CODES.put("Irlandia", "D01 F5P2");     // Dublin
        COUNTRY_POSTAL_CODES.put("Islandia", "101");          // Reykjavik
        COUNTRY_POSTAL_CODES.put("Lichtenstein", "9490");     // Vaduz
        COUNTRY_POSTAL_CODES.put("Litwa", "01001");           // Vilnius
        COUNTRY_POSTAL_CODES.put("Łotwa", "LV-1050");         // Riga
        COUNTRY_POSTAL_CODES.put("Luksemburg", "1111");       // Luxembourg
        COUNTRY_POSTAL_CODES.put("Malta", "VLT 1117");        // Valletta
        COUNTRY_POSTAL_CODES.put("Niemcy", "10115");          // Berlin
        COUNTRY_POSTAL_CODES.put("Norwegia", "0001");         // Oslo
        COUNTRY_POSTAL_CODES.put("Polska", "00-001");         // Warsaw
        COUNTRY_POSTAL_CODES.put("Portugalia", "1000-001");   // Lisbon
        COUNTRY_POSTAL_CODES.put("Rumunia", "010001");        // Bucharest
        COUNTRY_POSTAL_CODES.put("Słowacja", "811 01");       // Bratislava
        COUNTRY_POSTAL_CODES.put("Słowenia", "1000");         // Ljubljana
        COUNTRY_POSTAL_CODES.put("Szwecja", "111 21");        // Stockholm
        COUNTRY_POSTAL_CODES.put("Węgry", "1011");            // Budapest
        COUNTRY_POSTAL_CODES.put("Wielka Brytania", "SW1A 1AA"); // London
        COUNTRY_POSTAL_CODES.put("Włochy", "00100");          // Rome
        // Default for other countries
        COUNTRY_POSTAL_CODES.put("DEFAULT", "10000");
    }

    /**
     * Get a valid phone number for the specified country
     *
     * @param country The country name in Polish
     * @return A valid phone number for that country
     */
    public static String getPhoneNumberForCountry(String country) {
        return COUNTRY_PHONE_NUMBERS.getOrDefault(country, COUNTRY_PHONE_NUMBERS.get("DEFAULT"));
    }

    /**
     * Get a valid postal code for the specified country
     *
     * @param country The country name in Polish
     * @return A valid postal code for that country
     */
    public static String getPostalCodeForCountry(String country) {
        return COUNTRY_POSTAL_CODES.getOrDefault(country, COUNTRY_POSTAL_CODES.get("DEFAULT"));
    }

    /**
     * Get a list of all European countries available in the dropdown
     *
     * @return Array of country names in Polish
     */
    public static String[] getAllEuropeanCountries() {
        return new String[]{
                "Austria", "Belgia", "Bułgaria", "Chorwacja", "Cypr", "Czechy", "Dania",
                "Estonia", "Finlandia", "Francja", "Grecja", "Hiszpania", "Holandia",
                "Irlandia", "Islandia", "Lichtenstein", "Litwa", "Łotwa", "Luksemburg",
                "Malta", "Niemcy", "Norwegia", "Portugalia", "Rumunia", "Słowacja",
                "Słowenia", "Szwecja", "Węgry", "Wielka Brytania", "Włochy"
        };
    }

    /**
     * Get a list of common European countries available in the dropdown
     *
     * @return Array of country names in Polish
     */
    public static String[] getCommonEuropeanCountries() {
        return new String[]{
                "Niemcy", "Francja", "Włochy", "Hiszpania", "Wielka Brytania",
                "Czechy", "Słowacja", "Austria", "Belgia", "Holandia"
        };
    }

    /**
     * Get a random country that is not Poland
     *
     * @return A random country name in Polish
     */
    public static String getRandomNonPolishCountry() {
        String[] countries = getCommonEuropeanCountries();
        int randomIndex = (int) (Math.random() * countries.length);
        return countries[randomIndex];
    }
}