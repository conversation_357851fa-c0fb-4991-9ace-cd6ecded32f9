package common.test_data;

import lombok.Builder;
import lombok.Getter;

/**
 * Represents basic personal information for test data
 */
@Getter
@Builder
public class PersonalInfo {
    private final String email;
    private final String firstName;
    private final String lastName;
    private final String phone;

    public PersonalInfo(String email, String firstName, String lastName, String phone) {
        this.email = email;
        this.firstName = firstName;
        this.lastName = lastName;
        this.phone = phone;
    }

    /**
     * Creates default personal info for testing
     */
    public static PersonalInfo defaultPersonalInfo() {
        return PersonalInfo.builder()
                .email("<EMAIL>")
                .firstName("Automation")
                .lastName("Test")
                .phone("123456789")
                .build();
    }

    /**
     * Creates an alternative personal info for testing different scenarios
     */
    public static PersonalInfo alternativePersonalInfo() {
        return PersonalInfo.builder()
                .email("<EMAIL>")
                .firstName("Test")
                .lastName("User")
                .phone("987654321")
                .build();
    }
}
