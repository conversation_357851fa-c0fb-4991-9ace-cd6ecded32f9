package pages;

import common.constants.PageType;
import net.serenitybdd.core.pages.PageObject;
import objects_behaviors.implementation.WebElement;
import objects_behaviors.rules.IWebElement;
import org.openqa.selenium.By;

import java.util.Objects;

public class BasePage extends PageObject {
    private static final String
            MAIN_CATEGORY_ITEM = "//div[contains(@class,'menu-bold')]//span[text()='%s']",
            SUBCATEGORY_ITEM = "//a[.//div[text()='%s']]",
            CART = "//a[.//span[contains(@class, 'hidden lg:block') and (normalize-space(text())='Koszyk' or normalize-space(text())='Cart')]]\n",
            SELECT_COUNTRY_OPTION = "//div[@id=\"select-options\"]//li//p[contains(text(), '%s')]",
            SELECT_LANGUAGE_OPTION = "//div[@id=\"select-options\"]//li//p[contains(text(), '%s')]";
    private static final By
            MY_ACCOUNT_BUTTON = By.xpath("//a//span[contains(text(), '<PERSON>je konto')]"),
            PAYMENT_AND_DELIVERY_DETAILS_CONTAINER = By.xpath("//main | //div[contains(@class, 'content')] | //div[contains(@class, 'page-content')] | //body"),
            PAGE_TITLE = By.xpath("//h3[contains(text(), 'Płatność i dostawa')] | //h1 | //title"),
            SELECT_COUNTRY = By.xpath("(//div[contains(@class, 'hover:text-primary ')]//..//div//*[local-name()='svg']//..//..//div)[1]"),
            SELECT_COUNTRY_DROPDOWN = By.xpath("(//button[@aria-controls=\"select-options\"])[1]"),
            SELECT_LANGUAGE_DROPDOWN = By.xpath("(//button[@aria-controls=\"select-options\"])[2]"),
            SAVE_AND_GO_TO_SHOP_BUTTON = By.xpath("//button//span[contains(text(), 'Zapisz i przejdź do sklepu')]//..//..//button"),
            CHANGE_COUNTRY_AND_LANGUAGE_HEADER_AT_FOOTER = By.xpath("(//div[@data-sentry-source-file=\"ChangeLanguageButton.tsx\"]//span)[1]"),
            COUNTRY_CURRENCY_LANGUAGE_HEADER_AT_FOOTER = By.xpath("(//div[@data-sentry-source-file=\"ChangeLanguageButton.tsx\"]//span)[2]"),
            COUNTRY_NAME_ON_HEADER = By.xpath("(//div[contains(@class, 'hover:text-primary')])[1]"),
            SELECT_COUNTRY_MODAL_HEADER = By.xpath("//div//h5"),
            SELECT_COUNTRY_MODAL_DESCRIPTION = By.xpath("//div[@class =\"body_s text-tertiary py-6\"]");

    public WebElement getMainCategoryItem(String category) {
        return new WebElement(findBy(String.format(MAIN_CATEGORY_ITEM, category)));
    }

    public WebElement getSubCategoryItem(String subCategory) {
        return new WebElement(findBy(String.format(SUBCATEGORY_ITEM, subCategory)));
    }

    public WebElement openCart() {
        return new WebElement(findBy(CART));
    }

    public WebElement cartCounter() {
        return new WebElement(findBy("//a[contains(@href, '/cart')]//span[contains(text(), '(')]"));
    }

    public void openPage(String pageName) {
        String baseUrl = Objects.requireNonNull(getDriver().getCurrentUrl()).split("/")[0] + "//" + getDriver().getCurrentUrl().split("//")[1].split("/")[0];
        try {
            PageType pageType = PageType.fromDisplayName(pageName);
            getDriver().get(baseUrl + pageType.getUrl());
            waitABit(3000);

        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException(e.getMessage());
        }
    }

    public IWebElement myAccountButton() {
        return new WebElement($(MY_ACCOUNT_BUTTON), "My Account Button");
    }

    public IWebElement paymentAndDeliveryDetailsContainer() {
        return new WebElement($(PAYMENT_AND_DELIVERY_DETAILS_CONTAINER), "Payment and Delivery Details Container");
    }

    public IWebElement pageTitle() {
        return new WebElement($(PAGE_TITLE), "Payment and Delivery Page Title");
    }

    public IWebElement selectCountry() {
        return new WebElement($(SELECT_COUNTRY), "Select Country");
    }

    public IWebElement selectCountryDropdown() {
        return new WebElement($(SELECT_COUNTRY_DROPDOWN), "Select Country Dropdown");
    }

    public IWebElement selectLanguageDropdown() {
        return new WebElement($(SELECT_LANGUAGE_DROPDOWN), "Select Language Dropdown");
    }

    public IWebElement selectCountryOption(String countryName) {
        var element = String.format(SELECT_COUNTRY_OPTION, countryName);
        return new WebElement($(element), String.format("%s Country Option", countryName));
    }

    public IWebElement selectLanguageOption(String languageName) {
        var element = String.format(SELECT_LANGUAGE_OPTION, languageName);
        return new WebElement($(element), String.format("%s Language Option", languageName));
    }

    public IWebElement saveAndGoToShopButton() {
        return new WebElement($(SAVE_AND_GO_TO_SHOP_BUTTON), "Save and Go to Shop Button");
    }

    public IWebElement changeCountryAndLanguageHeaderAtFooter() {
        return new WebElement($(CHANGE_COUNTRY_AND_LANGUAGE_HEADER_AT_FOOTER), "Change Country and Language Header at Footer");
    }

    public IWebElement countryCurrencyLanguageHeaderAtFooter() {
        return new WebElement($(COUNTRY_CURRENCY_LANGUAGE_HEADER_AT_FOOTER), "Country Currency Language Header at Footer");
    }

    public IWebElement countryNameOnHeader() {
        return new WebElement($(COUNTRY_NAME_ON_HEADER), "Country Name on Header");
    }
    public IWebElement selectCountryModalHeader() {
        return new WebElement($(SELECT_COUNTRY_MODAL_HEADER), "Select Country Modal Header");
    }

    public IWebElement selectCountryModalDescription() {
        return new WebElement($(SELECT_COUNTRY_MODAL_DESCRIPTION), "Select Country Modal Description");
    }
}
