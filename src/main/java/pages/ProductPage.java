package pages;

import net.serenitybdd.core.pages.PageObject;
import objects_behaviors.implementation.Button;
import objects_behaviors.implementation.InputField;
import objects_behaviors.implementation.WebElement;
import objects_behaviors.implementation.WebList;
import objects_behaviors.rules.IButton;
import objects_behaviors.rules.IInputField;
import objects_behaviors.rules.IWebElement;
import objects_behaviors.rules.IWebList;
import org.openqa.selenium.By;

public class ProductPage extends PageObject {
    private static final String
            PRODUCT_NAME = "//div[contains(@class, 'order-0')]//h1[contains(@class, 'header-m')]",
            PRODUCT_PRICE = "//div[@data-sentry-component=\"VariantPricing\"]//div[contains(@class, 'body-m_bold')]",
            PRODUCT_QUANTITY = "//input[@type='text' and contains(@class, 'text-center')]",
            ADD_TO_CART_BUTTON = "//button[@type=\"button\"]//span[contains(text(), \"Do koszyka\")]",
            DISCOUNT_PRICE = "//div[contains(@class, 'body-m_bold text-red-primary my-auto')]",
            OPEN_CART_BUTTON_AFTER_ADDING_PRODUCT = "//a[@title=\"Dodano. Zobacz koszyk\"]";

    private final By
            BREADCRUMBS_ON_PRODUCT_PAGE = By.xpath("//div[@data-sentry-component=\"Breadcrumbs\"]"),
            PRODUCT_SKU = By.xpath("//div[@data-sentry-component=\"ProductSku\"]"),
            PRODUCT_IMAGE = By.xpath("//div[@data-sentry-component=\"ProductImageContainer\"]"),
            INCLUDES_VAT_TEXT = By.xpath("//div[@class=\"body-xs text-secondary\"]"),
            WITHOUT_DELIVERY_COST_LINK = By.xpath("(//a[@href=\"/pl/PL/pg/dostawa-i-platnosc\"])[1]"),
            PLUS_BUTTON = By.xpath("//*[local-name()='svg' and @data-sentry-element=\"PlusSmallIcon\"]"),
            MINUS_BUTTON = By.xpath("//*[local-name()='svg' and @data-sentry-element=\"MinusSmallIcon\"]"),
            PRODUCT_AMOUNT_INPUT = By.xpath("//input[@value=\"1\"]"),
            DELIVERY_PRICE_HEADER = By.xpath("//span[contains(text(), 'Dostawa od:')]"),
            DELIVERY_PRICE_HEADER_GENERIC = By.xpath("//span[contains(text(), 'Dostawa')]"),
            DELIVERY_DETAILS_LINK = By.xpath("//a[contains(text(), 'Szczegóły dostawy')]"),
            PRODUCT_DESCRIPTION = By.xpath("//div[@data-sentry-component=\"Description\"]"),
            DIMENSION_AND_DETAILS_BUTTON = By.xpath("//button//span[contains(text(), 'Wymiary i szczegóły')]"),
            DIMENSION_AND_DETAILS_HEADER = By.xpath("(//span[contains(text(), 'Wymiary i szczegóły')])[2]"),
            DIMENSION_AND_DETAILS_CONTENT = By.xpath("//div[@class='flex flex-col gap-y-3']"),
            DIMENSION_AND_DETAILS_GRAPHIC = By.xpath("//div[contains(@class, 'rid hidden grid-cols-1')]"),
            QUESTION_AND_ANSWERS_BUTTON = By.xpath("//button//span[contains(text(), 'Pytania i odpowiedzi')]"),
            QUESTION_AND_ANSWERS_HEADER = By.xpath("(//span[contains(text(), 'Pytania i odpowiedzi')])[2]"),
            QUESTION_AND_ANSWERS_CONTENT = By.xpath("//div[@data-sentry-component=\"QuestionAndAnswer\"]"),
            ASK_QUESTION_BUTTON = By.xpath("//button//span[contains(text(), \"Zadaj pytanie\")]"),
            EMAIL_INPUT_FOR_QUESTION = By.xpath("//input[@placeholder=\"Wpisz adres e-mail\"]"),
            MESSAGE_INPUT_FOR_QUESTION = By.xpath("//textarea[@placeholder=\"Zacznij pisać...\"]"),
            AGREE_TO_PRIVACY_POLICY_CHECKBOX = By.xpath("//input[@type=\"checkbox\"]"),
            SEND_QUESTION_BUTTON = By.xpath("//button//span[contains(text(), \"Wyślij\")]"),
            ERROR_MESSAGE_FOR_UNSELECTED_PRIVACY_POLICY_CHECKBOX = By.xpath("//div[contains(@class, \"text-error\")]"),
            MESSAGE_SENT_TEXT = By.xpath("//span[contains(text(), \"Wysłano\")]"),
            RELATED_PRODUCTS_HEADER = By.xpath("//div[contains(text(), 'Powiązane produkty')]"),
            RELATED_PRODUCTS = By.xpath("//div[contains(text(), \"Powiązane produkty\")]"),
            PRODUCT_QUANTITY_INPUT = By.xpath("//input[@type='text' and contains(@class, 'text-center')]"),
            RELATED_PRODUCTS_LIST = By.xpath("//div[@data-sentry-component=\"ProductBox\"]//a"),
            RELATED_PRODUCT_NAMES = By.xpath("//div[@data-sentry-component=\"ProductBox\"]//p[contains(@class, 'header-xs') and contains(@class, 'line-clamp-2')]"),
            RELATED_PRODUCT_PRICES = By.xpath("//div[@data-sentry-component=\"ProductBox\"]//div[@data-sentry-component=\"PriceDisplay\"]"),
            PRODUCT_ADDED_VIEW_CART_BUTTON = By.xpath("//span[contains(text(), \"Dodano. Zobacz koszyk\")]"),
            MAXIMUM_PRODUCT_QUANTITY_ERROR_MESSAGE = By.xpath("//div[contains(@class, \"text-error body-xs absolute\")]"),
            BREADCRUMB_LINKS = By.xpath("//div[@data-sentry-component=\"Breadcrumbs\"]//a"),
            QUESTION_AND_ANSWERS_LIST = By.xpath("//div[@data-sentry-component=\"QuestionAndAnswer\"]//div[@data-sentry-component=\"Accordion\"]//div[@role=\"button\"]");


    public IWebElement productName() {
        return new WebElement($(PRODUCT_NAME), "Product Name");
    }

    public IWebElement productPrice() {
        return new WebElement($(PRODUCT_PRICE), "Product Price");
    }

    public IWebElement productQuantity() {
        return new WebElement($(PRODUCT_QUANTITY), "Product Quantity");
    }

    public IButton addToCartButton() {
        return new Button($(ADD_TO_CART_BUTTON), "Add to Cart Button");
    }

    public IWebElement discountPrice() {
        return new WebElement($(DISCOUNT_PRICE), "Discount Price");
    }

    public IWebElement openCartButtonAfterAddingProduct() {
        return new WebElement($(OPEN_CART_BUTTON_AFTER_ADDING_PRODUCT), "Open Cart Button After Adding Product");
    }

    public IWebElement breadcrumbsOnProductPage() {
        return new WebElement($(BREADCRUMBS_ON_PRODUCT_PAGE), "Breadcrumbs on Product Page");
    }

    public IWebElement productSku() {
        return new WebElement($(PRODUCT_SKU), "Product SKU");
    }

    public IWebElement productImage() {
        return new WebElement($(PRODUCT_IMAGE), "Product Image");
    }

    public IWebElement includesVatText() {
        return new WebElement($(INCLUDES_VAT_TEXT), "Includes VAT Text");
    }

    public IWebElement withoutDeliveryCostLink() {
        return new WebElement($(WITHOUT_DELIVERY_COST_LINK), "Without Delivery Cost Link");
    }

    public IWebElement plusButton() {
        return new WebElement($(PLUS_BUTTON), "Plus Button");
    }

    public IWebElement minusButton() {
        return new WebElement($(MINUS_BUTTON), "Minus Button");
    }

    public IInputField productAmountInput() {
        return new InputField($(PRODUCT_AMOUNT_INPUT), "Product Amount Input");
    }

    public IWebElement deliveryPriceHeader() {
        return new WebElement($(DELIVERY_PRICE_HEADER), "Delivery Price Header");
    }

    public IWebElement deliveryPriceHeaderGeneric() {
        return new WebElement($(DELIVERY_PRICE_HEADER_GENERIC), "Delivery Price Header Generic");
    }

    public IWebElement deliveryDetailsLink() {
        return new WebElement($(DELIVERY_DETAILS_LINK), "Delivery Details Link");
    }

    public IWebElement productDescription() {
        return new WebElement($(PRODUCT_DESCRIPTION), "Product Description");
    }

    public IWebElement dimensionAndDetailsButton() {
        return new WebElement($(DIMENSION_AND_DETAILS_BUTTON), "Dimension and Details Button");
    }

    public IWebElement dimensionAndDetailsHeader() {
        return new WebElement($(DIMENSION_AND_DETAILS_HEADER), "Dimension and Details Header");
    }

    public IWebElement dimensionAndDetailsContent() {
        return new WebElement($(DIMENSION_AND_DETAILS_CONTENT), "Dimension and Details Content");
    }

    public IWebElement dimensionAndDetailsGraphic() {
        return new WebElement($(DIMENSION_AND_DETAILS_GRAPHIC), "Dimension and Details Graphic");
    }

    public IWebElement questionAndAnswersButton() {
        return new WebElement($(QUESTION_AND_ANSWERS_BUTTON), "Question and Answers Button");
    }

    public IWebElement questionAndAnswersHeader() {
        return new WebElement($(QUESTION_AND_ANSWERS_HEADER), "Question and Answers Header");
    }

    public IWebElement questionAndAnswersContent() {
        return new WebElement($(QUESTION_AND_ANSWERS_CONTENT), "Question and Answers Content");
    }

    public IButton askQuestionButton() {
        return new Button($(ASK_QUESTION_BUTTON), "Ask Question Button");
    }

    public IInputField emailInputForQuestion() {
        return new InputField($(EMAIL_INPUT_FOR_QUESTION), "Email Input for Question");
    }

    public IInputField messageInputForQuestion() {
        return new InputField($(MESSAGE_INPUT_FOR_QUESTION), "Message Input for Question");
    }

    public IWebElement agreeToPrivacyPolicyCheckbox() {
        return new WebElement($(AGREE_TO_PRIVACY_POLICY_CHECKBOX), "Agree to Privacy Policy Checkbox");
    }

    public IButton sendMessageButton() {
        return new Button($(SEND_QUESTION_BUTTON), "Send Message Button");
    }

    public IWebElement errorMessageForUnselectedPrivacyPolicyCheckbox() {
        return new WebElement($(ERROR_MESSAGE_FOR_UNSELECTED_PRIVACY_POLICY_CHECKBOX), "Error Message for Unselected Privacy Policy Checkbox");
    }

    public IWebElement messageSentText() {
        return new WebElement($(MESSAGE_SENT_TEXT), "Message Sent Text");
    }

    public IWebElement relatedProductsHeader() {
        return new WebElement($(RELATED_PRODUCTS_HEADER), "Related Products Header");
    }

    public IWebElement relatedProducts() {
        return new WebElement($(RELATED_PRODUCTS), "Related Products");
    }

    public IInputField productQuantityInput() {
        return new InputField($(PRODUCT_QUANTITY_INPUT), "Product Quantity Input");
    }

    public IWebList relatedProductsList() {
        return new WebList($$(RELATED_PRODUCTS_LIST), "Related Products List");
    }

    public IWebList relatedProductNames() {
        return new WebList($$(RELATED_PRODUCT_NAMES), "Related Product Names");
    }

    public IWebList relatedProductPrices() {
        return new WebList($$(RELATED_PRODUCT_PRICES), "Related Product Prices");
    }

    public IWebElement productAddedViewCartButton() {
        return new WebElement($(PRODUCT_ADDED_VIEW_CART_BUTTON), "Product Added View Cart Button");
    }

    public IWebElement maximumProductQuantityErrorMessage() {
        return new WebElement($(MAXIMUM_PRODUCT_QUANTITY_ERROR_MESSAGE), "Maximum Product Quantity Error Message");
    }

    public IWebList breadcrumbLinks() {
        return new WebList($$(BREADCRUMB_LINKS), "Breadcrumb Links");
    }
    public IWebList questionAndAnswersList() {
        return new WebList($$(QUESTION_AND_ANSWERS_LIST), "Question and Answers List");
    }
}
