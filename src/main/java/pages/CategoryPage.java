package pages;

import net.serenitybdd.core.steps.UIInteractionSteps;
import objects_behaviors.implementation.WebElement;
import objects_behaviors.rules.IWebElement;
import org.openqa.selenium.By;

public class CategoryPage extends UIInteractionSteps {
    private final By
            categoryPageHeader = By.xpath("/html/body/div/main/h1"),
            categoryPageDescription = By.xpath("/html/body/div[4]/main/div[2]/div/p"),
            seeMoreProductsButton = By.xpath("//button[@type=\"button\"]//span[contains(text(), \"Więcej produktów\")]");

    public IWebElement categoryPageHeader() {
        return new WebElement($(categoryPageHeader), "Category Page Header");
    }
    public IWebElement categoryPageDescription() {
        return new WebElement($(categoryPageDescription), "Category Page Description");
    }
    public IWebElement seeMoreProductsButton() {
        return new WebElement($(seeMoreProductsButton), "See More Products Button");
    }

}
