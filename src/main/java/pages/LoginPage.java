package pages;


import net.serenitybdd.core.pages.PageObject;
import objects_behaviors.implementation.CheckBox;
import objects_behaviors.implementation.InputField;
import objects_behaviors.implementation.WebElement;
import objects_behaviors.rules.ICheckBox;
import objects_behaviors.rules.IInputField;
import objects_behaviors.rules.IWebElement;
import org.openqa.selenium.By;

public class LoginPage extends PageObject {
    private static final By
            LOGIN_LINK = By.xpath("//span[contains(text(), \"Zaloguj się\")]"),
            EMAIL_INPUT = By.xpath("//input[@name='email']"),
            PASSWORD_INPUT = By.xpath("//input[@name='password']"),
            LOGIN_BUTTON = By.xpath("//span[contains(text(), \"Zaloguj się\")]"),
            USER_ACCOUNT_ICON = By.xpath("//span[contains(text(),\"Moje konto\")]//..//*[local-name()='svg']"),
            LOGOUT_LINK = By.xpath("//div[@role='button' and contains(@class, 'fg-brand-primary') and contains(@class, 'cursor-pointer')]"),
            LOGIN_ERROR_MESSAGE = By.xpath("//div[contains(@class, 'text-error')]"),
            FORGOT_PASSWORD_LINK = By.xpath("//span[contains(text(), \"Nie pamiętasz hasła?\")]"),
            MY_ACCOUNT_BUTTON = By.xpath("//a//span[contains(text(), 'Moje konto')]"),
            REGISTER_LINK = By.xpath("//div[contains(text(), 'Zarejestruj się')]"),
            YOU_DONT_HAVE_AN_ACCOUNT_YET_TEXT = By.xpath("//div[contains(text(), 'Nie masz konta?')]"),
            YOU_DONT_REMEMBER_YOUR_PASSWORD_LINK = By.xpath("//span[contains(text(), 'Nie pamiętasz hasła?')]"),
            LOGIN_MODAL_HEADER = By.xpath("(//div[contains(text(), 'Zaloguj się')])[1]"),
            LOGIN_MODAL_SUB_HEADER = By.xpath("//div[contains(text(), 'Zaloguj się, aby uzyskać dostęp do konta.')]"),
            REGISTER_MODAL_HEADER = By.xpath("//div[contains(text(), 'Zarejestruj się')]"),
            LOGIN_LINK_ON_REGISTER_MODAL = By.xpath("//div[contains(text(), 'Zaloguj się')]"),
            FORGOT_PASSWORD_MODAL_HEADER = By.xpath("//div[contains(text(), 'Nie pamiętasz hasła?')]"),
            FORGOT_PASSWORD_DESCRIPTION = By.xpath("//div[contains(text(), 'Podaj poniżej adres e-mail użyty podczas rejestracji, aby otrzymać link do zresetowania hasła.')]"),
            RESET_PASSWORD_BUTTON = By.xpath("//button//span[contains(text(), 'Resetuj hasło')]"),
            RESET_PASSWORD_SUCCESS_MESSAGE_HEADER = By.xpath("//div[contains(text(), 'Prosimy o potwierdzenie')]"),
            RESET_PASSWORD_SUCCESS_MESSAGE = By.xpath("/html/body/dialog/div[2]/div[2]/div/div/div/p"),
            CLOSE_RESET_PASSWORD_MODAL_BUTTON = By.xpath("//div[@class=\"absolute right-0 z-50 cursor-pointer\"]"),
            INVALID_EMAIL_ERROR_MESSAGE = By.xpath("//div[contains(text(), 'Podaj poprawny adres email')]"),
            EMPTY_PASSWORD_ERROR_MESSAGE = By.xpath("//p[contains(text(), 'Pole nie może być puste')]"),
            RETURN_TO_LOGIN_BUTTON_ON_RESET_PASSWORD_MODAL = By.xpath("(//div[contains(@class, 'text-brand-primary')]//text()//..)[1]"),
            INVALID_LOGIN_ERROR_MESSAGE = By.xpath("//div[contains(text(), 'Nieprawidłowy adres e-mail lub hasło. Spróbuj ponownie.')]"),
            CLOSE_MODAL_BUTTON = By.xpath("//*[local-name()='svg' and @data-sentry-element=\"CloseIcon\"]"),
            AGREEMENT_CHECKBOX_FOR_REGISTRATION = By.xpath("//input[@type='checkbox']"),
            REGISTRATION_FIRST_NAME_INPUT = By.xpath("//input[@name='firstName']"),
            REGISTRATION_LAST_NAME_INPUT = By.xpath("//input[@name='lastName']"),
            REGISTRATION_EMAIL_INPUT = By.xpath("//input[@name='email']"),
            REGISTRATION_PASSWORD_INPUT = By.xpath("//input[@name='password']"),
            REGISTRATION_BUTTON = By.xpath("//button//span[contains(text(), 'Zarejestruj się')]"),
            REGISTRATION_SUCCESS_MESSAGE_HEADER = By.xpath("//div[contains(text(), 'Potwierdź swój adres email')]"),
            REGISTRATION_SUCCESS_MESSAGE = By.xpath("//div[contains(text(), 'Konto zostało utworzone. Na Twój adres email został wysłany link aktywacyjny.')]"),
            CLOSE_REGISTRATION_SUCCESS_MODAL_BUTTON = By.xpath("//*[local-name()='svg' and @data-sentry-element=\"CloseIcon\"]"),
            REGISTRATION_AGREEMENT_CHECKBOX_LABEL = By.xpath("/html/body/dialog/div[2]/div[2]/div/div/div/div[6]/div/div[2]"),
            AGREEMENT_CHECKBOX_NOT_CHECKED_ERROR_MESSAGE = By.xpath("//div[contains(text(), 'Zaznaczenie tego pola jest niezbędne do założenia konta')]"),
            INCORRECT_EMAIL_FORMAT_ERROR_MESSAGE = By.xpath("//p[contains(text(), 'Nieprawidłowy format adresu email')]"),
            DO_YOU_HAVE_AN_ACCOUNT_TEXT_ON_REGISTER_MODAL = By.xpath("//div[contains(text(), 'Masz już konto?')]");

    public IWebElement getLoginLink() {
        return new WebElement($(LOGIN_LINK), "Login Link");
    }

    public IInputField getEmailInput() {
        return new InputField($(EMAIL_INPUT), "Email Input");
    }

    public IInputField getPasswordInput() {
        return new InputField($(PASSWORD_INPUT), "Password Input");
    }

    public IWebElement getLoginButton() {
        return new WebElement($(LOGIN_BUTTON), "Login Button");
    }

    public IWebElement getUserAccountIcon() {
        return new WebElement($(USER_ACCOUNT_ICON), "User Account Icon");
    }

    public IWebElement getLogoutLink() {
        return new WebElement($(LOGOUT_LINK), "Logout Link");
    }

    public IWebElement getLoginErrorMessage() {
        return new WebElement($(LOGIN_ERROR_MESSAGE), "Login Error Message");
    }
    public IWebElement getForgotPasswordLink() {
        return new WebElement($(FORGOT_PASSWORD_LINK), "Forgot Password Link");
    }

    public IWebElement myAccountButton() {
        return new WebElement($(MY_ACCOUNT_BUTTON), "My Account Button");
    }

    public IWebElement registerLink() {
        return new WebElement($(REGISTER_LINK), "Register Link");
    }

    public IWebElement youDontHaveAnAccountYetText() {
        return new WebElement($(YOU_DONT_HAVE_AN_ACCOUNT_YET_TEXT), "You Don't Have an Account Yet Text");
    }

    public IWebElement youDontRememberYourPasswordLink() {
        return new WebElement($(YOU_DONT_REMEMBER_YOUR_PASSWORD_LINK), "You Don't Remember Your Password Link");
    }

    public IWebElement loginModalHeader() {
        return new WebElement($(LOGIN_MODAL_HEADER), "Login Modal Header");
    }

    public IWebElement loginModalSubHeader() {
        return new WebElement($(LOGIN_MODAL_SUB_HEADER), "Login Modal Sub Header");
    }

    public IWebElement registerModalHeader() {
        return new WebElement($(REGISTER_MODAL_HEADER), "Register Modal Header");
    }

    public IWebElement loginLinkOnRegisterModal() {
        return new WebElement($(LOGIN_LINK_ON_REGISTER_MODAL), "Login Link on Register Modal");
    }

    public IWebElement forgotPasswordModalHeader() {
        return new WebElement($(FORGOT_PASSWORD_MODAL_HEADER), "Forgot Password Modal Header");
    }

    public IWebElement forgotPasswordDescription() {
        return new WebElement($(FORGOT_PASSWORD_DESCRIPTION), "Forgot Password Description");
    }

    public IWebElement resetPasswordButton() {
        return new WebElement($(RESET_PASSWORD_BUTTON), "Reset Password Button");
    }

    public IWebElement resetPasswordSuccessMessageHeader() {
        return new WebElement($(RESET_PASSWORD_SUCCESS_MESSAGE_HEADER), "Reset Password Success Message Header");
    }

    public IWebElement resetPasswordSuccessMessage() {
        return new WebElement($(RESET_PASSWORD_SUCCESS_MESSAGE), "Reset Password Success Message");
    }

    public IWebElement closeResetPasswordModalButton() {
        return new WebElement($(CLOSE_RESET_PASSWORD_MODAL_BUTTON), "Close Reset Password Modal Button");
    }

    public IWebElement invalidEmailErrorMessage() {
        return new WebElement($(INVALID_EMAIL_ERROR_MESSAGE), "Invalid Email Error Message");
    }

    public IWebElement emptyPasswordErrorMessage() {
        return new WebElement($(EMPTY_PASSWORD_ERROR_MESSAGE), "Empty Password Error Message");
    }

    public IWebElement returnToLoginButtonOnResetPasswordModal() {
        return new WebElement($(RETURN_TO_LOGIN_BUTTON_ON_RESET_PASSWORD_MODAL), "Return to Login Button on Reset Password Modal");
    }

    public IWebElement invalidLoginErrorMessage() {
        return new WebElement($(INVALID_LOGIN_ERROR_MESSAGE), "Invalid Login Error Message");
    }

    public IWebElement loginButton() {
        return new WebElement($(LOGIN_BUTTON), "Login Button");
    }

    public IWebElement loginHeader() {
        return new WebElement($(LOGIN_MODAL_HEADER), "Login Header");
    }

    public IWebElement getCloseModalButton() {
        return new WebElement($(CLOSE_MODAL_BUTTON), "Close Modal Button");
    }

    public ICheckBox agreementCheckboxForRegistration() {
        return new CheckBox($(AGREEMENT_CHECKBOX_FOR_REGISTRATION), "Agreement Checkbox for Registration");
    }

    public IInputField registrationFirstNameInput() {
        return new InputField($(REGISTRATION_FIRST_NAME_INPUT), "Registration First Name Input");
    }

    public IInputField registrationLastNameInput() {
        return new InputField($(REGISTRATION_LAST_NAME_INPUT), "Registration Last Name Input");
    }

    public IInputField registrationEmailInput() {
        return new InputField($(REGISTRATION_EMAIL_INPUT), "Registration Email Input");
    }

    public IInputField registrationPasswordInput() {
        return new InputField($(REGISTRATION_PASSWORD_INPUT), "Registration Password Input");
    }

    public IWebElement registrationButton() {
        return new WebElement($(REGISTRATION_BUTTON), "Registration Button");
    }

    public IWebElement registrationSuccessMessageHeader() {
        return new WebElement($(REGISTRATION_SUCCESS_MESSAGE_HEADER), "Registration Success Message Header");
    }

    public IWebElement registrationSuccessMessage() {
        return new WebElement($(REGISTRATION_SUCCESS_MESSAGE), "Registration Success Message");
    }

    public IWebElement closeRegistrationSuccessModalButton() {
        return new WebElement($(CLOSE_REGISTRATION_SUCCESS_MODAL_BUTTON), "Close Registration Success Modal Button");
    }

    public IWebElement registrationAgreementCheckboxLabel() {
        return new WebElement($(REGISTRATION_AGREEMENT_CHECKBOX_LABEL), "Registration Agreement Checkbox Label");
    }

    public IWebElement agreementCheckboxNotCheckedErrorMessage() {
        return new WebElement($(AGREEMENT_CHECKBOX_NOT_CHECKED_ERROR_MESSAGE), "Agreement Checkbox Not Checked Error Message");
    }

    public IWebElement incorrectEmailFormatErrorMessage() {
        return new WebElement($(INCORRECT_EMAIL_FORMAT_ERROR_MESSAGE), "Incorrect Email Format Error Message");
    }

    public IWebElement doYouHaveAnAccountTextOnRegisterModal() {
        return new WebElement($(DO_YOU_HAVE_AN_ACCOUNT_TEXT_ON_REGISTER_MODAL), "Do You Have an Account Text on Register Modal");
    }

}