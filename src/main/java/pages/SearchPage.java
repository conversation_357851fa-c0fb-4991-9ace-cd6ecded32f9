package pages;

import net.serenitybdd.core.pages.PageObject;
import objects_behaviors.implementation.InputField;
import objects_behaviors.implementation.WebElement;
import objects_behaviors.implementation.WebList;
import objects_behaviors.rules.IInputField;
import objects_behaviors.rules.IWebElement;
import objects_behaviors.rules.IWebList;
import org.openqa.selenium.By;

public class SearchPage extends PageObject {
    private static final String
            SEARCH_RESULT_HEADER = "//h1[contains(text(), 'Wyniki wyszukiwania dla')]",
            SORT_OPTION = "//ul[contains(@class, 'thin-scrollbar')]//button[.//p[text()='%s']]",
            APPLIED_SORT_HEADER = "//button[@aria-label='Open select']//p[contains(text(), '%s')]";

    private final By
            SEARCH_INPUT = By.xpath("//input[@placeholder='Szukaj...']\n"),
            NO_RESULT_MESSAGE = By.xpath("//h1[contains(text(), 'Nie znaleziono produktów')]"),
            NUMBER_OF_PRODUCTS_HEADER = By.xpath("/html/body/main/div/div[2]/div/div[1]/div"),
            PRODUCT_NAME = By.xpath("//p[contains(@class, 'header-xs') and contains(@class, 'line-clamp-2')] | //h2[contains(@class, 'header-xs')]"),
            SORT_BUTTON = By.xpath("//button[.//span[contains(text(), 'Sortuj')]]\n"),
            CLOSE_SEARCH_BAR_BUTTON = By.xpath("//div[@class='flex cursor-pointer items-center']//span[text()='Zamknij']"),
            SEARCH_DROPDOWN_CONTAINER = By.xpath("//div[contains(@class, 'mb-6') and contains(@class, 'overflow')]"),
            SEARCH_DROPDOWN_CATEGORY_LIST = By.xpath("//div[@class='lg:w-1/2'][.//div[text()='Kategorie']]//a//span[@class='ml-4']"),
            SEARCH_DROPDOWN_RECOMMENDED_LIST = By.xpath("//div[@class='mt-8 lg:mt-0 lg:w-1/2'][.//div[text()='Polecane']]//a//span[@class='ml-4']"),
            SEARCH_DROPDOWN_BESTSELLER_LIST_CONTAINER = By.xpath("//div[@class='mt-8 lg:mt-0 lg:w-1/2'][.//div[text()='Bestsellery']]//a//span[@class='ml-4']"),
            SEARCH_DROPDOWN_INSPIRATIONS_LIST_CONTAINER = By.xpath("//div[@data-sentry-component='Popular']\n"),
            CLOSE_QUICK_SEARCH_DROPDOWN_BUTTON = By.xpath("//span[contains(text(), 'Zamknij')]"),
            PRODUCT_PRICE_ON_SEARCH_PAGE = By.xpath("//div[@data-sentry-component=\"PriceDisplay\"]"),
            DISCOUNT_PRODUCT_PRICE_ON_SEARCH_PAGE = By.xpath("//span[contains(@class, \"body-m_bold text-red\")]"),
            SEARCH_ICON = By.xpath("//*[local-name()='svg' and @class=\"absolute top-4 right-6 z-30 cursor-pointer\"]"),
            SEE_MORE_PRODUCTS_BUTTON = By.xpath("//button[.//span[contains(text(), 'Zobacz więcej')]]");


    public IInputField searchInput() {
        return new InputField($(SEARCH_INPUT), "Search Input");
    }

    public IWebElement searchResultHeader() {
        return new WebElement($(SEARCH_RESULT_HEADER), "Search Result Header");
    }

    public IWebElement noResultMessage() {
        return new WebElement($(NO_RESULT_MESSAGE), "No Result Message");
    }

    public IWebElement numberOfProductsHeader() {
        return new WebElement($(NUMBER_OF_PRODUCTS_HEADER), "Number of Products Header");
    }

    public IWebList productName() {
        return new WebList($$(PRODUCT_NAME), "Product Name");
    }

    public IWebElement sortButton() {
        return new WebElement($(SORT_BUTTON), "Sort Button");
    }

    public IWebElement sortOption(String sortOption) {
        var element = String.format(SORT_OPTION, sortOption);
        return new WebElement($(element), String.format("%s Sort Option", sortOption));
    }

    public IWebElement closeSearchBarButton() {
        return new WebElement($(CLOSE_SEARCH_BAR_BUTTON), "Close Search Bar Button");
    }

    public IWebElement searchDropdownContainer() {
        return new WebElement($(SEARCH_DROPDOWN_CONTAINER), "Search Dropdown Container");
    }

    public IWebList searchDropdownCategoryList() {
        return new WebList($$(SEARCH_DROPDOWN_CATEGORY_LIST), "Search Dropdown Category List");
    }

    public IWebList searchDropdownRecommendedList() {
        return new WebList($$(SEARCH_DROPDOWN_RECOMMENDED_LIST), "Search Dropdown Recommended List");
    }

    public IWebElement searchDropdownBestsellerListContainer() {
        return new WebElement($(SEARCH_DROPDOWN_BESTSELLER_LIST_CONTAINER), "Search Dropdown Bestseller List Container");
    }

    public IWebElement searchDropdownInspirationsListContainer() {
        return new WebElement($(SEARCH_DROPDOWN_INSPIRATIONS_LIST_CONTAINER), "Search Dropdown Inspirations List Container");
    }

    public IWebElement closeQuickSearchDropdownButton() {
        return new WebElement($(CLOSE_QUICK_SEARCH_DROPDOWN_BUTTON), "Close Quick Search Dropdown Button");
    }

    public IWebList productPriceOnSearchPage() {
        return new WebList($$(PRODUCT_PRICE_ON_SEARCH_PAGE), "Product Price on Search Page");
    }

    public IWebElement searchIcon() {
        return new WebElement($(SEARCH_ICON), "Search Icon");
    }
    public IWebList discountProductPriceOnSearchPage() {
        return new WebList($$(DISCOUNT_PRODUCT_PRICE_ON_SEARCH_PAGE), "Discount Product Price on Search Page");
    }
    public IWebList productPriceOnSearchPageWithoutDiscount() {
        return new WebList($$(PRODUCT_PRICE_ON_SEARCH_PAGE), "Product Price on Search Page Without Discount");
    }

    public IWebList discountedProductPriceOnSearchPage() {
        return new WebList($$(DISCOUNT_PRODUCT_PRICE_ON_SEARCH_PAGE), "Product Price on Search Page With Discount");
    }

    public IWebElement appliedSortHeader(String sortOption) {
        var element = String.format(APPLIED_SORT_HEADER, sortOption);
        return new WebElement($(element), String.format("%s Applied Sort Header", sortOption));
    }

    public IWebElement seeMoreProductsButton() {
        return new WebElement($(SEE_MORE_PRODUCTS_BUTTON), "See More Products Button");
    }
}
