package pages;

import net.thucydides.core.pages.PageObject;
import objects_behaviors.implementation.Button;
import objects_behaviors.implementation.InputField;
import objects_behaviors.implementation.WebElement;
import objects_behaviors.implementation.WebList;
import objects_behaviors.rules.IButton;
import objects_behaviors.rules.IInputField;
import objects_behaviors.rules.IWebElement;
import objects_behaviors.rules.IWebList;

public class CartPage extends PageObject {
    private static final String PRODUCT_NAME_IN_CART = "//a[@data-sentry-component='LinkWrapper']//div[contains(@class, 'header-xs')]",
            PRODUCT_PRICE_IN_CART = "//div[@class='hidden lg:block']//div[@data-sentry-component='CartPriceDisplay']//span[contains(@class, 'body-m_bold') and contains(@class, 'text-primary')]",
            DISCOUNT_PRICE_IN_CART = "//span[contains(@class, 'body-m_bold text-red-primary')]",
            TOTAL_PRICE_IN_CART = "(//div[contains(text(), 'Wartość koszyka') or contains(text(), 'Cart summary')])/../div[2]",
            PRODUCT_QUANTITY_IN_CART = "//div[@class='hidden lg:block']//input[@type='text' and contains(@class, 'text-center')]",
            ADD_PRODUCT_QUANTITY_BUTTON = "/html/body/main/div/div[2]/div[1]/div[2]/div/div[1]/div[2]/div/div[2]/div/div[2]/div[2]/div/button[2]",
            SUBTRACT_PRODUCT_QUANTITY_BUTTON = "/html/body/main/div/div[2]/div[1]/div[2]/div/div[1]/div[2]/div/div[2]/div/div[2]/div[2]/div/button[1]",
            REMOVE_ITEM_BUTTON = "(//button[contains(@class, 'rounded-full') and contains(@class, 'bg-tertiary')])[1]//div",
            CART_MESSAGE = "//div[contains(@class, 'border-primary') and contains(@class, 'rounded-full')]//span[contains(@class, 'body-s')]",
            COUPON_CODE_INPUT = "//input[@placeholder='Kod rabatowy']",
            VAT_AMOUNT = "//div[div[text()='Podatek VAT']]/div[2]",
            VAT_HEADER = "//div[div[text()='Podatek VAT']]/div[1]",
            PRICE_DOES_NOT_INCLUDE_DELIVERY_HEADER = "//div[@class='mt-2' and text()='Cena nie zawiera']",
            DELIVERY_PRICE_HEADER = "//div[@data-sentry-component='ShippingCost' and contains(@class,'flex')]",
            APPLY_COUPON_BUTTON = "//div[contains(@class, 'flex-column') and contains(@class, 'cursor-pointer') and contains(@class, 'right-6')]",
            EMPTY_CART_MESSAGE_HEADER = "//h2[text()='Koszyk jest pusty']",
            EMPTY_CART_MESSAGE_DESCRIPTION = "//p[@class='whitespace-pre-line body-m text-secondary']",
            CONTINUE_SHOPPING_BUTTON = "//a[contains(@class, 'bg-brand-primary') and contains(@class, 'cursor-pointer')]",
            TOTAL_PRICE_IN_CART_HEADER = "//div[contains(text(), 'Wartość koszyka')]/../div[1]",
            OUT_OF_STOCK_MESSAGE_AT_CART = "//div[contains(@class, 'text-error')]",
            FREE_SHIPPING_THRESHOLD_MESSAGE = "//div[contains(text(), 'Do darmowej dostawy brakuje')]",
            FREE_SHIPPING_THRESHOLD_REMAINING_VALUE = "//div[contains(text(), 'Do darmowej dostawy brakuje')]//..//div[2]/span",
            FREE_DELIVERY_MESSAGE = "//div[contains(text(), 'Zamówienie z darmową dostawą')]",
            ACTIVE_COUPON_CODE_HEADER = "//div[contains(text(), 'Aktywny kod:') or contains(text(), 'Active code:')]",
            APPLIED_DISCOUNT_NAME = "(//div[contains(text(), 'Aktywny kod:') or contains(text(), 'Active code:')])//..//div[2]/div",
            REMOVE_COUPON_BUTTON = "//*[local-name()='svg' and @data-sentry-element='CloseIcon']",
            APPLIED_DISCOUNT_HEADER = "//div[contains(text(), 'Rabat') or contains(text(), 'Discount')]",
            APPLIED_DISCOUNT_VALUE = "(//div[contains(text(), 'Rabat') or contains(text(), 'Discount')])/../div[2]",
            INVALID_DISCOUNT_CODE_MESSAGE = "//p[contains(text(), 'Nieprawidłowy kod rabatowy')]",
            PRODUCT_CODE_IN_CART = "(//div[contains(@class, ' self-end px-2')])[1]",
            FREE_SHIPPING_THRESHOLD_PROCESS_BAR = "//div[@data-sentry-component='ProgressBar']//div[@style='width: %s%%;']",
            SHIPPING_PRICE_IN_CART = "//div[div[text()='Dostawa']]/div[2]",
            REMAINING_AMOUNT_FOR_FREE_SHIPPING = "//div[div[text()='Do darmowej dostawy brakuje']]/div[2]//span";


    public IWebList productNameInCart() {
        return new WebList($$(PRODUCT_NAME_IN_CART), "Product Name in Cart");
    }

    public IWebList productPriceInCart() {
        return new WebList($$(PRODUCT_PRICE_IN_CART), "Product Price in Cart");
    }

    public IWebList discountPriceInCart() {
        return new WebList($$(DISCOUNT_PRICE_IN_CART), "Discount Price in Cart");
    }

    public IWebElement totalPriceInCart() {
        return new WebElement($(TOTAL_PRICE_IN_CART), "Total Price in Cart");
    }

    public IWebList productQuantityInCart() {
        return new WebList($$(PRODUCT_QUANTITY_IN_CART), "Product Quantity in Cart");
    }

    public IInputField productQuantityInput() {
        return new InputField($(PRODUCT_QUANTITY_IN_CART), "Product Quantity Input");
    }

    public IButton addProductQuantityButton() {
        return new Button($(ADD_PRODUCT_QUANTITY_BUTTON), "Add Product Quantity Button");
    }

    public IButton subtractProductQuantityButton() {
        return new Button($(SUBTRACT_PRODUCT_QUANTITY_BUTTON), "Subtract Product Quantity Button");
    }

    public IWebList removeItemButton() {
        return new WebList($$(REMOVE_ITEM_BUTTON), "Remove Item Button");
    }

    public IWebElement cartMessage() {
        return new WebElement($(CART_MESSAGE), "Cart Message");
    }

    public IInputField couponCodeInput() {
        return new InputField($(COUPON_CODE_INPUT), "Coupon Code Input");
    }

    public IWebElement vatAmount() {
        return new WebElement($(VAT_AMOUNT), "VAT Amount");
    }

    public IWebElement vatHeader() {
        return new WebElement($(VAT_HEADER), "VAT Header");
    }

    public IWebElement priceDoesNotIncludeDeliveryHeader() {
        return new WebElement($(PRICE_DOES_NOT_INCLUDE_DELIVERY_HEADER), "Price Does Not Include Delivery Header");
    }

    public IWebElement deliveryPriceHeader() {
        return new WebElement($(DELIVERY_PRICE_HEADER), "Delivery Price Header");
    }

    public IWebElement applyCouponButton() {
        return new WebElement($(APPLY_COUPON_BUTTON), "Apply Coupon Button");

    }

    public IWebElement emptyCartMessageHeader() {
        return new WebElement($(EMPTY_CART_MESSAGE_HEADER), "Empty Cart Message Header");
    }

    public IWebElement emptyCartMessageDescription() {
        return new WebElement($(EMPTY_CART_MESSAGE_DESCRIPTION), "Empty Cart Message Description");
    }

    public IWebElement continueShoppingButton() {
        return new WebElement($(CONTINUE_SHOPPING_BUTTON), "Continue Shopping Button");
    }

    public IWebElement totalPriceInCartHeader() {
        return new WebElement($(TOTAL_PRICE_IN_CART_HEADER), "Total Price in Cart Header");
    }

    public IWebElement outOfStockMessageAtCart() {
        return new WebElement($(OUT_OF_STOCK_MESSAGE_AT_CART), "Out of Stock Message at Cart");
    }

    public IWebElement freeShippingThresholdMessage() {
        return new WebElement($(FREE_SHIPPING_THRESHOLD_MESSAGE), "Free Shipping Threshold Message");
    }

    public IWebElement freeShippingThresholdRemainingValue() {
        return new WebElement($(FREE_SHIPPING_THRESHOLD_REMAINING_VALUE), "Free Shipping Threshold Remaining Value");
    }

    public IWebElement freeDeliveryMessage() {
        return new WebElement($(FREE_DELIVERY_MESSAGE), "Free Delivery Message");
    }

    public IWebElement activeCouponCodeHeader() {
        return new WebElement($(ACTIVE_COUPON_CODE_HEADER), "Active Coupon Code Header");
    }

    public IWebElement appliedDiscountName() {
        return new WebElement($(APPLIED_DISCOUNT_NAME), "Applied Discount Name");
    }

    public IWebElement removeCouponButton() {
        return new WebElement($(REMOVE_COUPON_BUTTON), "Remove Coupon Button");
    }

    public IWebElement appliedDiscountHeader() {
        return new WebElement($(APPLIED_DISCOUNT_HEADER), "Applied Discount Header");
    }

    public IWebElement appliedDiscountValue() {
        return new WebElement($(APPLIED_DISCOUNT_VALUE), "Applied Discount Value");
    }

    public IWebElement invalidDiscountCodeMessage() {
        return new WebElement($(INVALID_DISCOUNT_CODE_MESSAGE), "Invalid Discount Code Message");
    }

    public IWebElement productCodeInCart() {
        return new WebElement($(PRODUCT_CODE_IN_CART), "Product Code in Cart");
    }

    public IWebElement freeShippingThresholdProcessBar(String width) {
        return new WebElement($(String.format(FREE_SHIPPING_THRESHOLD_PROCESS_BAR, width)), "Free Shipping Threshold Process Bar");
    }

    public IWebElement shippingPriceInCart() {
        return new WebElement($(SHIPPING_PRICE_IN_CART), "Shipping Price in Cart");
    }
    public IWebElement remainingAmountForFreeShipping() {
        return new WebElement($(REMAINING_AMOUNT_FOR_FREE_SHIPPING), "Remaining Amount for Free Shipping");
    }
}