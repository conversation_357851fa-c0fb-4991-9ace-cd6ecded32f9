package pages;

import net.serenitybdd.core.pages.PageObject;
import objects_behaviors.implementation.WebList;
import objects_behaviors.rules.IWebList;
import org.openqa.selenium.By;

public class ProductListPage extends PageObject {
    private final By
    productList = By.xpath("//div[contains(@data-sentry-component, 'ProductBox') or contains(@data-sentry-component, 'BoxesWithGridContainer')]/a");

    public IWebList productList() {
        return new WebList($$(productList), "Product List");
    }
}
