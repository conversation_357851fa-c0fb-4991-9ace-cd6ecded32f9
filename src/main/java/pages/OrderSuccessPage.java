package pages;

import net.serenitybdd.core.pages.PageObject;
import objects_behaviors.implementation.WebElement;
import objects_behaviors.rules.IWebElement;
import org.openqa.selenium.By;

public class OrderSuccessPage extends PageObject {
    private final By
    ORDER_SUCCESS_THANK_YOU_HEADER = By.xpath("//h2[@class='header-l' and text()='Dziękujemy!']"),
    ORDER_RECEIVED_HEADER = By.xpath("//p[@class='body-m text-secondary mt-6' and contains(text(), 'Wpłynęło do nas Twoje zamówienie o numerze')]"),
    ORDER_STATUS_INFORM_HEADER = By.xpath("//p[contains(text(), 'O statusie zamówienia poinformujemy Cię mailowo.')]"),
    ORDER_PAID_HEADER = By.xpath("//div[contains(@class, 'text-success-primary') and text()='Zamówienie opłacone']"),
    ORDER_NUMBER = By.xpath("//p[contains(text(), \"Wpłynęło do nas Twoje zamówienie o numerz\")]");

    public IWebElement orderSuccessThankYouHeader() {
        return new WebElement(find(ORDER_SUCCESS_THANK_YOU_HEADER), "Order Success Thank You Header");
    }
    public IWebElement orderReceivedHeader() {
        return new WebElement(find(ORDER_RECEIVED_HEADER), "Order Received Header");
    }
    public IWebElement orderStatusInformHeader() {
        return new WebElement(find(ORDER_STATUS_INFORM_HEADER), "Order Status Inform Header");
    }
    public IWebElement orderPaidHeader() {
        return new WebElement(find(ORDER_PAID_HEADER), "Order Paid Header");
    }
    public IWebElement orderNumber() {
        return new WebElement(find(ORDER_NUMBER), "Order Number");
    }
}
