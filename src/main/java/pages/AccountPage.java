package pages;

import net.serenitybdd.core.pages.PageObject;
import objects_behaviors.implementation.CheckBox;
import objects_behaviors.implementation.InputField;
import objects_behaviors.implementation.WebElement;
import objects_behaviors.implementation.WebList;
import objects_behaviors.rules.ICheckBox;
import objects_behaviors.rules.IInputField;
import objects_behaviors.rules.IWebElement;
import objects_behaviors.rules.IWebList;
import org.openqa.selenium.By;

public class AccountPage extends PageObject {
    private static final By
            ACCOUNT_PAGE_HEADER = By.xpath("//div[@data-sentry-component=\"UserPanelContainer\"]//span[contains(text(), '<PERSON>je konto')]"),
            LOGOUT_LINK = By.xpath("//*[local-name()='svg' and @data-sentry-element=\"LogoutIcon\"]//..//..//div"),
            ORDER_HISTORY_LINK = By.xpath("//a[contains(text(), 'Historia zamówień')]"),
            MY_PROFILE_LINK = By.xpath("//a[contains(text(), '<PERSON><PERSON> dane')]"),
            MY_ADDRESS_LINK = By.xpath("//a[contains(text(), 'Moje adresy')]"),
            CONTACT_WITH_THE_STORE_LINK = By.xpath("//a[contains(text(), 'Kontakt ze sklepem')]"),
            LATEST_ORDER_DETAILS_BUTTON = By.xpath("//a[contains(text(), 'Szczegóły zamówienia')]"),
            ORDER_NUMBER_HEADER = By.xpath("//h4[contains(text(), \"Zamówienie nr\")]"),
            ORDER_PLACEMENT_DATE_HEADER = By.xpath("//p[contains(text(), \"Data złożenia zamówienia\")]"),
            ORDER_PLACEMENT_DATE_VALUE = By.xpath("//p[contains(text(), \"Data złożenia zamówienia\")]//..//p[2]"),
            PAYMENT_METHOD_HEADER = By.xpath("//p[contains(text(), \"Metoda płatności\")]//..//p[2]"),
            PAYMENT_METHOD_VALUE = By.xpath("//p[contains(text(), \"Metoda płatności\")]//..//p[2]"),
            ORDER_STATUS_HEADER = By.xpath("//p[contains(text(), \"Status zamówienia\")]"),
            ORDER_STATUS_VALUE = By.xpath("//p[contains(text(), \"Status zamówienia\")]//..//div//div"),
            SHIPPING_STATUS_HEADER = By.xpath("//p[@class=\"body-s mr-2\"]"),
            SHIPPING_STATUS_VALUE = By.xpath("(//div[@data-sentry-component=\"StatusLabel\"])[2]"),
            ORDERED_PRODUCT_NAME = By.xpath("//div[@class='ml-4 w-full']/h5/span[1]"),
            ORDERED_PRODUCT_IMAGE = By.xpath("//div[@class=\"mb-4 flex w-full flex-row items-start\"]//img[@data-sentry-element=\"ImageNext\"]"),
            ORDERED_PRODUCT_PRICE = By.xpath("//div[@class='ml-4 w-full']/h5/span[2]"),
            ORDER_TOTAL_PRICE_HEADER = By.xpath("//p[contains(text(), \"Wartość zamówienia\")]"),
            TOTAL_COST_HEADER = By.xpath("//h5[contains(text(), \"Koszt całkowity\")]"),
            TOTAL_COST_VALUE = By.xpath("//p[contains(text(), \"Koszt zamówienia\")]//..//p[2]"),
            DELIVERY_ADDRESS_HEADER = By.xpath("//h5[contains(text(), \"Adres dostawy\")]"),
            BILLING_ADDRESS_HEADER = By.xpath("//h5[contains(text(), \"Adres do faktury\")]"),
            DELIVERY_ADDRESS_VALUE = By.xpath("(//div[@class=\"text-secondary body-m\"])[1]"),
            BILLING_ADDRESS_VALUE = By.xpath("(//div[@class=\"text-secondary body-m\"])[2]"),
            ORDER_TOTAL_PRICE_VALUE = By.xpath("//p[contains(text(), \"Wartość zamówienia\")]//..//p[2]"),
            ORDER_DETAILS_BUTTON = By.xpath("//a//button//span[contains(text(), 'Szczegóły zamówienia')]"),
            CREATE_A_TICKET_LINK = By.xpath("//span[contains(text(), 'Utwórz zgłoszenie')]"),
            REASON_FOR_CONTACT_DROPDOWN_SELECT = By.xpath("(//button[@aria-label=\"Open select\"])[1]"),
            REASON_FOR_CONTACT_DROPDOWN_OPTIONS = By.xpath("(//label[@class=\"w-full \"])[1]//div[@id=\"select-options\"]//ul//li//p"),
            ORDER_TO_CREATE_A_TICKET_DROPDOWN_SELECT = By.xpath("(//button[@aria-label=\"Open select\"])[2]"),
            ORDER_TO_CREATE_A_TICKET_DROPDOWN_OPTIONS = By.xpath("(//label[@class=\"w-full \"])[2]//div[@id=\"select-options\"]//ul//li//p"),
            MESSAGE_INPUT_FIELD = By.xpath("//textarea[contains(@class, \"text-secondary\")]"),
            SUBMIT_REQUEST_BUTTON = By.xpath("//button[@type=\"submit\"]"),
            REQUEST_SUBMITTED_TEXT = By.xpath("//button[@type=\"submit\"]//span[contains(text(), \"Wysłano\")]"),
            TICKET_CREATED_DATE = By.xpath("//div[@data-sentry-component=\"ContactShop\"]//a[contains(@class, \"text-primary mt-4 flex\")][1]//span[contains(@class, \"body-s text-secondary ml-4\")]"),
            TICKET_HEADER = By.xpath("//div[@data-sentry-component=\"ContactShop\"]//a[contains(@class, \"text-primary mt-4 flex\")][1]//p[contains(@class, \"my-4\")]"),
            TICKET_LAST_MESSAGE_HEADER = By.xpath("//div[@data-sentry-component=\"ContactShop\"]//a[contains(@class, \"text-primary mt-4 flex\")][1]//p[contains(@class, \"body-s_bold\")]"),
            TICKET_LAST_MESSAGE = By.xpath("//div[@data-sentry-component=\"ContactShop\"]//a[contains(@class, \"text-primary mt-4 flex\")][1]/div[1]/div[2]//div[2]"),
            USER_PROFILE_HEADER = By.xpath("//div[@data-sentry-component=\"Profile\"]//div[@class='header-m']"),
            NAME_AND_LAST_NAME_HEADER = By.xpath("//div[@data-sentry-component=\"Profile\"]//div/div/div[contains(text(), 'Imię i nazwisko')]"),
            NAME_AND_LAST_NAME_VALUE = By.xpath("(//div[@data-sentry-component=\"Profile\"]//div/div/div[2])[1]"),
            EMAIL_HEADER = By.xpath("//div[@data-sentry-component=\"Profile\"]//div/div/div[contains(text(), 'email')]"),
            EMAIL_VALUE = By.xpath("(//div[@data-sentry-component=\"Profile\"]//div/div/div[2])[2]"),
            PASSWORD_HEADER = By.xpath("//div[@data-sentry-component=\"Profile\"]//div/div/div[contains(text(), 'Hasło')]"),
            PASSWORD_VALUE = By.xpath("(//div[@data-sentry-component=\"Profile\"]//div/div/div[2])[3]"),
            CHANGE_DATA_BUTTON = By.xpath("//button[@type=\"button\"]//span[contains(text(), 'Zmień dane')]"),
            USER_NAME_INPUT = By.xpath("//input[@autocomplete=\"given-name\"]"),
            USER_SURNAME_INPUT = By.xpath("//input[@autocomplete=\"family-name\"]"),
            NEW_PASSWORD_INPUT = By.xpath("(//input[@type=\"password\"])[2]"),
            SHOW_OLD_PASSWORD_ICON = By.xpath("(//div[@class='absolute top-12.5 right-6 cursor-pointer'])[1]"),
            SHOW_NEW_PASSWORD_ICON = By.xpath("(//div[@class='absolute top-12.5 right-6 cursor-pointer'])[2]"),
            GO_BACK_BUTTON = By.xpath("//button[@type=\"button\"]//span[contains(text(), 'Powrót')]"),
            SAVE_BUTTON = By.xpath("//button[@type=\"button\"]//span[contains(text(), 'Zapisz')]"),
            DATA_SAVED_TEXT = By.xpath("//button[@type=\"button\"]//span[contains(text(), 'Dane zapisane')]"),
            SAVED_SHIPPING_ADDRESS_VALUES = By.xpath("//div[@data-sentry-component=\"DisplayUserData\"]//div[@data-sentry-component=\"AddressList\"][1]//div[@data-sentry-component=\"AddressSummaryFormatting\"]"),
            SAVED_SHIPPING_ADDRESS_EDIT = By.xpath("//div[@data-sentry-component=\"DisplayUserData\"]//div[@data-sentry-component=\"AddressList\"][1]//a[@data-sentry-element=\"Link\"]//button//span[contains(text(), 'Zmień')]"),
            SAVED_BILLING_ADDRESS_VALUES = By.xpath("//div[@data-sentry-component=\"DisplayUserData\"]//div[@data-sentry-component=\"AddressList\"][2]//div[@data-sentry-component=\"AddressSummaryFormatting\"]"),
            SAVED_BILLING_ADDRESS_EDIT = By.xpath("//div[@data-sentry-component=\"DisplayUserData\"]//div[@data-sentry-component=\"AddressList\"][2]//a[@data-sentry-element=\"Link\"]//button//span[contains(text(), 'Zmień')]"),
            ADD_NEW_SHIPPING_ADDRESS_BUTTON = By.xpath("//div[@data-sentry-component=\"DisplayUserData\"]//div[@data-sentry-component=\"AddressList\"][1]//button//span[contains(text(), \"Dodaj adres\")]"),
            ADD_NEW_BILLING_ADDRESS_BUTTON = By.xpath("//div[@data-sentry-component=\"DisplayUserData\"]//div[@data-sentry-component=\"AddressList\"][2]//button//span[contains(text(), \"Dodaj adres\")]"),
            COMPANY_BILLING_ADDRESS_OPTION = By.xpath("//div[contains(text(), \"Firma\")]"),
            INDIVIDUAL_BILLING_ADDRESS_OPTION = By.xpath("//div[contains(text(), \"Osoba fizyczna\")]"),
            THIS_FIELD_IS_REQUIRED_ERROR = By.xpath("//div[contains(@class,\"text-error\")]"),
            DELETE_BILLING_ADDRESS_BUTTONS = By.xpath("//div[@data-sentry-component=\"DisplayUserData\"]//div[@data-sentry-component=\"AddressList\"][2]//button[@type=\"button\"]//span[contains(text(), \"Usuń\")]"),
            DELETE_SHIPPING_ADDRESS_BUTTONS = By.xpath("//div[@data-sentry-component=\"DisplayUserData\"]//div[@data-sentry-component=\"AddressList\"][1]//button[@type=\"button\"]//span[contains(text(), \"Usuń\")]"),
            CONTACT_CHAT_HISTORY_LINKS = By.xpath("//a[contains(@href, 'pl/PL/user/contact-chat')]"),
            CHAT_INPUT_FIELD = By.xpath("//input[@placeholder=\"Napisz wiadomość...\"]"),
            SEND_MESSAGE_BUTTON = By.xpath("//button[@type=\"button\"]//span[contains(text(), 'Wyśli')]"),
            USER_SENT_MESSAGES = By.xpath("//div[@class = \"body-s text-secondary w-11/12 bg-white p-6 lg:w-5/6\"]"),
            USER_NAME_IN_CHAT = By.xpath("//div[@class=\"body-s_bold\"]"),
            DATE_AND_TIME_IN_CHAT = By.xpath("//div[@class=\"body-s_bold\"]//..//..//..//..//div/div[contains(@class, \"body-s text-secondary\")]/div[contains(text(), \"2\")]"),
            MESSAGE_RECEIVED_HEADER = By.xpath("//div[contains(@class, \"border-primary relative\")]//span"),
            ACCOUNT_DATA_CHANGE_SAVE_ERROR_MESSAGE = By.xpath("//p[contains(@class, 'text-error')]"),
            NEWSLETTER_AGREEMENT_CHECKBOX = By.xpath("//input[@type='checkbox']"),
            NEWSLETTER_AGREEMENT_CHECKBOX_LABEL = By.xpath("//div[@class='body-m flex items-start py-4 text-primary']"),
            ATTACH_FILE_BUTTON = By.xpath("//button[@class=\"mx-5 cursor-pointer\"]"),
            ATTACHED_FILE_NAME = By.xpath("//p[contains(@class, \"text-success-primary\")]"),
            ATTACHED_FILES_IN_CHAT = By.xpath("//a[@class=\"underline\"]"),
            INVALID_FILE_TYPE_ERROR = By.xpath("//p[contains(text(), \"Zły format pliku (obsługiwane formaty: jpg, pdf, doc)\")]"),
            EMPTY_MESSAGE_FIELD_ERROR = By.xpath("//div[contains(text(), 'Wypełnij treść wiadomości.')]"),
            REASON_FOR_CONTACT_NOT_SELECTED_ERROR = By.xpath("//div[contains(text(), 'Wybierz temat wiadomości')]"),
            ERROR_MESSAGE_FOR_INVALID_PHONE_NUMBER = By.xpath("//div[contains(text(), 'Nieprawidłowy numer telefonu')]");


    public IWebElement accountPageHeader() {
        return new WebElement($(ACCOUNT_PAGE_HEADER), "Account Page Header");
    }

    public IWebElement logoutLink() {
        return new WebElement($(LOGOUT_LINK), "Logout Link");
    }

    public IWebElement orderHistoryLink() {
        return new WebElement($(ORDER_HISTORY_LINK), "Order History Link");
    }

    public IWebElement myProfileLink() {
        return new WebElement($(MY_PROFILE_LINK), "My Profile Link");
    }

    public IWebElement latestOrderDetailsButton() {
        return new WebElement($(LATEST_ORDER_DETAILS_BUTTON), "Latest Order Details Button");
    }

    public IWebElement orderNumberHeader() {
        return new WebElement($(ORDER_NUMBER_HEADER), "Order Number Header");
    }

    public IWebElement orderPlacementDateHeader() {
        return new WebElement($(ORDER_PLACEMENT_DATE_HEADER), "Order Placement Date Header");
    }

    public IWebElement orderPlacementDateValue() {
        return new WebElement($(ORDER_PLACEMENT_DATE_VALUE), "Order Placement Date Value");
    }

    public IWebElement paymentMethodHeader() {
        return new WebElement($(PAYMENT_METHOD_HEADER), "Payment Method Header");
    }

    public IWebElement paymentMethodValue() {
        return new WebElement($(PAYMENT_METHOD_VALUE), "Payment Method Value");
    }

    public IWebElement orderStatusHeader() {
        return new WebElement($(ORDER_STATUS_HEADER), "Order Status Header");
    }

    public IWebElement orderStatusValue() {
        return new WebElement($(ORDER_STATUS_VALUE), "Order Status Value");
    }

    public IWebElement shippingStatusHeader() {
        return new WebElement($(SHIPPING_STATUS_HEADER), "Shipping Status Header");
    }

    public IWebElement shippingStatusValue() {
        return new WebElement($(SHIPPING_STATUS_VALUE), "Shipping Status Value");
    }

    public IWebElement orderedProductName() {
        return new WebElement($(ORDERED_PRODUCT_NAME), "Ordered Product Name");
    }

    public IWebElement orderedProductImage() {
        return new WebElement($(ORDERED_PRODUCT_IMAGE), "Ordered Product Image");
    }

    public IWebElement orderedProductPrice() {
        return new WebElement($(ORDERED_PRODUCT_PRICE), "Ordered Product Price");
    }

    public IWebElement orderTotalPriceHeader() {
        return new WebElement($(ORDER_TOTAL_PRICE_HEADER), "Order Total Price Header");
    }

    public IWebElement totalCostHeader() {
        return new WebElement($(TOTAL_COST_HEADER), "Total Cost Header");
    }

    public IWebElement totalCostValue() {
        return new WebElement($(TOTAL_COST_VALUE), "Total Cost Value");
    }

    public IWebElement deliveryAddressHeader() {
        return new WebElement($(DELIVERY_ADDRESS_HEADER), "Delivery Address Header");
    }

    public IWebElement billingAddressHeader() {
        return new WebElement($(BILLING_ADDRESS_HEADER), "Billing Address Header");
    }

    public IWebElement deliveryAddressValue() {
        return new WebElement($(DELIVERY_ADDRESS_VALUE), "Delivery Address Value");
    }

    public IWebElement billingAddressValue() {
        return new WebElement($(BILLING_ADDRESS_VALUE), "Billing Address Value");
    }

    public IWebElement orderTotalPriceValue() {
        return new WebElement($(ORDER_TOTAL_PRICE_VALUE), "Order Total Price Value");
    }

    public IWebList orderDetailsButton() {
        return new WebList($$(ORDER_DETAILS_BUTTON), "Order Details Button");
    }

    public IWebElement contactWithTheStoreLink() {
        return new WebElement($(CONTACT_WITH_THE_STORE_LINK), "Contact with the Store Link");
    }

    public IWebElement createATicketLink() {
        return new WebElement($(CREATE_A_TICKET_LINK), "Create a Ticket Link");
    }

    public IWebElement reasonForContactDropdownSelect() {
        return new WebElement($(REASON_FOR_CONTACT_DROPDOWN_SELECT), "Reason for Contact Dropdown Select");
    }

    public IWebList reasonForContactDropdownOptions() {
        return new WebList($$(REASON_FOR_CONTACT_DROPDOWN_OPTIONS), "Reason for Contact Dropdown Options");
    }

    public IWebElement orderToCreateATicketDropdownSelect() {
        return new WebElement($(ORDER_TO_CREATE_A_TICKET_DROPDOWN_SELECT), "Order to Create a Ticket Dropdown Select");
    }

    public IWebList orderToCreateATicketDropdownOptions() {
        return new WebList($$(ORDER_TO_CREATE_A_TICKET_DROPDOWN_OPTIONS), "Order to Create a Ticket Dropdown Options");
    }

    public IInputField messageInputField() {
        return new InputField($(MESSAGE_INPUT_FIELD), "Message Input Field");
    }

    public IWebElement submitRequestButton() {
        return new WebElement($(SUBMIT_REQUEST_BUTTON), "Submit Request Button");

    }

    public IWebElement requestSubmittedText() {
        return new WebElement($(REQUEST_SUBMITTED_TEXT), "Request Submitted Text");
    }

    public IWebElement ticketCreatedDate() {
        return new WebElement($(TICKET_CREATED_DATE), "Ticket Created Date");
    }

    public IWebElement ticketHeader() {
        return new WebElement($(TICKET_HEADER), "Ticket Header");
    }

    public IWebElement ticketLastMessageHeader() {
        return new WebElement($(TICKET_LAST_MESSAGE_HEADER), "Ticket Last Message Header");
    }

    public IWebElement ticketLastMessage() {
        return new WebElement($(TICKET_LAST_MESSAGE), "Ticket Last Message");
    }

    public IWebElement userProfileHeader() {
        return new WebElement($(USER_PROFILE_HEADER), "User Profile Header");
    }

    public IWebElement nameAndLastNameHeader() {
        return new WebElement($(NAME_AND_LAST_NAME_HEADER), "Name and Last Name Header");
    }

    public IWebElement nameAndLastNameValue() {
        return new WebElement($(NAME_AND_LAST_NAME_VALUE), "Name and Last Name Value");
    }

    public IWebElement emailHeader() {
        return new WebElement($(EMAIL_HEADER), "Email Header");
    }

    public IWebElement emailValue() {
        return new WebElement($(EMAIL_VALUE), "Email Value");
    }

    public IWebElement passwordHeader() {
        return new WebElement($(PASSWORD_HEADER), "Password Header");
    }

    public IWebElement passwordValue() {
        return new WebElement($(PASSWORD_VALUE), "Password Value");
    }

    public IWebElement changeDataButton() {
        return new WebElement($(CHANGE_DATA_BUTTON), "Change Data Button");
    }

    public IInputField userNameInput() {
        return new InputField($(USER_NAME_INPUT), "User Name Input");
    }

    public IInputField userSurnameInput() {
        return new InputField($(USER_SURNAME_INPUT), "User Surname Input");
    }

    public IInputField newPasswordInput() {
        return new InputField($(NEW_PASSWORD_INPUT), "New Password Input");
    }

    public IWebElement showOldPasswordIcon() {
        return new WebElement($(SHOW_OLD_PASSWORD_ICON), "Show Old Password Icon");
    }

    public IWebElement showNewPasswordIcon() {
        return new WebElement($(SHOW_NEW_PASSWORD_ICON), "Show New Password Icon");
    }

    public IWebElement goBackButton() {
        return new WebElement($(GO_BACK_BUTTON), "Go Back Button");
    }

    public IWebElement saveButton() {
        return new WebElement($(SAVE_BUTTON), "Save Button");
    }

    public IWebElement dataSavedText() {
        return new WebElement($(DATA_SAVED_TEXT), "Data Saved Text");
    }

    public IWebList savedShippingAddressValues() {
        return new WebList($$(SAVED_SHIPPING_ADDRESS_VALUES), "Saved Shipping Address Values");
    }

    public IWebList savedBillingAddressValues() {
        return new WebList($$(SAVED_BILLING_ADDRESS_VALUES), "Saved Billing Address Values");
    }

    public IWebList editSavedShippingAddressButton() {
        return new WebList($$(SAVED_SHIPPING_ADDRESS_EDIT), "Edit Saved Shipping Address Button");
    }

    public IWebList editSavedBillingAddressButton() {
        return new WebList($$(SAVED_BILLING_ADDRESS_EDIT), "Edit Saved Billing Address Button");
    }

    public IWebElement addNewShippingAddressButton() {
        return new WebElement($(ADD_NEW_SHIPPING_ADDRESS_BUTTON), "Add New Shipping Address Button");
    }

    public IWebElement addNewBillingAddressButton() {
        return new WebElement($(ADD_NEW_BILLING_ADDRESS_BUTTON), "Add New Billing Address Button");
    }

    public IWebElement companyBillingAddressOption() {
        return new WebElement($(COMPANY_BILLING_ADDRESS_OPTION), "Company Billing Address Option");
    }

    public IWebElement individualBillingAddressOption() {
        return new WebElement($(INDIVIDUAL_BILLING_ADDRESS_OPTION), "Individual Billing Address Option");
    }

    public IWebElement thisFieldIsRequiredError() {
        return new WebElement($(THIS_FIELD_IS_REQUIRED_ERROR), "This Field Is Required Error");
    }

    public IWebList deleteBillingAddressButtons() {
        return new WebList($$(DELETE_BILLING_ADDRESS_BUTTONS), "Delete Billing Address Buttons");
    }

    public IWebList deleteShippingAddressButtons() {
        return new WebList($$(DELETE_SHIPPING_ADDRESS_BUTTONS), "Delete Shipping Address Buttons");
    }

    public IWebList contactChatHistoryLinks() {
        return new WebList($$(CONTACT_CHAT_HISTORY_LINKS), "Contact Chat History Links");
    }

    public IInputField chatInputField() {
        return new InputField($(CHAT_INPUT_FIELD), "Chat Input Field");
    }

    public IWebElement sendMessageButton() {
        return new WebElement($(SEND_MESSAGE_BUTTON), "Send Message Button");
    }

    public IWebList userNameInChat() {
        return new WebList($$(USER_NAME_IN_CHAT), "User Name in Chat");
    }

    public IWebList dateAndTimeInChat() {
        return new WebList($$(DATE_AND_TIME_IN_CHAT), "Date and Time in Chat");
    }

    public IWebList userSentMessageInChat() {
        return new WebList($$(USER_SENT_MESSAGES), "User Sent Message in Chat");
    }

    public IWebElement messageReceivedHeader() {
        return new WebElement($(MESSAGE_RECEIVED_HEADER), "Message Received Header");
    }

    public IWebElement accountDataChangeSaveErrorMessage() {
        return new WebElement($(ACCOUNT_DATA_CHANGE_SAVE_ERROR_MESSAGE), "Account Data Change Save Error Message");
    }

    public ICheckBox newsletterAgreementCheckbox() {
        return new CheckBox($(NEWSLETTER_AGREEMENT_CHECKBOX), "Newsletter Agreement Checkbox");
    }

    public IWebElement newsletterAgreementCheckboxLabel() {
        return new WebElement($(NEWSLETTER_AGREEMENT_CHECKBOX_LABEL), "Newsletter Agreement Checkbox Label");
    }

    public IWebElement attachFileButton() {
        return new WebElement($(ATTACH_FILE_BUTTON), "Attach File Button");
    }

    public IWebList attachedFilesInChat() {
        return new WebList($$(ATTACHED_FILES_IN_CHAT), "Attached Files in Chat");
    }

    public IWebElement attachedFileDownloadLink() {
        return new WebElement($(ATTACHED_FILES_IN_CHAT), "Attached File Download Link");
    }

    public IWebElement attachedFileName() {
        return new WebElement($(ATTACHED_FILE_NAME), "Attached File Name");
    }
    public IWebElement invalidFileTypeError() {
        return new WebElement($(INVALID_FILE_TYPE_ERROR), "Invalid File Type Error");
    }
    public IWebElement emptyMessageFieldError() {
        return new WebElement($(EMPTY_MESSAGE_FIELD_ERROR), "Empty Message Field Error");
    }

    public IWebElement reasonForContactNotSelectedError() {
        return new WebElement($(REASON_FOR_CONTACT_NOT_SELECTED_ERROR), "Reason For Contact Not Selected Error");
    }
    public IWebElement errorMessageForInvalidPhoneNumber() {
        return new WebElement($(ERROR_MESSAGE_FOR_INVALID_PHONE_NUMBER), "Error Message For Invalid Phone Number");
    }
}