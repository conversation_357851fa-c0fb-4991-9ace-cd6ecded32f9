package objects_behaviors;

import net.serenitybdd.core.pages.ListOfWebElementFacades;
import net.serenitybdd.core.pages.WebElementFacade;
import objects_behaviors.rules.IWebElement;
import org.jetbrains.annotations.Nullable;
import org.openqa.selenium.*;

import java.time.Duration;
import java.util.concurrent.Callable;

public interface IGuiElement extends WrapsElement {

    WebElementFacade getWrappedElement();

    String getGuiElementName();

    void click();

    void clickWithoutException();

    void doubleClick();

    void clickWithAction();

    void hoverOver();

    void hoverOver(int xOffset, int yOffset);

    void clickToXOffsetAndYOffset(int xOffset, int yOffset);

    Dimension getElementSize();

    String getText();

    String getAttribute(String attributeName);

    String getValue();

    void scrollByCoordinates();

    IWebElement findBy(By by);

    boolean isPresent();

    boolean isVisible();

    boolean isDisplayed();

    boolean hasClass(String cssClassName);

    boolean isAttributeValuePresent(String attribute);

    void waitUntilText(String text, Duration waitAtMost);

    void waitUntilValue(String text, Duration waitAtMost);

    void waitUntilAttributeText(String attributeName, String text, Duration waitAtMost);

    void waitUntilAttributeTextWithoutException(String attributeName, String text, Duration waitAtMost);

    void waitUntilAttributeNotPresent(String attributeName, Duration waitAtMost);

    void waitUntilAttributeNotPresentWithoutException(String attributeName, Duration waitAtMost);

    void waitUntilAttributePresent(String attributeName, Duration waitAtMost);

    void waitUntilAttributePresent(String attributeName, Duration waitAtMost, int interval);

    void waitUntilAttributeNotContains(String attributeName, String text, Duration waitAtMost);

    void waitUntilClassNotContains(String className, Duration waitAtMost);


    void waitUntilAttributeContains(String attributeName, String text, Duration waitAtMost);

    void waitUntilVisible();

    void waitUntilVisible(Duration waitAtMost);

    void waitUntilVisibleWithoutException(Duration waitAtMost);

    boolean isVisibleAfterWaiting(Duration waitAtMost);

    void waitUntilNotVisible();

    void waitUntilNotVisible(Duration waitAtMost);

    void waitUntilNotVisibleWithoutException(Duration waitAtMost);

    void waitUntilPresent();

    void waitUntilPresent(Duration waitAtMost);

    void waitUntilPresentWithoutException(Duration waitAtMost);

    void waitUntilNotPresent(Duration waitAtMost);

    void waitUntilNotPresentWithoutException(Duration waitAtMost);

    void waitUntilStaleness(Duration waitAtMost);

    void waitUntilClickable(Duration waitAtMost);

    void clickRight();

    void scrollToTop();

    void scrollToEnd();

    void scrollElementToTop();

    boolean isEnabled();

    void scrollElementToCenter();


    void waitByConditionEvaluator(Callable<Boolean> callable, Duration timeToWait);



}
