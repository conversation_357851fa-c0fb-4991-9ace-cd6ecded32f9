package objects_behaviors.rules;

import objects_behaviors.IGuiElement;

public interface IInputField extends IGuiElement {

    void sendKeys(String text);

    void type(String text);

    void typeAndEnter(String text);

    void typeAndTab(String text);

    void clearText();

    void clearAndType(String text);

    void selectAllText();

    boolean isActive();

    boolean isNumberInput();

    String placeholder();

    /**
     * Fill value in text input
     *
     * @param value the value to be filled
     * @return true if it changed the value input
     */
    boolean fillValue(String value);

    /**
     * Clear value in text input
     *
     * @return true if it changed the value input
     */
    boolean clearInput();
}
