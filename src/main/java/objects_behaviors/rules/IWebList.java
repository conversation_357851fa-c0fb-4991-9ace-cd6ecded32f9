package objects_behaviors.rules;

import net.serenitybdd.core.pages.WebElementFacade;
import org.openqa.selenium.By;

import javax.annotation.Nullable;
import java.time.Duration;
import java.util.List;
import java.util.function.Supplier;

public interface IWebList {

    IWebElement getElementByIndex(int index);

    int getSize();

    List<String> getTexts();

    //get all elements
    List<WebElementFacade> getAllElements();

    List<String> getTextContents();

    List<String> getValues();

    String getTextByIndex(int index);

    int getIndexByText(String text);

    IWebElement getElementByText(String text);

    int getRandomIndex();

    IWebElement getRandomElement();

    String getRandomText();

    String getAttributeByText(String text, String attributeName);

    String getAttributeByIndex(int index, String attributeName);

    List<String> getAttributes(String attributeName);

    void clickByContainedText(String text);

    void clickByText(String text);

    void clickByTextOrThrow(String text);

    void clickByPartialText(String text);

    int clickByTextAndGetIndex(String text);

    void clickByIndex(int index);

    String clickByIndexAndGetText(int index);

    void clickFirst();

    String clickFirstAndGetText();

    void clickRandomly();

    int clickRandomlyAndGetIndex();

    String clickRandomlyAndGetText();

    IWebElement getWebElementByText(String text, By by);

    IWebElement getWebElementByText(String text);

    IWebElement getWebElementByTextContent(String text);

    IWebElement getWebElementByAttribute(String attribute, String text);

    IWebElement getWebElementByAttribute(String attribute, String text, By by);

    boolean isSelectedByIndex(int index);

    void waitUntilVisible();

    void waitUntilVisible(Duration atMost);

    void waitUntilVisibleWithoutException(Duration atMost);

    void waitUntilAttributeTextWithoutException(Duration atMost);

    void waitUntilNotVisible();

    void waitUntilNotVisible(Duration atMost);

    void waitUntilOpen(boolean isOpen);

    void waitUntilOpen(boolean isOpen, Duration duration);

    void waitUntilAllElementsAreVisible(Duration duration);

    boolean isExpand(String optionName);

    void scrollDownToItem(final String item);

    default IWebElement getLastElement() {
        return getElementByIndex(getSize() - 1);
    }

    @Nullable
    IWebList getIWebListScrollableBaseOnElementText(Supplier<IWebList> exportWebList, boolean isDown);


    void scrollToTop(Supplier<IWebList> exportWebList);

    void waitUntilValueVisible(final String value, final Duration waitAtMost);


    void waitUntilValueVisibleWithoutException(String value, Duration waitAtMost);

    boolean isEmpty();

}
