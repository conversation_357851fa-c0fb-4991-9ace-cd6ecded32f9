package objects_behaviors.implementation;

import net.serenitybdd.core.Serenity;
import net.serenitybdd.core.pages.WebElementFacade;
import objects_behaviors.AbstractGuiElement;
import objects_behaviors.rules.IRadioButton;
import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;

public class RadioButton extends AbstractGuiElement implements IRadioButton {

    public RadioButton(WebElementFacade element) {
        super(element);
    }

    public RadioButton(WebElementFacade element, String name) {
        super(element, name);
    }

    @Override
    public boolean isChecked() {
        JavascriptExecutor jsExecutor = (JavascriptExecutor) Serenity.getDriver();
        String id = getWrappedElement().getAttribute("id");
        if (id == null)
            return false;
        String executeScript = String.format("return document.querySelector(\"input[id='%s']\").checked;", id);
        return Boolean.parseBoolean(jsExecutor.executeScript(executeScript).toString());
    }

    @Override
    public boolean isSelected() {
        final String checkedAttr = getWrappedElement().getAttribute("checked");
        if (null != checkedAttr) {
            return Boolean.parseBoolean(checkedAttr);
        }

        return false;
    }

    @Override
    public boolean isEnabled() {
        final String disabledAttr = getWrappedElement().getAttribute("disabled");
        if (null != disabledAttr) {
            return !Boolean.parseBoolean(disabledAttr);
        }

        return true;
    }

    @Override
    public void click() {
        getWrappedElement().find(By.xpath("./..")).click();
    }

    @Override
    public void clickLabel() {
        getWrappedElement().find(By.xpath("./..//label")).getWrappedElement().click();
    }

}
