package objects_behaviors.implementation;

import net.serenitybdd.core.pages.WebElementFacade;
import objects_behaviors.AbstractGuiElement;
import objects_behaviors.rules.ICheckBox;
import org.openqa.selenium.By;

public class CheckBox extends AbstractGuiElement implements ICheckBox {

    public CheckBox(WebElementFacade element) {
        super(element);
    }

    public CheckBox(WebElementFacade element, String name) {
        super(element, name);
    }

    @Override
    public boolean select() {
        if (isSelected() || isChecked()) {
            return false;
        } else {
            getWrappedElement().click();
            return true;
        }
    }

    @Override
    public boolean deselect() {
        if (isSelected() || isChecked()) {
            getWrappedElement().click();
            return true;
        }
        return false;
    }

    @Override
    public boolean isSelected() {
        return getWrappedElement().isSelected();
    }

    @Override
    public boolean isChecked() {
        try {
            // Direct translation of the JavaScript code
            By checkboxSelector = By.cssSelector(".flex.flex-row.py-4 input[type=\"checkbox\"]");
            WebElementFacade checkbox = getWrappedElement().find(checkboxSelector);

            if (checkbox != null && checkbox.isVisible()) {
                return checkbox.isSelected();
            }

        } catch (Exception e) {
            // Element isn't found or other error
            System.out.println("Checkbox not found or error occurred: " + e.getMessage());
        }

        return false;
    }

}
