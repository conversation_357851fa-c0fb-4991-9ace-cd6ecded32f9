package objects_behaviors.implementation;

import net.serenitybdd.core.pages.WebElementFacade;
import objects_behaviors.AbstractGuiElement;
import objects_behaviors.rules.IWebElement;
import org.openqa.selenium.By;

public class WebElement extends AbstractGuiElement implements IWebElement {
    private final WebElementFacade element;

    public WebElement(WebElementFacade element) {
        super(element);
        this.element = element;
    }

    public WebElement(WebElementFacade element, String name) {
        super(element, name);
        this.element = element;
    }

    @Override
    public boolean isBlocked() {
        return false;
    }

    @Override
    public boolean isOpen() {
        return false;
    }

    @Override
    public boolean isClosed() {
        return false;
    }
}
