package objects_behaviors.implementation;

import net.serenitybdd.core.pages.PageObject;
import net.serenitybdd.core.pages.WebElementFacade;
import net.serenitybdd.core.pages.WebElementState;
import objects_behaviors.rules.IWebElement;
import objects_behaviors.rules.IWebList;
import org.awaitility.Awaitility;
import org.awaitility.core.ConditionTimeoutException;
import org.jetbrains.annotations.Nullable;
import org.openqa.selenium.By;

import java.time.Duration;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

public class WebList extends PageObject implements IWebList {

    private final List<WebElementFacade> elements;
    private String name;

    public WebList(List<WebElementFacade> elements) {
        this.elements = elements;
    }
    public WebList(List<WebElementFacade> elements, String name) {
        this(elements);
        this.name = name;
    }


    private List<WebElementFacade> getWrappedElements() {
        return this.elements;
    }

    private WebElementFacade getWrappedElementByIndex(int index) {
        return getWrappedElements().get(index);
    }

    @Override
    public IWebElement getElementByIndex(int index) {
        try {
            return new WebElement(getWrappedElementByIndex(index));
        } catch (IndexOutOfBoundsException e) {
            return null;
        }
    }

    @Override
    public IWebElement getElementByText(String text) {
        return getWrappedElements().stream()
                .filter(e -> e.getText().equalsIgnoreCase(text))
                .findFirst()
                .map(WebElement::new)
                .orElse(null);
    }

    @Override
    public int getSize() {
        return getWrappedElements().size();
    }

    @Override
    public List<String> getTexts() {
        return getWrappedElements().stream()
                .map(WebElementFacade::getText)
                .collect(Collectors.toList());
    }

    //get all elements
    @Override
    public List<WebElementFacade> getAllElements() {
        return getWrappedElements();
    }

    @Override
    public List<String> getTextContents() {
        return getWrappedElements().stream()
                .map(WebElementFacade::getTextContent)
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getValues() {
        return getWrappedElements().stream()
                .map(WebElementFacade::getValue)
                .collect(Collectors.toList());
    }

    @Override
    public String getTextByIndex(int index) {
        return getWrappedElementByIndex(index).getText();
    }

    @Override
    public int getIndexByText(String text) {
        return IntStream.range(0, getSize())
                .filter(x -> getTextByIndex(x).equalsIgnoreCase(text))
                .findFirst()
                .orElse(-1);
    }

    @Override
    public int getRandomIndex() {
        int listSize = getSize();
        // make sure the random function works on the positive values
        if (listSize == 0) {
            waitUntilAllElementsAreVisible(Duration.ofMinutes(1));
        }
        return new Random().nextInt(listSize);
    }

    @Override
    public IWebElement getRandomElement() {
        int randomIndex = getRandomIndex();
        return new WebElement(getWrappedElements().get(randomIndex));
    }

    @Override
    public String getRandomText() {
        int randomIndex = getRandomIndex();
        return getTextByIndex(randomIndex);
    }

    @Override
    public String getAttributeByText(String text, String attributeName) {
        return getWrappedElements().stream()
                .filter(e -> e.getText().equals(text))
                .findFirst()
                .get()
                .getAttribute(attributeName);
    }

    @Override
    public String getAttributeByIndex(int index, String attributeName) {
        return getWrappedElementByIndex(index).getAttribute(attributeName);
    }

    @Override
    public List<String> getAttributes(String attributeName) {
        return getWrappedElements().stream()
                .map(e -> e.getAttribute(attributeName))
                .collect(Collectors.toList());
    }

    @Override
    public void clickByText(String text) {
        getWrappedElements().stream()
                .filter(e -> e.getText().equalsIgnoreCase(text))
                .findFirst()
                .get()
                .click();
    }


    @Override
    public void clickByContainedText(String text) {
        getWrappedElements().stream()
                .filter(e -> e.getText().contains(text))
                .findFirst()
                .get()
                .click();
    }

    @Override
    public void clickByTextOrThrow(String text) {
        getWrappedElements().stream()
                .filter(e -> e.getText().equalsIgnoreCase(text))
                .findFirst()
                .orElseThrow()
                .click();
    }

    @Override
    public void clickByPartialText(String text) {
        getWrappedElements().stream()
                .filter(e -> e.getText().contains(text))
                .findFirst()
                .get()
                .click();
    }

    @Override
    public int clickByTextAndGetIndex(String text) {
        int index = getIndexByText(text);
        clickByIndex(index);
        return index;
    }

    @Override
    public void clickByIndex(int index) {
        getWrappedElementByIndex(index).click();
    }

    @Override
    public String clickByIndexAndGetText(int index) {
        clickByIndex(index);
        return getTextByIndex(index);
    }

    @Override
    public void clickFirst() {
        clickByIndex(0);
    }

    @Override
    public String clickFirstAndGetText() {
        String text = getTextByIndex(0);
        clickByIndex(0);
        return text;
    }

    @Override
    public void clickRandomly() {
        int randomIndex = getRandomIndex();
        clickByIndex(randomIndex);
    }

    @Override
    public int clickRandomlyAndGetIndex() {
        int randomIndex = getRandomIndex();
        clickByIndex(randomIndex);
        return randomIndex;
    }

    @Override
    public String clickRandomlyAndGetText() {
        int randomIndex = getRandomIndex();
        var value = getTextByIndex(randomIndex);
        getWrappedElementByIndex(randomIndex).click();
        return value;
    }

    @Override
    public boolean isSelectedByIndex(int index) {
        return getWrappedElementByIndex(index).isSelected();
    }


    @Override
    public boolean isExpand(String text) {
        var item = getWrappedElements().stream()
                .filter(e -> e.getText().equalsIgnoreCase(text))
                .findFirst()
                .get()
                .find(By.xpath(".//span[contains(@class, 'ag-group-contracted')]"));
        return item.getAttribute("class").contains("ag-hidden");
    }

    @Override
    public IWebElement getWebElementByText(String text, By by) {
        return new WebElement(getWrappedElements()
                .stream()
                .filter(e -> e.findBy(by).getText().equals(text))
                .findFirst().get());
    }

    @Override
    public IWebElement getWebElementByText(String text) {
        try {
            return new WebElement(getWrappedElements()
                    .stream()
                    .filter(e -> e.getText().equalsIgnoreCase(text))
                    .findFirst()
                    .get());
        } catch (Exception ignored) {
            return null;
        }
    }

    @Override
    public IWebElement getWebElementByTextContent(String text) {
        try {
            return new WebElement(getWrappedElements()
                    .stream()
                    .filter(e -> e.getTextContent().equalsIgnoreCase(text))
                    .findFirst()
                    .get());
        } catch (Exception ignored) {
            return null;
        }
    }

    @Override
    public IWebElement getWebElementByAttribute(String attribute, String text) {
        return new WebElement(getWrappedElements()
                .stream()
                .filter(e -> e.getAttribute(attribute).equals(text))
                .findFirst().get());
    }

    @Override
    public IWebElement getWebElementByAttribute(String attribute, String text, By by) {
        return new WebElement(getWrappedElements()
                .stream()
                .filter(e -> e.findBy(by).getAttribute(attribute).equals(text))
                .findFirst().get());
    }

    @Override
    public void waitUntilVisible() {
        Awaitility.await()
                .atMost(Duration.ofSeconds(5))
                .with()
                .ignoreExceptions()
                .pollInterval(Duration.ofMillis(250))
                .until(() -> !this.elements.isEmpty());
    }

    @Override
    public void waitUntilVisible(Duration atMost) {
        Awaitility.await()
                .atMost(atMost)
                .with()
                .ignoreExceptions()
                .pollInterval(Duration.ofMillis(250))
                .until(() -> !this.elements.isEmpty());
    }

    @Override
    public void waitUntilVisibleWithoutException(Duration atMost) {
        try {
            Awaitility.await()
                    .atMost(atMost)
                    .with()
                    .ignoreExceptions()
                    .pollInterval(Duration.ofMillis(500))
                    .until(() -> !this.elements.isEmpty());
        } catch (Exception ignored) {
        }
    }

    @Override
    public void waitUntilAttributeTextWithoutException(Duration waitAtMost) {
        try {
            Awaitility.await()
                    .atMost(waitAtMost)
                    .with()
                    .ignoreExceptions()
                    .pollInterval(Duration.ofMillis(500))
                    .until(() -> !this.elements.isEmpty());
        } catch (Exception ignored) {
        }
    }

    @Override
    public void waitUntilNotVisible() {
        Awaitility.await()
                .atMost(Duration.ofSeconds(5))
                .with()
                .ignoreExceptions()
                .pollInterval(Duration.ofMillis(250))
                .until(this.elements::isEmpty);
    }

    @Override
    public void waitUntilNotVisible(Duration atMost) {
        Awaitility.await()
                .atMost(atMost)
                .with()
                .ignoreExceptions()
                .pollInterval(Duration.ofMillis(250))
                .until(this.elements::isEmpty);
    }

    @Override
    public void waitUntilOpen(boolean isOpen) {
        waitUntilOpen(isOpen, Duration.ofMinutes(2));
    }

    @Override
    public void waitUntilOpen(boolean isOpen, Duration duration) {
        if (!(this.elements.isEmpty())) {
            Awaitility.await()
                    .atMost(duration)
                    .with()
                    .ignoreExceptions()
                    .pollInterval(Duration.ofMillis(500))
                    .until(() -> this.elements.isEmpty() != isOpen);
        }
    }

    @Override
    public void waitUntilAllElementsAreVisible(Duration duration) {
        Awaitility.await()
                .atMost(duration)
                .with()
                .ignoreExceptions()
                .pollInterval(Duration.ofMillis(500))
                .until(() -> !this.elements.isEmpty() && elements.stream().allMatch(WebElementState::isVisible));
    }

    @Override
    public void scrollDownToItem(final String item) {
        Set<String> allAvailableOptions = new HashSet<>();
        List<String> options;
        var numberOfComplexTemp = 0;
        IWebElement theLastComplexInList;
        List<WebElementFacade> elements = this.elements;

        // Scroll to top
        this.getElementByIndex(0).scrollElementToCenter();

        while (!elements.isEmpty()) {

            // Check item present but not visible
            IWebElement elementByText = this.getElementByText(item);
            if (elementByText.isPresent()) {
                elementByText.scrollElementToCenter();
                break;
            }

            options = this.getTexts();
            // Add temporary list into final list
            allAvailableOptions.addAll(options);

            // Get the last complex in list view
            theLastComplexInList = this.getElementByIndex(options.size() - 1);
            // Scroll down and wait for all element loaded
            theLastComplexInList.scrollElementToTop();
            // Exit loop if scroll to end of page
            if (allAvailableOptions.size() == numberOfComplexTemp) break;

            // Count number of items in the list to prepare for condition for the next loop
            numberOfComplexTemp = allAvailableOptions.size();
        }
    }

    @Nullable
    @Override
    public IWebList getIWebListScrollableBaseOnElementText(Supplier<IWebList> exportWebList, boolean isDown) {
        if (isDown) {
            var endElement = getLastElement();
            var endElementText = endElement.getText();

            endElement.scrollElementToTop();

            // get update element value of WebList after scroll
            var updatedWebList = exportWebList.get();
            var updatedElement = updatedWebList.getLastElement();
            var updatedElementText = updatedElement.getText();

            if (endElementText.equals(updatedElementText)) {
                return null;
            } else {
                return updatedWebList;
            }
        } else {
            var topElement = getElementByIndex(0);
            var topElementText = topElement.getText();
            topElement.scrollElementToTop();
            var updatedWebList = exportWebList.get();
            var updatedElement = updatedWebList.getElementByIndex(0);
            var updatedElementText = updatedElement.getText();

            if (topElementText.equals(updatedElementText)) {
                return null;
            } else {
                return updatedWebList;
            }
        }
    }

    @Override
    public void scrollToTop(Supplier<IWebList> exportWebList) {
        var webList = (IWebList) this;
        var isTop = false;
        while (!isTop) {
            webList.getElementByIndex(0).scrollElementToTop();
            webList = webList.getIWebListScrollableBaseOnElementText(exportWebList, false);
            isTop = webList == null;
        }
    }

    @Override
    public void waitUntilValueVisible(String value, Duration waitAtMost) {
        Awaitility.await()
                .atMost(waitAtMost)
                .with()
                .ignoreExceptions()
                .pollInterval(Duration.ofMillis(250))
                .until(() -> getTexts().contains(value));
    }

    @Override
    public void waitUntilValueVisibleWithoutException(final String value, final Duration waitAtMost) {
        try {
            waitUntilValueVisible(value, waitAtMost);
        } catch (ConditionTimeoutException ignored) {
        }
    }

    @Override
    public boolean isEmpty() {
        return getSize() == 0;
    }

}
