package objects_behaviors.implementation;

import net.serenitybdd.core.pages.WebElementFacade;
import objects_behaviors.AbstractGuiElement;
import objects_behaviors.rules.IField;

import java.time.Duration;

public class Field extends AbstractGuiElement implements IField {
    public Field(WebElementFacade element) {
        super(element);
    }

    public Field(WebElementFacade element, String name) {
        super(element, name);
    }

}
