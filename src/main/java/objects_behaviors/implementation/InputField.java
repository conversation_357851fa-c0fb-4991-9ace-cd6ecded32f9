package objects_behaviors.implementation;

import lombok.extern.slf4j.Slf4j;
import net.serenitybdd.core.pages.WebElementFacade;
import objects_behaviors.AbstractGuiElement;
import objects_behaviors.rules.IInputField;
import org.apache.commons.lang3.StringUtils;
import org.openqa.selenium.Keys;

/**
 * All type() methods include clear() method inside.
 */
@Slf4j
public class InputField extends AbstractGuiElement implements IInputField {

    public InputField(WebElementFacade element) {
        super(element);
    }

    public InputField(WebElementFacade element, String name) {
        super(element, name);
    }

    @Override
    public void sendKeys(String text) {
        getWrappedElement().sendKeys(text);
    }

    /**
     * Clear and type
     */
    @Override
    public void type(String text) {
        getWrappedElement().type(text);
    }

    @Override
    public void typeAndEnter(String text) {
        getWrappedElement().typeAndEnter(text);
    }

    @Override
    public void typeAndTab(String text) {
        getWrappedElement().typeAndTab(text);
    }

    @Override
    public void clearText() {
        doubleClick();
        getWrappedElement().sendKeys(Keys.chord(Keys.CONTROL, "a"));
        getWrappedElement().sendKeys(Keys.DELETE);
    }

    @Override
    public void clearAndType(String text) {
        clearText();
        type(text);
    }

    @Override
    public void selectAllText() {
        doubleClick();
        getWrappedElement().sendKeys(Keys.chord(Keys.CONTROL, "a"));
    }

    @Override
    public boolean isActive() {
        return getWrappedElement().getAttribute("disabled") == null;
    }

    @Override
    public boolean isNumberInput() {
        return !getAttribute("type").equals("text");
    }

    public String placeholder() {
        return getAttribute("placeholder");
    }

    /**
     * Fill value in text input
     *
     * @param value the value to be filled
     * @return true if it changed the value input
     */
    @Override
    public boolean fillValue(String value) {
        if (!getValue().equals(value)) {
            clearText();
            sendKeys(value);
            return true;
        }
        return false;
    }


    /**
     * Clear value in text input
     *
     * @return true if it changed the value input
     */
    @Override
    public boolean clearInput() {
        if (StringUtils.isNotEmpty(getValue()) || StringUtils.isNotEmpty(getText())) {
            click();
            sendKeys(Keys.chord(Keys.CONTROL, "a"));
            sendKeys(String.valueOf(Keys.DELETE));
            return true;
        }
        return false;
    }
}
