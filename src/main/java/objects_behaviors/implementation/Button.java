package objects_behaviors.implementation;

import net.serenitybdd.core.pages.WebElementFacade;
import objects_behaviors.AbstractGuiElement;
import objects_behaviors.rules.IButton;

public class <PERSON><PERSON> extends AbstractGuiElement implements IButton {

    public Button(WebElementFacade element) {
        super(element);
    }

    public Button(WebElementFacade element, String name) {
        super(element, name);
    }

    @Override
    public boolean isActive() {
        return getAttribute("disabled") == null;
    }

    @Override
    public boolean isSelected() {
        return getAttribute("class").contains("active");
    }
}
