package objects_behaviors;

import lombok.extern.slf4j.Slf4j;
import lombok.val;
import net.serenitybdd.core.pages.PageObject;
import net.serenitybdd.core.pages.WebElementFacade;
import objects_behaviors.implementation.WebElement;
import objects_behaviors.rules.IWebElement;
import org.apache.commons.lang3.StringUtils;
import org.awaitility.Awaitility;
import org.awaitility.core.ConditionTimeoutException;
import org.jetbrains.annotations.Nullable;
import org.openqa.selenium.By;
import org.openqa.selenium.Dimension;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.interactions.Coordinates;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.FluentWait;
import org.openqa.selenium.support.ui.Wait;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.stream.Collectors;

@Slf4j
public abstract class AbstractGuiElement extends PageObject implements IGuiElement {

    protected String name;
    protected WebElementFacade element;

    public AbstractGuiElement(WebElementFacade element) {
        this.element = element;
    }

    public AbstractGuiElement(WebElementFacade element, String name) {
        this(element);
        this.name = name;
    }

    @Override
    public WebElementFacade getWrappedElement() {
        return this.element;
    }

    @Override
    public String getGuiElementName() {
        return this.name;
    }

    @Override
    public void click() {
        getWrappedElement().waitUntilClickable().click();
    }

    @Override
    public void clickWithoutException() {
        try {
            getWrappedElement().click();
        } catch (Exception ignore) {

        }
    }

    @Override
    public void doubleClick() {
        withAction().doubleClick(getWrappedElement()).build().perform();
    }

    @Override
    public void clickWithAction() {
        withAction().click(getWrappedElement()).build().perform();
    }

    @Override
    public void hoverOver() {
        withAction().moveToElement(getWrappedElement()).build().perform();
    }

    @Override
    public void hoverOver(int xOffset, int yOffset) {
        withAction().moveToElement(getWrappedElement(), xOffset, yOffset).build().perform();
    }

    @Override
    public void clickToXOffsetAndYOffset(int xOffset, int yOffset) {
        // xOffset from the element's in-view center point. A negative value means an offset left of the point
        // yOffset from the element's in-view center point. A negative value means an offset above the point
        withAction().moveToElement(getWrappedElement(), xOffset, yOffset).click().build().perform();
    }

    @Override
    public Dimension getElementSize() {
        return getWrappedElement().getSize();
    }

    @Override
    public String getText() {
        return getWrappedElement().getText();
    }

    @Override
    public String getAttribute(String attributeName) {
        try {
            return getWrappedElement().waitUntilVisible().getAttribute(attributeName);
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public String getValue() {
        return getWrappedElement().getAttribute("value");
    }

    @Override
    public void scrollByCoordinates() {
        Coordinates coordinates = getWrappedElement().getCoordinates();
        coordinates.inViewPort();
    }

    @Override
    public IWebElement findBy(By by) {
        return new WebElement(getWrappedElement().find(by));
    }


    @Override
    public boolean isPresent() {
        try {
            return getWrappedElement().isPresent();
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public boolean isVisible() {
        try {
            return getWrappedElement().isVisible();
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public boolean isDisplayed() {
        try {
            return getWrappedElement().isDisplayed();
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public boolean hasClass(String cssClassName) {
        return getWrappedElement().hasClass(cssClassName);
    }

    @Override
    public boolean isAttributeValuePresent(String attributeName) {
        try {
            if (null != getAttribute(attributeName)) {
                return true;
            }
        } catch (Exception ignored) {
        }
        return false;
    }

    @Override
    public void waitUntilText(String text, Duration waitAtMost) {
        Awaitility.await()
                .atMost(waitAtMost)
                .with()
                .ignoreExceptions()
                .pollInterval(Duration.ofMillis(500))
                .until(() -> getText().equals(text));
    }

    @Override
    public void waitUntilValue(String text, Duration waitAtMost) {
        Awaitility.await()
                .atMost(waitAtMost)
                .with()
                .ignoreExceptions()
                .pollInterval(Duration.ofMillis(500))
                .until(() -> getValue().equals(text));
    }


    @Override
    public void waitUntilAttributeText(String attributeName, String text, Duration waitAtMost) {
        Awaitility.await()
                .atMost(waitAtMost)
                .with()
                .ignoreExceptions()
                .pollInterval(Duration.ofMillis(500))
                .until(() -> getAttribute(attributeName).equals(text));
    }

    @Override
    public void waitUntilAttributeTextWithoutException(String attributeName, String text, Duration waitAtMost) {
        try {
            Awaitility.await()
                    .atMost(waitAtMost)
                    .with()
                    .ignoreExceptions()
                    .pollInterval(Duration.ofMillis(500))
                    .until(() -> getAttribute(attributeName).equals(text));
        } catch (Exception ignored) {
        }
    }

    @Override
    public void waitUntilAttributeNotPresent(String attributeName, Duration waitAtMost) {
        Awaitility.await()
                .atMost(waitAtMost)
                .with()
                .ignoreExceptions()
                .pollInterval(Duration.ofMillis(500))
                .until(() -> null == getAttribute(attributeName));
    }

    @Override
    public void waitUntilAttributeNotPresentWithoutException(String attributeName, Duration waitAtMost) {
        try {
            waitUntilAttributeNotPresent(attributeName, waitAtMost);
        } catch (Exception ignored) {
        }
    }

    @Override
    public void waitUntilAttributePresent(String attributeName, Duration waitAtMost) {
        Awaitility.await()
                .atMost(waitAtMost)
                .with()
                .ignoreExceptions()
                .pollInterval(Duration.ofMillis(500))
                .until(() -> getAttribute(attributeName) != null);
    }

    @Override
    public void waitUntilAttributePresent(String attributeName, Duration waitAtMost, int interval) {
        Awaitility.await()
                .atMost(waitAtMost)
                .with()
                .ignoreExceptions()
                .pollInterval(Duration.ofMillis(interval))
                .until(() -> getAttribute(attributeName) != null);
    }

    @Override
    public void waitUntilAttributeNotContains(String attributeName, String text, Duration waitAtMost) {
        Awaitility.await()
                .atMost(waitAtMost)
                .with()
                .ignoreExceptions()
                .pollInterval(Duration.ofMillis(500))
                .until(() -> !getAttribute(attributeName).contains(text));
    }

    @Override
    public void waitUntilClassNotContains(String className, Duration waitAtMost) {
        Awaitility.await()
                .atMost(waitAtMost)
                .with()
                .ignoreExceptions()
                .pollInterval(Duration.ofMillis(500))
                .until(() -> !getClass().getName().contains(className));
    }

    @Override
    public void waitUntilAttributeContains(String attributeName, String text, Duration waitAtMost) {
        Awaitility.await()
                .atMost(waitAtMost)
                .with()
                .ignoreExceptions()
                .pollInterval(Duration.ofMillis(100))
                .until(() -> getAttribute(attributeName).contains(text));
    }

    @Override
    public void waitUntilVisible() {
        getWrappedElement().waitUntilVisible();
    }

    @Override
    public void waitUntilVisible(Duration waitAtMost) {
        Awaitility.await()
                .atMost(waitAtMost)
                .with()
                .ignoreExceptions()
                .pollInterval(Duration.ofMillis(500))
                .until(this::isVisible);
    }

    @Override
    public void waitUntilVisibleWithoutException(Duration waitAtMost) {
        try {
            Awaitility.await()
                    .atMost(waitAtMost)
                    .with()
                    .ignoreExceptions()
                    .pollInterval(Duration.ofMillis(500))
                    .until(this::isVisible);
        } catch (ConditionTimeoutException ignored) {
        }
    }

    @Override
    public boolean isVisibleAfterWaiting(Duration waitAtMost) {
        waitUntilVisibleWithoutException(waitAtMost);
        return isVisible();
    }

    @Override
    public void waitByConditionEvaluator(Callable<Boolean> callable, Duration waitAtMost) {
        Awaitility.await()
                .atMost(waitAtMost)
                .with()
                .ignoreExceptions()
                .pollInterval(Duration.ofMillis(500))
                .until(callable);
    }

    @Override
    public void waitUntilNotVisible() {
        getWrappedElement().waitUntilNotVisible();
    }

    @Override
    public void waitUntilNotVisible(Duration waitAtMost) {
        Awaitility.await()
                .atMost(waitAtMost)
                .with()
                .ignoreExceptions()
                .pollInterval(Duration.ofMillis(500))
                .until(() -> !isVisible());
    }

    @Override
    public void waitUntilNotVisibleWithoutException(Duration waitAtMost) {
        try {
            Awaitility.await()
                    .atMost(waitAtMost)
                    .with()
                    .ignoreExceptions()
                    .pollInterval(Duration.ofMillis(500))
                    .until(() -> !isVisible());
        } catch (Exception ignored) {
        }
    }

    @Override
    public void waitUntilPresent() {
        getWrappedElement().waitUntilPresent();
    }

    @Override
    public void waitUntilPresent(Duration waitAtMost) {
        Awaitility.await()
                .atMost(waitAtMost)
                .with()
                .ignoreExceptions()
                .pollInterval(Duration.ofMillis(500))
                .until(this::isPresent);
    }

    @Override
    public void waitUntilPresentWithoutException(Duration waitAtMost) {
        try {
            Awaitility.await()
                    .atMost(waitAtMost)
                    .with()
                    .ignoreExceptions()
                    .pollInterval(Duration.ofMillis(500))
                    .until(this::isPresent);
        } catch (ConditionTimeoutException ignored) {
        }
    }

    @Override
    public void waitUntilNotPresent(Duration waitAtMost) {
        if (isPresent()) {
            Awaitility.await()
                    .atMost(waitAtMost)
                    .with()
                    .ignoreExceptions()
                    .pollInterval(Duration.ofMillis(500))
                    .until(() -> !isPresent());
        }
    }

    @Override
    public void waitUntilNotPresentWithoutException(Duration waitAtMost) {
        try {
            if (isPresent()) {
                Awaitility.await()
                        .atMost(waitAtMost)
                        .with()
                        .ignoreExceptions()
                        .pollInterval(Duration.ofMillis(500))
                        .until(() -> !isPresent());
            }
        } catch (Exception ignored) {
        }
    }


    @Override
    public void waitUntilStaleness(Duration waitAtMost) {
        Wait<WebDriver> wait = new FluentWait<WebDriver>(getDriver())
                .withTimeout(waitAtMost)
                .pollingEvery(Duration.ofMillis(250))
                .ignoreAll(List.of(Exception.class));
        wait.until(ExpectedConditions.stalenessOf(getWrappedElement()));
    }

    @Override
    public void waitUntilClickable(Duration waitAtMost) {
        Wait<WebDriver> wait = new FluentWait<WebDriver>(getDriver())
                .withTimeout(waitAtMost)
                .pollingEvery(Duration.ofMillis(250))
                .ignoreAll(List.of(Exception.class));
        wait.until(ExpectedConditions.elementToBeClickable(getWrappedElement()));
    }

    @Override
    public void clickRight() {
        withAction().contextClick(getWrappedElement()).build().perform();
    }

    @Override
    public void scrollToTop() {
        var driver = getDriver();
        JavascriptExecutor jsExecutor = (JavascriptExecutor) driver;
        jsExecutor.executeScript("arguments[0].scrollTop = 0", getWrappedElement());
        waitABit(1000);
    }
    @Override
    public void scrollElementToCenter() {
        var driver = getDriver();
        JavascriptExecutor jsExecutor = (JavascriptExecutor) driver;
        jsExecutor.executeScript("arguments[0].scrollIntoView({behavior: 'auto', block: 'center', inline: 'center'});", getWrappedElement());
        waitABit(500);
    }

    @Override
    public void scrollToEnd() {
        var driver = getDriver();
        JavascriptExecutor jsExecutor = (JavascriptExecutor) driver;
        jsExecutor.executeScript("arguments[0].scrollTop = arguments[0].scrollHeight", getWrappedElement());
        waitABit(1000);
    }

    @Override
    public void scrollElementToTop() {
        var driver = getDriver();
        JavascriptExecutor jsExecutor = (JavascriptExecutor) driver;
        jsExecutor.executeScript("arguments[0].scrollIntoView(true);", getWrappedElement());
        waitABit(1000);
    }
    @Override
    public boolean isEnabled() {
        return getWrappedElement().isEnabled();
    }

}
