package actions;

import common.constants.Constants;
import net.serenitybdd.core.steps.UIInteractionSteps;
import pages.BasePage;
import utils.Buttons;

public class MainMenuActions extends UIInteractionSteps {

    private BaseActions baseActions;
    private BasePage basePage;
    private Buttons buttons;

    public void openMainCategory(String categoryName) {
        baseActions.selectMainCategory(categoryName);

    }

    public void openSubCategory(String subCategory) {
        baseActions.selectSubCategory(subCategory);
    }

    public void openCart() {
        basePage.openCart().waitUntilVisible();
        basePage.openCart().click();
        //wait a bit for the page to load
        waitABit(3000);
    }

    public void openCartAndProceedToCheckout() {
        openCart();
        var checkoutButton = buttons.buttonWithText(Constants.Buttons.TO_CHECKOUT);
        checkoutButton.waitUntilVisible();
        checkoutButton.click();
        //if the checkoutButton is still visible, click it again
        if (checkoutButton.isVisible()) {
            checkoutButton.click();
        }
        waitABit(3000);
    }

    public void navigateBack() {
        baseActions.navigateBack();
    }

    public void goToMyAccountPage() {
        basePage.myAccountButton().waitUntilVisible();
        basePage.myAccountButton().click();
    }
}
