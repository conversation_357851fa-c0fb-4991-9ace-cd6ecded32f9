package actions;

import common.constants.IStorageKey;
import common.constants.Constants;
import lombok.val;
import net.serenitybdd.core.steps.UIInteractionSteps;
import org.assertj.core.api.SoftAssertions;
import org.jetbrains.annotations.NotNull;
import pages.ProductPage;
import pages.CartPage;
import pages.CategoryPage;
import utils.Buttons;
import utils.storage.Storage;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.Random;

import static common.constants.Constants.ErrorMessages.MAXIMUM_QUANTITY_ERROR;
import static common.constants.Constants.Headers.QUESTION_AND_ANSWERS_HEADER;
import static common.constants.Constants.Headers.RELATED_PRODUCTS_HEADER;
import static common.constants.IStorageKey.*;

@Slf4j
public class ProductPageActions extends UIInteractionSteps {
    private Buttons buttons;
    private ProductPage productPage;
    private CartPage cartPage;

    /*public void addTheProductToCart() {

        buttons.buttonWithText(Constants.Buttons.ADD_TO_CART).click();
        productPage.openCartButtonAfterAddingProduct().waitUntilVisible(Duration.ofSeconds(10));
    }*/

    public void addTheProductToCart() {
        String productPrice;
        // Store product name and price for later verification
        String productName = productPage.productName().getText();
        if (productPage.discountPrice().isPresent()) {
            // If discount price is available, use it
            productPrice = productPage.discountPrice().getText();
        } else {
            // Otherwise, use the regular price
            productPrice = productPage.productPrice().getText();
        }

        // Store product details in Storage
        Storage.getStorage().saveValue(IStorageKey.PRODUCT_NAME_FOR_ORDER, productName);
        Storage.getStorage().saveValue(IStorageKey.PRODUCT_PRICE_FOR_ORDER, productPrice);

        buttons.buttonWithText(Constants.Buttons.ADD_TO_CART).click();
        productPage.openCartButtonAfterAddingProduct().waitUntilVisible(Duration.ofSeconds(10));

    }

    public void verifyProductDetailPage() {
        //Get the product name
        String productName = productPage.productName().getText();
        //verify the product name is the same as the name from storage
        String storedProductName = Storage.getStorage().getValue(IStorageKey.FIRST_PRODUCT_NAME);
        val softAssertions = new SoftAssertions();
        softAssertions.assertThat(productName).isEqualTo(storedProductName);
        //Verify the browser title contains the product name
        //softAssertions.assertThat(productPage.getDriver().getTitle()).contains(productName);
        softAssertions.assertAll();
    }

    public void verifyProductImageIsDisplayed() {
        val softAssertions = new SoftAssertions();
        productPage.productImage().waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(productPage.productImage().isDisplayed()).isTrue();
        softAssertions.assertAll();
    }

    public void verifyDeliveryPriceHeaderShows(String expectedDeliveryPrice) {
        val softAssertions = new SoftAssertions();
        String actualDeliveryPrice = null;

        try {
            productPage.deliveryPriceHeader().waitUntilVisible(Duration.ofSeconds(5));
            actualDeliveryPrice = productPage.deliveryPriceHeader().getText();
        } catch (Exception e) {
            try {
                productPage.deliveryPriceHeaderGeneric().waitUntilVisible(Duration.ofSeconds(5));
                actualDeliveryPrice = productPage.deliveryPriceHeaderGeneric().getText();
            } catch (Exception ex) {
                softAssertions.fail("Could not find delivery price header element");
            }
        }
        if (actualDeliveryPrice != null) {
            softAssertions.assertThat(actualDeliveryPrice).isEqualTo(expectedDeliveryPrice);
        }
        softAssertions.assertAll();
    }

    public void verifyDimensionAndDetailsButtonIsDisplayed() {
        val softAssertions = new SoftAssertions();
        productPage.dimensionAndDetailsButton().waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(productPage.dimensionAndDetailsButton().isDisplayed()).isTrue();
        softAssertions.assertAll();
    }

    public void clickOnDimensionAndDetailsButton() {
        productPage.dimensionAndDetailsButton().waitUntilClickable(Duration.ofSeconds(10));
        productPage.dimensionAndDetailsButton().click();
    }

    public void verifyDimensionAndDetailsHeaderShows(String expectedHeader) {
        val softAssertions = new SoftAssertions();
        productPage.dimensionAndDetailsHeader().waitUntilVisible(Duration.ofSeconds(10));
        String actualHeader = productPage.dimensionAndDetailsHeader().getText();
        softAssertions.assertThat(actualHeader).isEqualTo(expectedHeader);
        softAssertions.assertAll();
    }

    public void verifyDimensionAndDetailsContentIsDisplayed() {
        val softAssertions = new SoftAssertions();
        productPage.dimensionAndDetailsContent().waitUntilVisible(Duration.ofSeconds(10));
        String contentText = productPage.dimensionAndDetailsContent().getText();
        softAssertions.assertThat(contentText).contains("Długość");
        softAssertions.assertThat(contentText).contains("Materiał");
        softAssertions.assertThat(contentText).contains("Maksymalne obciążenie");
        softAssertions.assertThat(contentText).contains("Szerokość");
        softAssertions.assertThat(contentText).contains("Wysokość");
        softAssertions.assertThat(contentText).contains("Waga produktu");
        softAssertions.assertThat(contentText).contains("Kolekcja");
        softAssertions.assertThat(contentText).containsPattern("\\d+(\\.\\d+)?cm");
        softAssertions.assertThat(contentText).containsPattern("\\d+(\\.\\d+)?kg");
        softAssertions.assertThat(contentText).containsPattern("Kolekcja\\s+\\w+");
        softAssertions.assertAll();
    }

    public void verifyProductAddedViewCartButtonIsDisplayed() {
        val softAssertions = new SoftAssertions();
        productPage.productAddedViewCartButton().waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(productPage.productAddedViewCartButton().isDisplayed()).isTrue();
        softAssertions.assertAll();
    }

    public void clickOnProductAddedViewCartButton() {
        productPage.productAddedViewCartButton().waitUntilClickable(Duration.ofSeconds(10));
        productPage.productAddedViewCartButton().click();
    }

    public void verifyCartIsOpened() {
        val softAssertions = new SoftAssertions();
        try {
            waitABit(5000);
            String currentUrl = productPage.getDriver().getCurrentUrl();
            softAssertions.assertThat(currentUrl).contains("/cart");
        } catch (Exception e) {
            String pageTitle = productPage.getDriver().getTitle();
            softAssertions.assertThat(pageTitle).containsIgnoringCase("koszyk");
        }
        softAssertions.assertAll();
    }

    public void verifyProductAmountInputShows(String expectedAmount) {
        val softAssertions = new SoftAssertions();
        // Wait for the product quantity input to be visible (using the more generic locator)
        productPage.productQuantityInput().waitUntilVisible(Duration.ofSeconds(10));
        // Get the actual amount value
        String actualAmount = productPage.productQuantityInput().getValue();
        // Verify the amount shows the expected value
        softAssertions.assertThat(actualAmount).as("Product amount input should show: " + expectedAmount).isEqualTo(expectedAmount);
        softAssertions.assertAll();

        // Store the initial quantity for later verification
        Storage.getStorage().saveValue(INITIAL_QUANTITY_KEY, expectedAmount);
    }

    public void clickPlusButtonRandomly(int minClicks, int maxClicks) {
        Random random = new Random();
        int clickCount = random.nextInt(maxClicks - minClicks + 1) + minClicks;

        // Get initial quantity
        String initialQuantityStr = productPage.productQuantityInput().getValue();
        int initialQuantity = Integer.parseInt(initialQuantityStr);

        // Click plus button the random number of times
        for (int i = 0; i < clickCount; i++) {
            productPage.plusButton().waitUntilClickable(Duration.ofSeconds(5));
            productPage.plusButton().click();
            waitABit(500); // Small wait between clicks
        }

        // Calculate expected quantity and store it
        int expectedQuantity = initialQuantity + clickCount;
        Storage.getStorage().saveValue(EXPECTED_QUANTITY_KEY, String.valueOf(expectedQuantity));
        Storage.getStorage().saveValue(PLUS_CLICKS_KEY, String.valueOf(clickCount));
    }

    public void verifyProductQuantityInCartMatchesSelectedAmount() {
        val softAssertions = new SoftAssertions();

        // Get expected quantity from storage
        String expectedQuantityStr = Storage.getStorage().getValue(EXPECTED_QUANTITY_KEY);

        // Wait for cart page to load and get actual quantity from cart
        waitABit(3000); // Wait for cart to load

        // The cart verification will be handled by existing CartActions
        // We just need to verify that the stored quantity matches what we expect
        softAssertions.assertThat(expectedQuantityStr).as("Expected quantity should be stored correctly").isNotNull();

        softAssertions.assertAll();
    }

    public void verifyProductAmountInputIsUpdatedCorrectly() {
        val softAssertions = new SoftAssertions();

        // Get expected quantity from storage
        String expectedQuantityStr = Storage.getStorage().getValue(EXPECTED_QUANTITY_KEY);
        int expectedQuantity = Integer.parseInt(expectedQuantityStr);

        // Wait for the input to be updated
        waitABit(1000);

        // Get actual quantity from input
        String actualQuantityStr = productPage.productQuantityInput().getValue();
        int actualQuantity = Integer.parseInt(actualQuantityStr);

        // Verify the quantity matches
        softAssertions.assertThat(actualQuantity).as("Product amount input should show the correct quantity after plus clicks").isEqualTo(expectedQuantity);

        softAssertions.assertAll();
    }

    public void clickMinusButtonRandomly(int minClicks, int maxClicks) {
        Random random = new Random();
        int clickCount = random.nextInt(maxClicks - minClicks + 1) + minClicks;
        String currentQuantityStr = productPage.productQuantityInput().getValue();
        int currentQuantity = Integer.parseInt(currentQuantityStr);
        // Click minus button the random number of times
        for (int i = 0; i < clickCount; i++) {
            productPage.minusButton().waitUntilClickable(Duration.ofSeconds(5));
            productPage.minusButton().click();
            waitABit(500);
        }
        int expectedQuantity = Math.max(1, currentQuantity - clickCount);
        Storage.getStorage().saveValue(EXPECTED_QUANTITY_KEY, String.valueOf(expectedQuantity));
        Storage.getStorage().saveValue(MINUS_CLICKS_KEY, String.valueOf(clickCount));
    }

    public void verifyProductAmountInputIsUpdatedCorrectlyAfterMinusClicks() {
        val softAssertions = new SoftAssertions();
        String expectedQuantityStr = Storage.getStorage().getValue(EXPECTED_QUANTITY_KEY);
        int expectedQuantity = Integer.parseInt(expectedQuantityStr);
        waitABit(1000);
        String actualQuantityStr = productPage.productQuantityInput().getValue();
        int actualQuantity = Integer.parseInt(actualQuantityStr);
        softAssertions.assertThat(actualQuantity).isEqualTo(expectedQuantity);
        softAssertions.assertThat(actualQuantity).isGreaterThanOrEqualTo(1);

        softAssertions.assertAll();
    }

    public void clickMinusButtonTimes(int times) {
        String initialQuantityStr = productPage.productQuantityInput().getValue();
        int initialQuantity = Integer.parseInt(initialQuantityStr);
        for (int i = 0; i < times; i++) {
            productPage.minusButton().waitUntilClickable(Duration.ofSeconds(5));
            productPage.minusButton().click();
            waitABit(500);
        }
        int expectedQuantity = Math.max(1, initialQuantity - times);
        Storage.getStorage().saveValue(EXPECTED_QUANTITY_KEY, String.valueOf(expectedQuantity));
    }

    public void clickPlusButtonTimes(int times) {
        String initialQuantityStr = productPage.productQuantityInput().getValue();
        int initialQuantity = Integer.parseInt(initialQuantityStr);
        for (int i = 0; i < times; i++) {
            productPage.plusButton().waitUntilClickable(Duration.ofSeconds(5));
            productPage.plusButton().click();
            waitABit(200); // Smaller wait for many clicks
            if (i > 0 && i % 10 == 0) {
                String currentQuantityStr = productPage.productQuantityInput().getValue();
                int currentQuantity = Integer.parseInt(currentQuantityStr);
                int expectedAtThisPoint = initialQuantity + i + 1;

                if (currentQuantity < expectedAtThisPoint) {
                    Storage.getStorage().saveValue(EXPECTED_QUANTITY_KEY, String.valueOf(currentQuantity));
                    return;
                }
            }
        }
        int expectedQuantity = initialQuantity + times;
        Storage.getStorage().saveValue(EXPECTED_QUANTITY_KEY, String.valueOf(expectedQuantity));
    }

    public void verifyProductAmountInputDoesNotExceedReasonableLimit() {
        val softAssertions = new SoftAssertions();
        waitABit(2000);
        String actualQuantityStr = productPage.productQuantityInput().getValue();
        int actualQuantity = Integer.parseInt(actualQuantityStr);
        softAssertions.assertThat(actualQuantity).isLessThanOrEqualTo(999);
        softAssertions.assertThat(actualQuantity).isGreaterThan(0);
        Storage.getStorage().saveValue(EXPECTED_QUANTITY_KEY, String.valueOf(actualQuantity));

        softAssertions.assertAll();
    }

    public void verifyPlusButtonBehaviorAtMaximumQuantity() {
        val softAssertions = new SoftAssertions();

        String currentQuantityStr = productPage.productQuantityInput().getValue();
        int currentQuantity = Integer.parseInt(currentQuantityStr);

        for (int i = 0; i < 3; i++) {
            productPage.plusButton().waitUntilClickable(Duration.ofSeconds(5));
            productPage.plusButton().click();
            waitABit(200);
        }

        String newQuantityStr = productPage.productQuantityInput().getValue();
        int newQuantity = Integer.parseInt(newQuantityStr);
        softAssertions.assertThat(newQuantity).isGreaterThanOrEqualTo(currentQuantity);
        softAssertions.assertThat(productPage.plusButton().isEnabled()).isTrue();

        softAssertions.assertAll();
    }

    public void verifyMaximumQuantityErrorMessageIsDisplayed() {
        val softAssertions = new SoftAssertions();

        productPage.maximumProductQuantityErrorMessage().waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(productPage.maximumProductQuantityErrorMessage().isDisplayed()).isTrue();

        String actualErrorMessage = productPage.maximumProductQuantityErrorMessage().getText();
        softAssertions.assertThat(actualErrorMessage).isEqualTo(MAXIMUM_QUANTITY_ERROR);

        softAssertions.assertAll();
    }

    public void verifyProductQuantityInCartMatchesMaximumAllowedQuantity() {
        val softAssertions = new SoftAssertions();
        String expectedQuantityStr = Storage.getStorage().getValue(EXPECTED_QUANTITY_KEY);
        int expectedQuantity = Integer.parseInt(expectedQuantityStr);
        waitABit(3000);

        try {
            cartPage.productQuantityInCart().getElementByIndex(0).waitUntilVisible(Duration.ofSeconds(10));
            String actualQuantityStr = cartPage.productQuantityInCart().getElementByIndex(0).getValue();
            int actualQuantity = Integer.parseInt(actualQuantityStr);

            softAssertions.assertThat(actualQuantity).isEqualTo(expectedQuantity);
            softAssertions.assertThat(actualQuantity).isLessThanOrEqualTo(50);
            softAssertions.assertThat(actualQuantity).isGreaterThan(0);

        } catch (Exception e) {
            softAssertions.fail("Could not verify product quantity in cart: " + e.getMessage());
        }

        softAssertions.assertAll();
    }

    public void clickOnAskQuestionButton() {
        productPage.askQuestionButton().waitUntilClickable(Duration.ofSeconds(10));
        productPage.askQuestionButton().click();
        waitABit(2000);
    }

    public void enterEmailForQuestion(String email) {
        productPage.emailInputForQuestion().waitUntilVisible(Duration.ofSeconds(10));
        productPage.emailInputForQuestion().sendKeys(email);
    }

    public void enterMessageForQuestion(String message) {
        productPage.messageInputForQuestion().waitUntilVisible(Duration.ofSeconds(10));
        productPage.messageInputForQuestion().sendKeys(message);
    }

    public void checkAgreeToPrivacyPolicyCheckbox() {
        productPage.agreeToPrivacyPolicyCheckbox().waitUntilClickable(Duration.ofSeconds(10));
        productPage.agreeToPrivacyPolicyCheckbox().click();
    }

    public void clickSendMessageButton() {
        productPage.sendMessageButton().waitUntilClickable(Duration.ofSeconds(10));
        productPage.sendMessageButton().click();
        waitABit(3000);
    }

    public void verifyMessageSentConfirmationIsDisplayed() {
        val softAssertions = new SoftAssertions();
        productPage.messageSentText().waitUntilVisible(Duration.ofSeconds(15));
        softAssertions.assertThat(productPage.messageSentText().isVisible()).isTrue();

        String actualText = productPage.messageSentText().getText();
        softAssertions.assertThat(actualText).contains("Wysłano");

        softAssertions.assertAll();
    }

    public void verifyErrorMessageForUnselectedPrivacyPolicyCheckbox(String expectedErrorMessage) {
        val softAssertions = new SoftAssertions();
        productPage.errorMessageForUnselectedPrivacyPolicyCheckbox().waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(productPage.errorMessageForUnselectedPrivacyPolicyCheckbox().isVisible()).isTrue();
        String actualErrorMessage = productPage.errorMessageForUnselectedPrivacyPolicyCheckbox().getText();
        softAssertions.assertThat(actualErrorMessage).isEqualTo(expectedErrorMessage);
        softAssertions.assertAll();
    }

    public void enterRandomEmailForQuestion() {
        String[] domains = {"gsgsgsdg.com", "dgsdgsdg.com", "dgwegtwe.com", "rwqruwru.com", "ruwruwr.com"};
        Random random = new Random();
        String randomDomain = domains[random.nextInt(domains.length)];
        String randomEmail = "dajar-test-user" + random.nextInt(10000) + "@" + randomDomain;
        enterEmailForQuestion(randomEmail);
    }

    public void typeQuantityInInput(String quantity) {
        productPage.productQuantityInput().waitUntilVisible(Duration.ofSeconds(10));
        productPage.productQuantityInput().clearAndType(quantity);
        waitABit(1000);
        Storage.getStorage().saveValue(EXPECTED_QUANTITY_KEY, quantity);
        Storage.getStorage().saveValue(IStorageKey.FIRST_PRODUCT_QUANTITY, quantity);
        updateTotalPriceInStorage(quantity);
    }

    public void verifyProductQuantityInputShowsValue(String expectedQuantity) {
        val softAssertions = new SoftAssertions();
        productPage.productQuantityInput().waitUntilVisible(Duration.ofSeconds(10));
        String actualQuantity = productPage.productQuantityInput().getValue();
        softAssertions.assertThat(actualQuantity).isEqualTo(expectedQuantity);
        softAssertions.assertAll();
    }

    public void verifyMaximumQuantityErrorMessageForInput() {
        val softAssertions = new SoftAssertions();
        try {
            productPage.maximumProductQuantityErrorMessage().waitUntilVisible(Duration.ofSeconds(10));
            softAssertions.assertThat(productPage.maximumProductQuantityErrorMessage().isDisplayed()).isTrue();
            String actualErrorMessage = productPage.maximumProductQuantityErrorMessage().getText();
            softAssertions.assertThat(actualErrorMessage).isEqualTo(MAXIMUM_QUANTITY_ERROR);

        } catch (Exception e) {
            String currentQuantityStr = productPage.productQuantityInput().getValue();
            int currentQuantity = Integer.parseInt(currentQuantityStr);
            if (currentQuantity <= 50) {
                softAssertions.assertThat(currentQuantity).isLessThanOrEqualTo(50);
            } else {
                softAssertions.fail("Expected error message or quantity correction, but quantity is: " + currentQuantity);
            }
        }

        softAssertions.assertAll();
    }

    public void clickMinusButtonAfterTypingQuantity() {
        String currentQuantityStr = productPage.productQuantityInput().getValue();
        int currentQuantity = Integer.parseInt(currentQuantityStr);
        productPage.minusButton().waitUntilClickable(Duration.ofSeconds(5));
        productPage.minusButton().click();
        waitABit(1000);
        int expectedQuantity = Math.max(1, currentQuantity - 1);
        Storage.getStorage().saveValue(EXPECTED_QUANTITY_KEY, String.valueOf(expectedQuantity));
        Storage.getStorage().saveValue(IStorageKey.FIRST_PRODUCT_QUANTITY, String.valueOf(expectedQuantity));
        updateTotalPriceInStorage(String.valueOf(expectedQuantity));
    }

    public void clickPlusButtonAfterTypingQuantity() {
        String currentQuantityStr = productPage.productQuantityInput().getValue();
        int currentQuantity = Integer.parseInt(currentQuantityStr);
        productPage.plusButton().waitUntilClickable(Duration.ofSeconds(5));
        productPage.plusButton().click();
        waitABit(1000);
        int expectedQuantity = currentQuantity + 1;
        Storage.getStorage().saveValue(EXPECTED_QUANTITY_KEY, String.valueOf(expectedQuantity));
        Storage.getStorage().saveValue(IStorageKey.FIRST_PRODUCT_QUANTITY, String.valueOf(expectedQuantity));
        updateTotalPriceInStorage(String.valueOf(expectedQuantity));
    }

    public void verifyQuantityAfterButtonClick() {
        val softAssertions = new SoftAssertions();
        String expectedQuantityStr = Storage.getStorage().getValue(EXPECTED_QUANTITY_KEY);
        int expectedQuantity = Integer.parseInt(expectedQuantityStr);
        waitABit(1000);
        String actualQuantityStr = productPage.productQuantityInput().getValue();
        int actualQuantity = Integer.parseInt(actualQuantityStr);
        softAssertions.assertThat(actualQuantity).isEqualTo(expectedQuantity);
        softAssertions.assertAll();
    }

    private void updateTotalPriceInStorage(String quantity) {
        try {
            String originalPriceStr = Storage.getStorage().getValue(IStorageKey.PRODUCT_PRICE_FOR_ORDER);
            if (originalPriceStr == null) {
                if (productPage.discountPrice().isPresent()) {
                    originalPriceStr = productPage.discountPrice().getText();
                } else {
                    originalPriceStr = productPage.productPrice().getText();
                }
                Storage.getStorage().saveValue(IStorageKey.PRODUCT_PRICE_FOR_ORDER, originalPriceStr);
            }

            if (originalPriceStr != null) {
                String totalPriceStr = getString(quantity, originalPriceStr);
                Storage.getStorage().saveValue(IStorageKey.FIRST_PRODUCT_PRICE, totalPriceStr);
            }
        } catch (Exception e) {
            log.info("Failed to update total price in storage: {}", e.getMessage());
        }
    }

    private static @NotNull String getString(String quantity, String originalPriceStr) {
        String priceNumericStr = originalPriceStr.replaceAll("[^0-9,.]", "");
        if (priceNumericStr.contains(",")) {
            priceNumericStr = priceNumericStr.replace(",", ".");
        }
        double unitPrice = Double.parseDouble(priceNumericStr);
        int quantityInt = Integer.parseInt(quantity);
        double totalPrice = unitPrice * quantityInt;
        return String.format("%.2f zł", totalPrice).replace(".", ",");
    }

    public void verifyRelatedProductsHeader() {
        val softAssertions = new SoftAssertions();
        productPage.relatedProductsHeader().waitUntilVisible(Duration.ofSeconds(15));
        softAssertions.assertThat(productPage.relatedProductsHeader().isVisible()).isTrue();
        String actualHeaderText = productPage.relatedProductsHeader().getText();
        softAssertions.assertThat(actualHeaderText).contains(RELATED_PRODUCTS_HEADER);
        softAssertions.assertAll();
    }

    public void verifyRelatedProductsAreListed() {
        val softAssertions = new SoftAssertions();
        try {
            productPage.relatedProductsList().waitUntilVisible(Duration.ofSeconds(15));
            int relatedProductsCount = productPage.relatedProductsList().getSize();
            softAssertions.assertThat(relatedProductsCount).isGreaterThan(0);
        } catch (Exception e) {
            softAssertions.fail("Related products list not found or not visible: " + e.getMessage());
        }

        softAssertions.assertAll();
    }

    public void verifyRelatedProductPricesAreDisplayed() {
        val softAssertions = new SoftAssertions();
        productPage.relatedProductPrices().waitUntilVisible(Duration.ofSeconds(10));

        int pricesCount = productPage.relatedProductPrices().getSize();
        softAssertions.assertThat(pricesCount).isGreaterThan(0);
        for (int i = 0; i < Math.min(pricesCount, 5); i++) { // Check first 5 prices
            String priceText = productPage.relatedProductPrices().getElementByIndex(i).getText();
            softAssertions.assertThat(priceText).contains("zł");
        }

        softAssertions.assertAll();
    }

    public void verifyRelatedProductNamesAreDisplayed() {
        val softAssertions = new SoftAssertions();
        productPage.relatedProductNames().waitUntilVisible(Duration.ofSeconds(10));
        int namesCount = productPage.relatedProductNames().getSize();
        softAssertions.assertThat(namesCount).isGreaterThan(0);
        for (int i = 0; i < Math.min(namesCount, 5); i++) { // Check first 5 names
            String nameText = productPage.relatedProductNames().getElementByIndex(i).getText();
            softAssertions.assertThat(nameText).isNotEmpty();
        }
        softAssertions.assertAll();
    }

    public void verifyBreadcrumbsAreDisplayed() {
        val softAssertions = new SoftAssertions();
        productPage.breadcrumbsOnProductPage().waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(productPage.breadcrumbsOnProductPage().isDisplayed()).isTrue();
        String breadcrumbText = productPage.breadcrumbsOnProductPage().getText().trim();
        softAssertions.assertThat(breadcrumbText).contains("Strona główna");
        softAssertions.assertThat(breadcrumbText).contains("Meble ogrodowe");
        softAssertions.assertThat(breadcrumbText).contains("Huśtawki");
        softAssertions.assertThat(breadcrumbText).matches(".*Strona główna[\\s\\n]*Meble ogrodowe[\\s\\n]*Huśtawki.*");
        softAssertions.assertAll();
    }

    public void verifyBreadcrumbLinksNavigation() {
        val softAssertions = new SoftAssertions();
        productPage.breadcrumbLinks().waitUntilVisible(Duration.ofSeconds(10));
        int breadcrumbCount = productPage.breadcrumbLinks().getSize();
        softAssertions.assertThat(breadcrumbCount).isGreaterThan(0);

        String[] breadcrumbTexts = new String[breadcrumbCount];
        for (int i = 0; i < breadcrumbCount; i++) {
            breadcrumbTexts[i] = productPage.breadcrumbLinks().getTextByIndex(i).trim();
        }

        String originalUrl = getDriver().getCurrentUrl();

        for (int i = breadcrumbCount - 1; i >= 0; i--) {
            if (i < breadcrumbCount - 1) {
                getDriver().get(originalUrl);
                waitABit(3000);
                productPage.breadcrumbLinks().waitUntilVisible(Duration.ofSeconds(10));
            }
            String linkText = breadcrumbTexts[i];
            productPage.breadcrumbLinks().getElementByIndex(i).click();
            waitABit(3000);

            if (linkText.equals("Strona główna")) {
                String currentUrl = getDriver().getCurrentUrl();
                softAssertions.assertThat(currentUrl).matches(".*/(pl/PL/?)?$");

            } else if (linkText.equals("Meble ogrodowe") || linkText.equals("Huśtawki")) {
                try {
                    CategoryPage categoryPage = new CategoryPage();
                    categoryPage.categoryPageHeader().waitUntilVisible(Duration.ofSeconds(15));
                    String categoryHeaderText = categoryPage.categoryPageHeader().getText();
                    softAssertions.assertThat(categoryHeaderText).containsIgnoringCase(linkText);
                } catch (Exception e) {
                    String currentUrl = getDriver().getCurrentUrl();
                    String expectedUrlPart = linkText.toLowerCase().replace(" ", "-").replace("ś", "s").replace("ą", "a").replace("ę", "e").replace("ł", "l").replace("ń", "n").replace("ó", "o").replace("ć", "c").replace("ź", "z").replace("ż", "z");

                    softAssertions.assertThat(currentUrl).containsIgnoringCase(expectedUrlPart);
                }
            }
        }

        softAssertions.assertAll();
    }

    public void verifyProductSkuIsDisplayed() {
        val softAssertions = new SoftAssertions();
        productPage.productSku().waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(productPage.productSku().isVisible()).isTrue();
        String skuText = productPage.productSku().getText();
        softAssertions.assertThat(skuText).isNotEmpty();
        softAssertions.assertThat(skuText).contains("Kod");
        softAssertions.assertAll();


    }

    public void verifyIncludesVatTextShows(String expectedText) {
        val softAssertions = new SoftAssertions();
        productPage.includesVatText().waitUntilVisible(Duration.ofSeconds(10));
        String actualText = productPage.includesVatText().getText();
        softAssertions.assertThat(actualText).isEqualTo(expectedText);
        softAssertions.assertAll();
    }

    public void clickOnCostWithoutDeliveryLink() {
        productPage.withoutDeliveryCostLink().waitUntilVisible(Duration.ofSeconds(10));
        productPage.withoutDeliveryCostLink().waitUntilClickable(Duration.ofSeconds(5));
        productPage.withoutDeliveryCostLink().click();
        waitABit(2000);

    }

    public void verifyProductDescriptionIsDisplayedAndContainsContent() {
        val softAssertions = new SoftAssertions();
        productPage.productDescription().waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(productPage.productDescription().isVisible()).isTrue();
        String descriptionText = productPage.productDescription().getText();
        softAssertions.assertThat(descriptionText).isNotEmpty();
        String trimmedDescription = descriptionText.trim();
        softAssertions.assertThat(trimmedDescription).isNotEmpty().hasSizeGreaterThan(10);
        softAssertions.assertAll();
    }

    public void clickOnDeliveryDetailsLinkInProductDescription() {
        productPage.deliveryDetailsLink().waitUntilVisible(Duration.ofSeconds(10));
        productPage.deliveryDetailsLink().waitUntilClickable(Duration.ofSeconds(5));
        productPage.deliveryDetailsLink().click();
        waitABit(2000);
    }

    public void verifyGraphicDimensionDetailIsDisplayed() {
        val softAssertions = new SoftAssertions();
        try {
            waitABit(3000);
            if (productPage.dimensionAndDetailsGraphic().isPresent()) {
                try {
                    productPage.dimensionAndDetailsGraphic().waitUntilVisible(Duration.ofSeconds(5));
                    softAssertions.assertThat(productPage.dimensionAndDetailsGraphic().isVisible()).isTrue();

                } catch (Exception visibilityException) {
                    softAssertions.assertThat(productPage.dimensionAndDetailsGraphic().isPresent()).isTrue();
                }
            } else {
                softAssertions.fail("Graphic dimension detail element is not present on the page. This might indicate the product doesn't have dimension graphics or the locator needs adjustment.");
            }

        } catch (Exception e) {
            softAssertions.fail("Failed to verify graphic dimension detail: " + e.getMessage());
        }

        softAssertions.assertAll();
    }

    public void clickOnQuestionAndAnswersButton() {
        productPage.questionAndAnswersButton().waitUntilClickable(Duration.ofSeconds(10));
        productPage.questionAndAnswersButton().click();
        waitABit(2000);
    }

    public void verifyQuestionAndAnswersSectionDisplaysCorrectCountAndContent() {
        val softAssertions = new SoftAssertions();
        try {
            waitABit(3000);
            productPage.questionAndAnswersHeader().waitUntilVisible(Duration.ofSeconds(10));
            String headerText = productPage.questionAndAnswersHeader().getText();
            softAssertions.assertThat(headerText).contains(QUESTION_AND_ANSWERS_HEADER);
            String buttonText = productPage.questionAndAnswersButton().getText();

            int expectedCount = 0;
            if (buttonText.contains("(") && buttonText.contains(")")) {
                String countStr = buttonText.substring(buttonText.indexOf("(") + 1, buttonText.indexOf(")"));
                try {
                    expectedCount = Integer.parseInt(countStr);
                } catch (NumberFormatException e) {
                    softAssertions.fail("Could not parse question count from button text: " + buttonText);
                    softAssertions.assertAll();
                    return;
                }
            } else {
                softAssertions.fail("Button text does not contain count in expected format: " + buttonText);
                softAssertions.assertAll();
                return;
            }
            softAssertions.assertThat(buttonText).contains("Pytania i odpowiedzi");

            int actualCount = 0;
            try {
                productPage.questionAndAnswersList().waitUntilVisible(Duration.ofSeconds(10));
                actualCount = productPage.questionAndAnswersList().getSize();
            } catch (Exception e) {
                if (expectedCount == 0) {
                    softAssertions.assertThat(actualCount).isEqualTo(0);
                } else {
                    softAssertions.fail("Expected " + expectedCount + " questions but could not find question list: " + e.getMessage());
                }
                softAssertions.assertAll();
                return;
            }
            softAssertions.assertThat(actualCount).isEqualTo(expectedCount);
            if (actualCount > 0) {
                for (int i = 0; i < actualCount; i++) {
                    try {
                        String questionContent = productPage.questionAndAnswersList().getElementByIndex(i).getText();
                        softAssertions.assertThat(questionContent).isNotEmpty().hasSizeGreaterThan(10);
                    } catch (Exception e) {
                        softAssertions.fail("Could not get content for question/answer " + (i + 1) + ": " + e.getMessage());
                    }
                }
            }

        } catch (Exception e) {
            softAssertions.fail("Failed to verify question and answers section: " + e.getMessage());
        }

        softAssertions.assertAll();
    }

    public void verifyQuestionAndAnswersListItemsAreExpandableAndClosableWhenClicked() {
        val softAssertions = new SoftAssertions();

        try {
            waitABit(3000);
            int listSize = productPage.questionAndAnswersList().getSize();

            if (listSize == 0) {
                softAssertions.fail("No question and answer items found to test expandable/closable functionality");
                softAssertions.assertAll();
                return;
            }

            int itemsToTest = Math.min(listSize, 3);
            for (int i = 0; i < itemsToTest; i++) {
                try {
                    var questionItem = productPage.questionAndAnswersList().getElementByIndex(i);
                    String initialAriaExpanded = questionItem.getAttribute("aria-expanded");
                    softAssertions.assertThat(initialAriaExpanded).isNotNull();
                    questionItem.click();
                    waitABit(1500); // Wait for animation/expansion
                    String ariaExpandedAfterFirstClick = questionItem.getAttribute("aria-expanded");
                    softAssertions.assertThat(ariaExpandedAfterFirstClick).isNotEqualTo(initialAriaExpanded);
                    questionItem.click();
                    waitABit(1500); // Wait for animation/collapse
                    String ariaExpandedAfterSecondClick = questionItem.getAttribute("aria-expanded");
                    softAssertions.assertThat(ariaExpandedAfterSecondClick).isEqualTo(initialAriaExpanded);
                    if ("false".equals(initialAriaExpanded)) {
                        softAssertions.assertThat(ariaExpandedAfterFirstClick).isEqualTo("true");
                        softAssertions.assertThat(ariaExpandedAfterSecondClick).isEqualTo("false");
                    } else if ("true".equals(initialAriaExpanded)) {
                        softAssertions.assertThat(ariaExpandedAfterFirstClick).isEqualTo("false");
                        softAssertions.assertThat(ariaExpandedAfterSecondClick).isEqualTo("true");
                    }
                    softAssertions.assertThat(questionItem.isVisible()).isTrue();

                } catch (Exception e) {
                    softAssertions.fail("Failed to test expandable/closable functionality for question/answer item " + (i + 1) + ": " + e.getMessage());
                }
            }

        } catch (Exception e) {
            softAssertions.fail("Failed to verify question and answers expandable/closable functionality: " + e.getMessage());
        }

        softAssertions.assertAll();
    }
}
