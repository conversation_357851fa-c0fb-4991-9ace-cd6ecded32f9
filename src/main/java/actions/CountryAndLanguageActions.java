package actions;

import common.constants.Constants;
import common.constants.IStorageKey;
import common.test_data.CountryAndLanguageTestData;
import net.serenitybdd.core.steps.UIInteractionSteps;
import org.assertj.core.api.SoftAssertions;
import pages.BasePage;
import pages.CartPage;
import pages.ProductPage;
import utils.storage.Storage;

import java.time.Duration;

public class CountryAndLanguageActions extends UIInteractionSteps {
    private BasePage basePage;
    private CartPage cartPage;
    private ProductPage productPage;

    public void openCountrySelectionModal() {
        basePage.selectCountry().waitUntilVisible(Duration.ofSeconds(10));
        basePage.selectCountry().click();
        waitABit(Constants.CountryLanguage.MODAL_OPEN_WAIT);
    }

    public void closeCountrySelectionModal() {
        Long windowWidth = (Long) evaluateJavascript("return window.innerWidth;");
        int xOffset = windowWidth.intValue() - 50;
        int yOffset = 50; // 50px from top
        String clickScript = String.format(
            "document.elementFromPoint(%d, %d).click();",
            xOffset,
            yOffset
        );
        evaluateJavascript(clickScript);
        waitABit(Constants.CountryLanguage.MODAL_OPEN_WAIT);
    }

    public void selectCountry(String countryName) {
        CountryAndLanguageTestData.CountryLanguageInfo countryInfo = CountryAndLanguageTestData.getCountryInfo(countryName);
        if (countryInfo == null) {
            throw new IllegalArgumentException("Country not found in test data: " + countryName);
        }
        String polishNameForSelection = countryInfo.getPolishNameForSelection();
        basePage.selectCountryDropdown().waitUntilVisible(Duration.ofSeconds(10));
        basePage.selectCountryDropdown().click();
        waitABit(Constants.CountryLanguage.COUNTRY_SELECTION_WAIT);
        basePage.selectCountryOption(polishNameForSelection).waitUntilVisible(Duration.ofSeconds(10));
        basePage.selectCountryOption(polishNameForSelection).click();
        waitABit(Constants.CountryLanguage.COUNTRY_SELECTION_WAIT);
    }

    public void selectLanguage(String languageName) {
        basePage.selectLanguageDropdown().waitUntilVisible(Duration.ofSeconds(10));
        basePage.selectLanguageDropdown().click();
        waitABit(Constants.CountryLanguage.LANGUAGE_SELECTION_WAIT);
        basePage.selectLanguageOption(languageName).waitUntilVisible(Duration.ofSeconds(10));
        basePage.selectLanguageOption(languageName).click();
        waitABit(Constants.CountryLanguage.LANGUAGE_SELECTION_WAIT);
    }

    public void saveAndGoToShop() {
        basePage.saveAndGoToShopButton().waitUntilVisible(Duration.ofSeconds(10));
        basePage.saveAndGoToShopButton().click();
        waitABit(Constants.CountryLanguage.PAGE_RELOAD_WAIT);
    }

    public void selectCountryAndLanguage(String countryName, String languageName) {
        openCountrySelectionModal();
        selectCountry(countryName);
        selectLanguage(languageName);
        saveAndGoToShop();
    }

    public void verifyCountryNameInHeader(String expectedCountryName) {
        SoftAssertions softAssertions = new SoftAssertions();
        try {
            basePage.countryNameOnHeader().waitUntilVisible(Duration.ofSeconds(10));
            String actualCountryName = basePage.countryNameOnHeader().getText().trim();
            softAssertions.assertThat(actualCountryName).isEqualTo(expectedCountryName);
        } catch (Exception e) {
            softAssertions.fail("Failed to verify country name in header: " + e.getMessage());
        }
        softAssertions.assertAll();
    }

    public void verifyUrlContainsCountryAndLanguageCodes(String expectedCountryCode, String expectedLanguageCode) {
        SoftAssertions softAssertions = new SoftAssertions();
        try {
            String currentUrl = getDriver().getCurrentUrl();
            String expectedUrlPattern = String.format("/%s/%s", expectedLanguageCode.toLowerCase(), expectedCountryCode.toUpperCase());
            softAssertions.assertThat(currentUrl).contains(expectedUrlPattern);
        } catch (Exception e) {
            softAssertions.fail("Failed to verify URL pattern: " + e.getMessage());
        }
        softAssertions.assertAll();
    }

    public void verifyFooterDisplay(String expectedFooterText) {
        SoftAssertions softAssertions = new SoftAssertions();
        try {
            basePage.countryCurrencyLanguageHeaderAtFooter().waitUntilVisible(Duration.ofSeconds(10));
            String actualFooterText = basePage.countryCurrencyLanguageHeaderAtFooter().getText().trim();
            softAssertions.assertThat(actualFooterText).isEqualTo(expectedFooterText);
        } catch (Exception e) {
            softAssertions.fail("Failed to verify footer display: " + e.getMessage());
        }
        softAssertions.assertAll();
    }

    public void verifyCountryAndLanguageSelection(String countryName, String languageName) {
        CountryAndLanguageTestData.CountryLanguageInfo countryInfo = CountryAndLanguageTestData.getCountryInfo(countryName);

        if (countryInfo == null) {
            throw new IllegalArgumentException("Country not found in test data: " + countryName);
        }

        SoftAssertions softAssertions = new SoftAssertions();

        try {
            // Verify country name in header
            String expectedCountryNameInHeader = countryInfo.getCountryNameInLanguage(languageName);
            verifyCountryNameInHeader(expectedCountryNameInHeader);

            // Verify URL pattern
            String languageCode = getLanguageCode(languageName);
            verifyUrlContainsCountryAndLanguageCodes(countryInfo.getUrlCountryCode(), languageCode);

            // Verify footer display
            String expectedFooterText = countryInfo.getFooterFormat(languageName);
            verifyFooterDisplay(expectedFooterText);

        } catch (Exception e) {
            softAssertions.fail("Failed to verify country and language selection: " + e.getMessage());
        }

        softAssertions.assertAll();
    }

    private String getLanguageCode(String languageName) {
        return switch (languageName) {
            case Constants.CountryLanguage.POLISH -> "pl";
            case Constants.CountryLanguage.ENGLISH -> "en";
            case Constants.CountryLanguage.GERMAN -> "de";
            default -> throw new IllegalArgumentException("Unknown language: " + languageName);
        };
    }

    public void verifyLanguageDropdownIsEnabled() {
        SoftAssertions softAssertions = new SoftAssertions();
        try {
            basePage.selectLanguageDropdown().waitUntilVisible(Duration.ofSeconds(10));
            boolean isEnabled = basePage.selectLanguageDropdown().isEnabled();
            softAssertions.assertThat(isEnabled).isTrue();
        } catch (Exception e) {
            softAssertions.fail("Failed to verify language dropdown state: " + e.getMessage());
        }
        softAssertions.assertAll();
    }

    public void verifyLanguageDropdownIsDisabled() {
        SoftAssertions softAssertions = new SoftAssertions();
        try {
            basePage.selectLanguageDropdown().waitUntilVisible(Duration.ofSeconds(10));
            boolean hasDisabledClass = basePage.selectLanguageDropdown().hasClass("border-disabled");
            softAssertions.assertThat(hasDisabledClass).isTrue();
        } catch (Exception e) {
            softAssertions.fail("Failed to verify language dropdown disabled state: " + e.getMessage());
        }
        softAssertions.assertAll();
    }

    public void verifyGoToStoreButtonIsDisabled() {
        SoftAssertions softAssertions = new SoftAssertions();
        try {
            basePage.saveAndGoToShopButton().waitUntilVisible(Duration.ofSeconds(10));
            boolean hasDisabledAttribute = basePage.saveAndGoToShopButton().isAttributeValuePresent("disabled");
            softAssertions.assertThat(hasDisabledAttribute).isTrue();
        } catch (Exception e) {
            softAssertions.fail("Failed to verify go to store button disabled state: " + e.getMessage());
        }
        softAssertions.assertAll();
    }

    public void verifyCountrySelectionModalIsOpen() {
        SoftAssertions softAssertions = new SoftAssertions();
        try {
            basePage.selectCountryDropdown().waitUntilVisible(Duration.ofSeconds(10));
            boolean isVisible = basePage.selectCountryDropdown().isVisible();
            softAssertions.assertThat(isVisible).isTrue();
        } catch (Exception e) {
            softAssertions.fail("Failed to verify country selection modal state: " + e.getMessage());
        }
        softAssertions.assertAll();
    }

    public void verifyCountrySelectionModalIsClosed() {
        SoftAssertions softAssertions = new SoftAssertions();
        try {
            waitABit(Constants.CountryLanguage.MODAL_OPEN_WAIT);
            boolean isVisible = basePage.selectCountryDropdown().isVisible();
            softAssertions.assertThat(isVisible).isFalse();
        } catch (Exception e) {
            softAssertions.fail("Failed to verify country selection modal state: " + e.getMessage());
        }
        softAssertions.assertAll();
    }


    public void verifyCountrySelectionModalHeader() {
        SoftAssertions softAssertions = new SoftAssertions();
        try {
            basePage.selectCountryModalHeader().waitUntilVisible(Duration.ofSeconds(10));
            String actualHeaderText = basePage.selectCountryModalHeader().getText().trim();
            softAssertions.assertThat(actualHeaderText).isEqualTo(Constants.CountryLanguage.MODAL_HEADER);
        } catch (Exception e) {
            softAssertions.fail("Failed to verify country selection modal header: " + e.getMessage());
        }
        softAssertions.assertAll();
    }

    public void verifyCountrySelectionModalDescription() {
        SoftAssertions softAssertions = new SoftAssertions();
        try {
            basePage.selectCountryModalDescription().waitUntilVisible(Duration.ofSeconds(10));
            String actualDescriptionText = basePage.selectCountryModalDescription().getText().trim();
            softAssertions.assertThat(actualDescriptionText).isEqualTo(Constants.CountryLanguage.MODAL_DESCRIPTION);
        } catch (Exception e) {
            softAssertions.fail("Failed to verify country selection modal description: " + e.getMessage());
        }
        softAssertions.assertAll();
    }

    public void saveProductSkuFromPdp() {
        SoftAssertions softAssertions = new SoftAssertions();
        try {
            productPage.productSku().waitUntilVisible(Duration.ofSeconds(10));
            String productSkuText = productPage.productSku().getText().trim();
            String skuCode = productSkuText.replace("Kod:", "").replace("Kod", "").trim();
            Storage.getStorage().saveValue(IStorageKey.PRODUCT_SKU_FROM_PDP, skuCode);

        } catch (Exception e) {
            softAssertions.fail("Failed to save product SKU from PDP: " + e.getMessage());
        }
        softAssertions.assertAll();
    }

    public void verifyProductSkuMatchesInCart() {
        SoftAssertions softAssertions = new SoftAssertions();
        try {
            String expectedSku = Storage.getStorage().getValue(IStorageKey.PRODUCT_SKU_FROM_PDP);
            softAssertions.assertThat(expectedSku).isNotNull().isNotEmpty();
            cartPage.productCodeInCart().waitUntilVisible(Duration.ofSeconds(10));
            String actualSkuInCart = cartPage.productCodeInCart().getText().trim();
            String skuCodeInCart = actualSkuInCart.replace("SKU", "").replace(" ", "").trim();
            softAssertions.assertThat(skuCodeInCart).isEqualTo(expectedSku);

        } catch (Exception e) {
            softAssertions.fail("Failed to verify product SKU matches in cart: " + e.getMessage());
        }
        softAssertions.assertAll();
    }

    public void verifyGoToStoreButtonIsEnabled() {
        SoftAssertions softAssertions = new SoftAssertions();
        try {
            basePage.saveAndGoToShopButton().waitUntilVisible(Duration.ofSeconds(10));
            boolean hasDisabledAttribute = basePage.saveAndGoToShopButton().isAttributeValuePresent("disabled");
            softAssertions.assertThat(hasDisabledAttribute).isFalse();
        } catch (Exception e) {
            softAssertions.fail("Failed to verify go to store button enabled state: " + e.getMessage());
        }
        softAssertions.assertAll();
    }
}
