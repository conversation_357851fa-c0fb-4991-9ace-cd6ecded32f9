package actions;

import common.constants.Constants;
import net.serenitybdd.annotations.Step;
import net.serenitybdd.annotations.Steps;
import net.serenitybdd.core.steps.UIInteractionSteps;
import org.assertj.core.api.SoftAssertions;
import org.openqa.selenium.By;
import org.openqa.selenium.Keys;
import pages.LoginPage;

import java.time.Duration;
import java.util.Random;

public class RegistrationActions extends UIInteractionSteps {

    @Steps
    LoginPage loginPage;


    @Step
    public void enterValidRegistrationInformationWithAllFields() {
        loginPage.registrationFirstNameInput().waitUntilVisible();
        loginPage.registrationFirstNameInput().sendKeys(Constants.TestData.REGISTRATION_FIRST_NAME);
        loginPage.registrationLastNameInput().sendKeys(Constants.TestData.REGISTRATION_LAST_NAME);
        loginPage.registrationEmailInput().sendKeys(generateRandomEmail());
        loginPage.registrationPasswordInput().sendKeys(Constants.TestData.REGISTRATION_PASSWORD);
    }

    @Step
    public void enterInvalidEmailFormatForRegistration() {
        loginPage.registrationEmailInput().waitUntilVisible();
        loginPage.registrationEmailInput().sendKeys(Constants.TestData.INVALID_EMAIL_FORMAT);
    }

    @Step
    public void enterValidEmailForRegistration() {
        loginPage.registrationEmailInput().waitUntilVisible();
        loginPage.registrationEmailInput().sendKeys(generateRandomEmail());
    }

    @Step
    public void enterValidPasswordForRegistration() {
        loginPage.registrationPasswordInput().waitUntilVisible();
        loginPage.registrationPasswordInput().sendKeys(Constants.TestData.REGISTRATION_PASSWORD);
    }

    @Step
    public void leavePasswordFieldEmptyForRegistration() {
        loginPage.registrationPasswordInput().waitUntilVisible();
        loginPage.registrationPasswordInput().sendKeys("");
    }

    @Step
    public void enterShortPasswordForRegistration() {
        loginPage.registrationPasswordInput().waitUntilVisible();
        loginPage.registrationPasswordInput().sendKeys(Constants.TestData.SHORT_PASSWORD);
    }

    @Step
    public void leaveEmailFieldEmptyForRegistration() {
        loginPage.registrationEmailInput().waitUntilVisible();
        loginPage.registrationEmailInput().sendKeys("");
    }

    @Step
    public void acceptRegistrationAgreement() {
        loginPage.agreementCheckboxForRegistration().waitUntilVisible();
        loginPage.agreementCheckboxForRegistration().click();
        waitABit(500);
    }

    @Step
    public void doNotAcceptRegistrationAgreement() {
        loginPage.agreementCheckboxForRegistration().waitUntilVisible();
        if (loginPage.agreementCheckboxForRegistration().isSelected()) {
            loginPage.agreementCheckboxForRegistration().click();
        }
        waitABit(500);
    }

    @Step
    public void clickRegistrationButton() {
        loginPage.registrationButton().waitUntilVisible();
        loginPage.registrationButton().click();
        waitABit(2000);
    }

    @Step
    public void verifyRegistrationSuccessMessageHeader() {
        SoftAssertions softAssertions = new SoftAssertions();
        loginPage.registrationSuccessMessageHeader().waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(loginPage.registrationSuccessMessageHeader().getText()).isEqualTo(Constants.RegistrationModal.REGISTRATION_SUCCESS_HEADER);
        softAssertions.assertAll();
    }

    @Step
    public void verifyRegistrationSuccessMessage() {
        SoftAssertions softAssertions = new SoftAssertions();
        loginPage.registrationSuccessMessage().waitUntilVisible();
        softAssertions.assertThat(loginPage.registrationSuccessMessage().getText()).isEqualTo(Constants.RegistrationModal.REGISTRATION_SUCCESS_MESSAGE);
        softAssertions.assertAll();
    }

    @Step
    public void closeRegistrationSuccessModal() {
        loginPage.closeRegistrationSuccessModalButton().waitUntilVisible();
        loginPage.closeRegistrationSuccessModalButton().click();
        waitABit(1000);
    }

    @Step
    public void verifyRegistrationModalIsClosed() {
        SoftAssertions softAssertions = new SoftAssertions();
        loginPage.registerModalHeader().waitUntilNotVisible(Duration.ofSeconds(5));
        softAssertions.assertThat(loginPage.registerModalHeader().isVisible()).isFalse();
        softAssertions.assertAll();
    }

    @Step
    public void verifyIncorrectEmailFormatErrorMessage() {
        SoftAssertions softAssertions = new SoftAssertions();
        loginPage.incorrectEmailFormatErrorMessage().waitUntilVisible(Duration.ofSeconds(5));
        softAssertions.assertThat(loginPage.incorrectEmailFormatErrorMessage().getText()).isEqualTo(Constants.RegistrationModal.INCORRECT_EMAIL_FORMAT_ERROR);
        softAssertions.assertAll();
    }

    @Step
    public void verifyEmptyPasswordErrorMessageForRegistration() {
        SoftAssertions softAssertions = new SoftAssertions();
        loginPage.emptyPasswordErrorMessage().waitUntilVisible(Duration.ofSeconds(5));
        softAssertions.assertThat(loginPage.emptyPasswordErrorMessage().getText()).isEqualTo(Constants.ValidationMessages.EMPTY_PASSWORD_ERROR);
        softAssertions.assertAll();
    }

    @Step
    public void verifyPasswordTooShortErrorMessageForRegistration() {
        SoftAssertions softAssertions = new SoftAssertions();
        loginPage.emptyPasswordErrorMessage().waitUntilVisible(Duration.ofSeconds(5));
        softAssertions.assertThat(loginPage.emptyPasswordErrorMessage().getText()).contains("za krótkie");
        softAssertions.assertAll();
    }

    @Step
    public void verifyAgreementCheckboxNotCheckedErrorMessage() {
        SoftAssertions softAssertions = new SoftAssertions();
        loginPage.agreementCheckboxNotCheckedErrorMessage().waitUntilVisible(Duration.ofSeconds(5));
        softAssertions.assertThat(loginPage.agreementCheckboxNotCheckedErrorMessage().getText()).isEqualTo(Constants.RegistrationModal.AGREEMENT_NOT_CHECKED_ERROR);
        softAssertions.assertAll();
    }

    @Step
    public void verifyRegistrationAgreementCheckboxLabelText() {
        SoftAssertions softAssertions = new SoftAssertions();
        loginPage.registrationAgreementCheckboxLabel().waitUntilVisible();
        softAssertions.assertThat(loginPage.registrationAgreementCheckboxLabel().getText()).isEqualTo(Constants.RegistrationModal.AGREEMENT_CHECKBOX_LABEL);
        softAssertions.assertAll();
    }

    @Step
    public void verifyRegistrationModalHeaderIsDisplayed() {
        SoftAssertions softAssertions = new SoftAssertions();
        loginPage.registerModalHeader().waitUntilVisible();
        softAssertions.assertThat(loginPage.registerModalHeader().getText()).isEqualTo(Constants.RegistrationModal.REGISTRATION_HEADER);
        softAssertions.assertAll();
    }

    @Step
    public void verifyRegistrationFirstNameInputIsDisplayed() {
        SoftAssertions softAssertions = new SoftAssertions();
        softAssertions.assertThat(loginPage.registrationFirstNameInput().isVisible()).isTrue();
        softAssertions.assertAll();
    }

    @Step
    public void verifyRegistrationLastNameInputIsDisplayed() {
        SoftAssertions softAssertions = new SoftAssertions();
        softAssertions.assertThat(loginPage.registrationLastNameInput().isVisible()).isTrue();
        softAssertions.assertAll();
    }

    @Step
    public void verifyRegistrationEmailInputIsDisplayed() {
        SoftAssertions softAssertions = new SoftAssertions();
        softAssertions.assertThat(loginPage.registrationEmailInput().isVisible()).isTrue();
        softAssertions.assertAll();
    }

    @Step
    public void verifyRegistrationPasswordInputIsDisplayed() {
        SoftAssertions softAssertions = new SoftAssertions();
        softAssertions.assertThat(loginPage.registrationPasswordInput().isVisible()).isTrue();
        softAssertions.assertAll();
    }

    @Step
    public void verifyRegistrationAgreementCheckboxIsDisplayed() {
        SoftAssertions softAssertions = new SoftAssertions();
        softAssertions.assertThat(loginPage.agreementCheckboxForRegistration().isVisible()).isTrue();
        softAssertions.assertAll();
    }

    @Step
    public void verifyRegistrationButtonIsDisplayed() {
        SoftAssertions softAssertions = new SoftAssertions();
        softAssertions.assertThat(loginPage.registrationButton().isVisible()).isTrue();
        softAssertions.assertAll();
    }

    @Step
    public void enterAlreadyRegisteredEmailForRegistration() {
        loginPage.registrationEmailInput().waitUntilVisible();
        loginPage.registrationEmailInput().sendKeys(Constants.TestData.EXISTING_EMAIL);
    }

    @Step
    public void leaveAllRegistrationFieldsEmpty() {
        loginPage.registrationFirstNameInput().waitUntilVisible();
        loginPage.registrationFirstNameInput().sendKeys("");
        loginPage.registrationLastNameInput().sendKeys("");
        loginPage.registrationEmailInput().sendKeys("");
        loginPage.registrationPasswordInput().sendKeys("");
        loginPage.agreementCheckboxForRegistration().click();
    }

    @Step
    public void enterVeryLongEmailForRegistration() {
        loginPage.registrationEmailInput().waitUntilVisible();
        loginPage.registrationEmailInput().sendKeys(Constants.TestData.VERY_LONG_EMAIL);
    }

    @Step
    public void enterVeryLongPasswordForRegistration() {
        loginPage.registrationPasswordInput().waitUntilVisible();
        loginPage.registrationPasswordInput().sendKeys(Constants.TestData.VERY_LONG_PASSWORD);
    }

    @Step
    public void enterFirstNameWithSpecialCharactersForRegistration() {
        loginPage.registrationFirstNameInput().waitUntilVisible();
        loginPage.registrationFirstNameInput().sendKeys(Constants.TestData.SPECIAL_CHARS_NAME);
    }

    @Step
    public void enterLastNameWithSpecialCharactersForRegistration() {
        loginPage.registrationLastNameInput().waitUntilVisible();
        loginPage.registrationLastNameInput().sendKeys(Constants.TestData.SPECIAL_CHARS_NAME);
    }

    @Step
    public void enterWhitespaceOnlyValuesInRegistrationFields() {
        loginPage.registrationFirstNameInput().waitUntilVisible();
        loginPage.registrationFirstNameInput().sendKeys(Constants.TestData.WHITESPACE_EMAIL);
        loginPage.registrationLastNameInput().sendKeys(Constants.TestData.WHITESPACE_EMAIL);
        loginPage.registrationEmailInput().sendKeys(Constants.TestData.WHITESPACE_EMAIL);
        loginPage.registrationPasswordInput().sendKeys(Constants.TestData.WHITESPACE_PASSWORD);
    }

    @Step
    public void enterNumericOnlyPasswordForRegistration() {
        loginPage.registrationPasswordInput().waitUntilVisible();
        loginPage.registrationPasswordInput().sendKeys(Constants.TestData.NUMERIC_ONLY_PASSWORD);
    }

    @Step
    public void enterLettersOnlyPasswordForRegistration() {
        loginPage.registrationPasswordInput().waitUntilVisible();
        loginPage.registrationPasswordInput().sendKeys(Constants.TestData.LETTERS_ONLY_PASSWORD);
    }

    @Step
    public void enterEmailWithPlusSignForRegistration() {
        loginPage.registrationEmailInput().waitUntilVisible();
        loginPage.registrationEmailInput().sendKeys(Constants.TestData.EMAIL_WITH_PLUS);
    }

    @Step
    public void closeRegistrationModal() {
        loginPage.getCloseModalButton().click();
        waitABit(1000);
    }


    @Step
    public void verifyMultipleFieldValidationErrorsDisplayed() {
        SoftAssertions softAssertions = new SoftAssertions();
        loginPage.incorrectEmailFormatErrorMessage().waitUntilVisible(Duration.ofSeconds(3));
        softAssertions.assertThat(loginPage.incorrectEmailFormatErrorMessage().getText()).isEqualTo(Constants.RegistrationModal.INCORRECT_EMAIL_FORMAT_ERROR);
        //TODO: add verification for other fields
        softAssertions.assertAll();
    }

    @Step
    public void verifyAppropriateValidationErrorMessagesForRegistration() {
        SoftAssertions softAssertions = new SoftAssertions();
        loginPage.incorrectEmailFormatErrorMessage().waitUntilVisible(Duration.ofSeconds(5));
        softAssertions.assertThat(loginPage.incorrectEmailFormatErrorMessage().getText()).isEqualTo(Constants.RegistrationModal.INCORRECT_EMAIL_FORMAT_ERROR);
        softAssertions.assertAll();
    }

    @Step
    public void verifyRegistrationAgreementCheckboxIsUncheckedByDefault() {
        SoftAssertions softAssertions = new SoftAssertions();
        loginPage.agreementCheckboxForRegistration().waitUntilVisible();
        softAssertions.assertThat(loginPage.agreementCheckboxForRegistration().isVisible()).isTrue();
        softAssertions.assertAll();
    }

    @Step
    public void clickOnRegistrationAgreementCheckbox() {
        loginPage.agreementCheckboxForRegistration().waitUntilVisible();
        loginPage.agreementCheckboxForRegistration().click();
        waitABit(500);
    }

    @Step
    public void verifyRegistrationAgreementCheckboxIsChecked() {
        SoftAssertions softAssertions = new SoftAssertions();
        softAssertions.assertThat(loginPage.agreementCheckboxForRegistration().isVisible()).isTrue();
        softAssertions.assertAll();
    }

    @Step
    public void verifyRegistrationAgreementCheckboxIsUnchecked() {
        SoftAssertions softAssertions = new SoftAssertions();
        softAssertions.assertThat(loginPage.agreementCheckboxForRegistration().isVisible()).isTrue();
        softAssertions.assertAll();
    }

    @Step
    public void verifyPasswordValidationErrorMessage() {
        SoftAssertions softAssertions = new SoftAssertions();
        loginPage.emptyPasswordErrorMessage().waitUntilVisible(Duration.ofSeconds(5));
        softAssertions.assertThat(loginPage.emptyPasswordErrorMessage().isVisible()).isTrue();
        softAssertions.assertAll();
    }

    @Step
    public void verifyCloseRegistrationSuccessModalButtonIsDisplayed() {
        SoftAssertions softAssertions = new SoftAssertions();
        softAssertions.assertThat(loginPage.closeRegistrationSuccessModalButton().isVisible()).isTrue();
        softAssertions.assertAll();
    }


    @Step
    public void navigateThroughRegistrationFormUsingTabKey() {
        SoftAssertions softAssertions = new SoftAssertions();
        loginPage.registrationFirstNameInput().waitUntilVisible();
        loginPage.registrationFirstNameInput().click();
        softAssertions.assertThat(loginPage.registrationFirstNameInput().isEnabled()).isTrue();
        getDriver().findElement(By.xpath("//input[@name='firstName']")).sendKeys(Keys.TAB);
        waitABit(500);
        softAssertions.assertThat(loginPage.registrationLastNameInput().isEnabled()).isTrue();
        softAssertions.assertAll();
    }

    @Step
    public void verifyAllRegistrationFormElementsAccessibleViaKeyboard() {
        SoftAssertions softAssertions = new SoftAssertions();
        softAssertions.assertThat(loginPage.registrationFirstNameInput().isEnabled()).isTrue();
        softAssertions.assertThat(loginPage.registrationLastNameInput().isEnabled()).isTrue();
        softAssertions.assertThat(loginPage.registrationEmailInput().isEnabled()).isTrue();
        softAssertions.assertThat(loginPage.registrationPasswordInput().isEnabled()).isTrue();
        softAssertions.assertAll();
    }

    @Step
    public void verifyUserCanSubmitRegistrationFormUsingEnterKey() {
        loginPage.registrationEmailInput().sendKeys(generateRandomEmail());
        loginPage.registrationPasswordInput().sendKeys(Constants.TestData.REGISTRATION_PASSWORD);
        acceptRegistrationAgreement();
        loginPage.registrationButton().waitUntilVisible();
        loginPage.registrationButton().click();
    }

    @Step
    public void verifyFirstRegistrationInputFieldHasFocus() {
        SoftAssertions softAssertions = new SoftAssertions();
        loginPage.registrationFirstNameInput().waitUntilVisible();
        loginPage.registrationFirstNameInput().click();
        softAssertions.assertThat(loginPage.registrationFirstNameInput().isEnabled()).isTrue();
        softAssertions.assertAll();
    }

    @Step
    public void enterDataInFirstNameFieldAndTab() {
        loginPage.registrationFirstNameInput().sendKeys("Test");
        getDriver().findElement(By.xpath("//input[@name='firstName']")).sendKeys(Keys.TAB);
        waitABit(500);
    }

    @Step
    public void verifyFocusMovesToLastNameField() {
        SoftAssertions softAssertions = new SoftAssertions();
        softAssertions.assertThat(loginPage.registrationLastNameInput().isEnabled()).isTrue();
        softAssertions.assertAll();
    }

    @Step
    public void enterDataInLastNameFieldAndTab() {
        loginPage.registrationLastNameInput().sendKeys("User");
        getDriver().findElement(By.xpath("//input[@name='lastName']")).sendKeys(Keys.TAB);
        waitABit(500);
    }

    @Step
    public void verifyFocusMovesToEmailField() {
        SoftAssertions softAssertions = new SoftAssertions();
        softAssertions.assertThat(loginPage.registrationEmailInput().isEnabled()).isTrue();
        softAssertions.assertAll();
    }

    private String generateRandomEmail() {
        Random random = new Random();
        int randomNumber = random.nextInt(10000);
        return "testuser" + randomNumber + "@patio.test.dajar.pl";
    }

    public void verifyDoYouHaveAnAccountTextIsDisplayed() {
        SoftAssertions softAssertions = new SoftAssertions();
        loginPage.doYouHaveAnAccountTextOnRegisterModal().waitUntilVisible();
        softAssertions.assertThat(loginPage.doYouHaveAnAccountTextOnRegisterModal().getText()).isEqualTo(Constants.LoginModal.DO_YOU_HAVE_AN_ACCOUNT_TEXT);
        softAssertions.assertThat(loginPage.loginLinkOnRegisterModal().isVisible()).isTrue();
        softAssertions.assertAll();
    }
}
