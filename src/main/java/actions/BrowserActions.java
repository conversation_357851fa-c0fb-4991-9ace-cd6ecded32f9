package actions;


import net.serenitybdd.core.Serenity;
import net.serenitybdd.core.steps.UIInteractionSteps;
import org.awaitility.Awaitility;
import org.openqa.selenium.WebDriver;
import pages.BrowserPage;
import utils.BrowserUtils;
import utils.serenity.SerenityConfigReader;

import java.time.Duration;

public class BrowserActions extends UIInteractionSteps {

    public void refreshPage() {
        Serenity.getDriver().navigate().refresh();
        waitUntilCurrentBrowserTabLoadingFinished();
    }

    public enum SiteConfig {;


        private final String value;

        SiteConfig(String value) {
            this.value = value;
        }

        public String getSite() {
            return SerenityConfigReader.getProperty(this.value);
        }
    }

    private BrowserPage browserPage;

    public void openSite(SiteConfig siteConfig) {
        WebDriver driver = Serenity.getDriver();
        driver.get(siteConfig.getSite());
    }

    public void closeCurrentBrowserAndLaunchNewBrowserWithSite(SiteConfig siteConfig) {
        WebDriver driver = Serenity.getDriver();
        driver.close();
        driver = Serenity.getWebdriverManager().getWebdriver();
        driver.get(siteConfig.getSite());
    }

    public void openURLInCurrentTab(String url) {
        Serenity.getDriver().navigate().to(url);
    }

    public void clickOnBrowserNavigationButton(String buttonType) {
        BrowserUtils.clickOnBrowserNavigationButton(buttonType);
        waitABit(2000); // wait for page reloaded
    }

    public void switchToTheBrowserTab(String tabIndex) {
        BrowserUtils.changeTab(Integer.parseInt(tabIndex));
        waitUntilCurrentBrowserTabLoadingFinished();
    }

    public void duplicateTheCurrentBrowserTab() {
        BrowserUtils.openNewTab(Serenity.getDriver().getCurrentUrl());
        waitUntilCurrentBrowserTabLoadingFinished();
    }

    public void waitUntilCurrentBrowserTabLoadingFinished() {
        Awaitility.await()
                .atMost(Duration.ofMinutes(5))
                .pollInterval(Duration.ofMillis(100))
                .until(() -> browserPage.getBody().isPresent());
    }

}
