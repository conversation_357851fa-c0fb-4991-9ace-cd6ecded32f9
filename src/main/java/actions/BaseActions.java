package actions;

import lombok.val;
import net.serenitybdd.core.steps.UIInteractionSteps;
import pages.BasePage;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.Duration;

public class BaseActions extends UIInteractionSteps {
    private BasePage basePage;

    public void selectMainCategory(String categoryName) {
        basePage.getMainCategoryItem(categoryName).waitUntilVisible(Duration.ofSeconds(10));
        basePage.getMainCategoryItem(categoryName).click();
    }
    public void selectSubCategory(String subCategory) {
        basePage.getSubCategoryItem(subCategory).waitUntilVisible(Duration.ofSeconds(10));
        basePage.getSubCategoryItem(subCategory).click();
    }

    public void navigateBack() {
        getDriver().navigate().back();
        waitABit(2000);
    }
    public void verifyUserIsOnPaymentAndDeliveryPage() {
        val softAssertions = new org.assertj.core.api.SoftAssertions();
        try {
            waitABit(3000); // Give page time to load
            String currentUrl = getDriver().getCurrentUrl();
            softAssertions.assertThat(currentUrl).contains("/pl/PL/pg/dostawa-i-platnosc");
            try {
                basePage.pageTitle().waitUntilVisible(Duration.ofSeconds(10));
                String actualTitle = basePage.pageTitle().getText();
                softAssertions.assertThat(actualTitle).containsIgnoringCase("płatność");
            } catch (Exception e) {
                if (!currentUrl.contains("/pl/PL/pg/dostawa-i-platnosc")) {
                    softAssertions.fail("Could not verify page title and URL doesn't match expected path. Current URL: " + currentUrl);
                }
                // If URL is correct, proceed with content verification
            }

        } catch (Exception e) {
            softAssertions.fail("Failed to verify payment and delivery page: " + e.getMessage());
        }
        softAssertions.assertAll();
    }

    public void verifyPaymentAndDeliveryPageContentMatchesExpectedText() {
        val softAssertions = new org.assertj.core.api.SoftAssertions();
        try {
            waitABit(5000);
            String expectedContent = readExpectedContentFromFile();
            String actualContent = "";

            try {
                basePage.paymentAndDeliveryDetailsContainer().waitUntilVisible(Duration.ofSeconds(15));
                actualContent = basePage.paymentAndDeliveryDetailsContainer().getText();
            } catch (Exception e) {
                try {
                    actualContent = getDriver().getPageSource();
                } catch (Exception ex) {
                    softAssertions.fail("Could not retrieve page content: " + ex.getMessage());
                    softAssertions.assertAll();
                    return;
                }
            }
            String normalizedActual = normalizeContent(actualContent);
            String[] expectedLines = expectedContent.split("\n");
            int matchedLines = 0;
            int totalSignificantLines = 0;
            for (String line : expectedLines) {
                String trimmedLine = line.trim();
                if (trimmedLine.length() > 5 && !trimmedLine.matches("^\\s*$")) {
                    totalSignificantLines++;
                    String normalizedLine = normalizeContent(trimmedLine);

                    if (normalizedActual.contains(normalizedLine)) {
                        matchedLines++;
                    } else {
                        if (trimmedLine.length() > 20) {
                            String[] words = trimmedLine.split("\\s+");
                            if (words.length > 3) {
                                int wordMatches = 0;
                                for (String word : words) {
                                    if (word.length() > 3 && normalizedActual.contains(normalizeContent(word))) {
                                        wordMatches++;
                                    }
                                }
                                if (wordMatches >= words.length / 2) {
                                    matchedLines++;
                                }
                            }
                        }
                    }
                }
            }
            double matchPercentage = totalSignificantLines > 0 ? (double) matchedLines / totalSignificantLines * 100 : 0;
            softAssertions.assertThat(matchPercentage).isGreaterThanOrEqualTo(60.0);
            //verify URL is correct
            String currentUrl = getDriver().getCurrentUrl();
            softAssertions.assertThat(currentUrl).contains("/pl/PL/pg/dostawa-i-platnosc");
        } catch (Exception e) {
            softAssertions.fail("Failed to verify payment and delivery page content: " + e.getMessage());
        }

        softAssertions.assertAll();
    }

    private String readExpectedContentFromFile() throws IOException {
        String filePath = System.getProperty("user.dir") + "/src/test/resources/files/payment_and_delivery";
        return new String(Files.readAllBytes(Paths.get(filePath)));
    }

    private String normalizeContent(String content) {
        if (content == null) {
            return "";
        }
        return content.toLowerCase()
                .replaceAll("\\s+", " ")
                .replaceAll("[^a-zA-Z0-9\\s,.]", "")
                .trim();
    }
}
