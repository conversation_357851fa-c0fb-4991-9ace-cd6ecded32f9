package actions;

import common.constants.IStorageKey;
import net.serenitybdd.core.Serenity;
import net.serenitybdd.core.pages.WebElementFacade;
import net.serenitybdd.core.steps.UIInteractionSteps;
import org.assertj.core.api.SoftAssertions;
import pages.CategoryPage;
import pages.ProductPage;
import pages.SearchPage;
import utils.storage.Storage;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

public class SearchActions extends UIInteractionSteps {
    private SearchPage searchPage;
    private CategoryPage categoryPage;
    private ProductPage productPage;
    private ProductListPageActions productListPage;


    public void searchForProduct(String searchTerm) {
        searchPage.searchInput().waitUntilVisible();
        searchPage.searchInput().typeAndEnter(searchTerm);
        //wait a bit for the page to load
        waitABit(3000);
        //close the search bar dropdown if it's open
        if (searchPage.searchDropdownContainer().isVisible()) {
            searchPage.closeSearchBarButton().click();
        }
        searchPage.searchResultHeader().waitUntilVisible(Duration.ofMinutes(1));
        //save the search term to storage
        Storage.getStorage().saveValue(IStorageKey.SEARCH_TERM, String.valueOf(searchTerm));
    }

    public void verifySearchResultsPageIsDisplayed() {
        SoftAssertions softAssertions = new SoftAssertions();
        String searchTerm = Storage.getStorage().getValue(IStorageKey.SEARCH_TERM);
        searchPage.searchResultHeader().waitUntilVisible();
        //verify the header contains the search term
        softAssertions.assertThat(searchPage.searchResultHeader().getText()).contains(searchTerm);
        softAssertions.assertThat(searchPage.searchResultHeader().isVisible()).isTrue();
        softAssertions.assertAll();
    }

    public void verifySearchResultsContain(String searchTerm) {
        SoftAssertions softAssertions = new SoftAssertions();
        List<String> productNames = searchPage.productName().getTexts();
        // Filter out empty product names
        List<String> validProductNames = productNames.stream()
                .filter(name -> name != null && !name.trim().isEmpty())
                .toList();

        if (validProductNames.isEmpty()) {
            throw new AssertionError("No valid product names found in search results");
        }

        // Convert search term to lowercase for case-insensitive comparison
        String searchTermLower = searchTerm.toLowerCase().trim();

        // Check each product name contains the search term (case-insensitive)
        for (String productName : validProductNames) {
            String productNameLower = productName.toLowerCase().trim();

            softAssertions.assertThat(productNameLower)
                    .as("Product name '" + productName + "' should contain search term '" + searchTerm + "'")
                    .contains(searchTermLower);
        }
        softAssertions.assertAll();
    }

    public void clickOnSearchBar() {
        searchPage.searchInput().waitUntilVisible(Duration.ofMinutes(1));
        searchPage.searchInput().click();
    }

    public void verifyTheSearchBarDropdownIsDisplayed() {
        SoftAssertions softAssertions = new SoftAssertions();
        searchPage.searchDropdownContainer().waitUntilVisible(Duration.ofMinutes(1));
        searchPage.searchDropdownContainer().isDisplayed();
        searchPage.searchDropdownBestsellerListContainer().isDisplayed();
        var categoryList = searchPage.searchDropdownCategoryList().getTexts();
        for (String s : categoryList) {
            softAssertions.assertThat(s).isNotBlank();
        }
        var recommendedList = searchPage.searchDropdownRecommendedList().getTexts();
        for (String s : recommendedList) {
            softAssertions.assertThat(s).isNotBlank();
        }
        searchPage.searchDropdownInspirationsListContainer().isDisplayed();
        softAssertions.assertAll();
    }

    public void verifyUserIsAbleToCloseTheSearchBarDropdown() {
        searchPage.closeSearchBarButton().waitUntilVisible(Duration.ofMinutes(1));
        searchPage.closeSearchBarButton().click();
        searchPage.searchDropdownContainer().waitUntilNotVisible(Duration.ofMinutes(1));
        assertFalse("Search dropdown is still visible after clicking close button!", searchPage.searchDropdownContainer().isVisible());
    }

    public void verifyThatAllCategoryLinksInTheSearchDropdownOpenCorrectPages() {
        SoftAssertions softAssertions = new SoftAssertions();

        // Store category texts first to avoid repeated DOM queries
        List<String> categoryTexts = getCategoryTextsFromDropdown();

        if (categoryTexts.isEmpty()) {
            throw new RuntimeException("No category items found in search dropdown");
        }

        for (int i = 0; i < categoryTexts.size(); i++) {
            String expectedText = categoryTexts.get(i);

            try {
                // Open dropdown and click the category item
                clickCategoryItemByIndex(i);

                // Verify the category page header
                categoryPage.categoryPageHeader().waitUntilVisible(Duration.ofMinutes(1));
                String actualHeader = categoryPage.categoryPageHeader().getText().trim();

                softAssertions.assertThat(actualHeader)
                        .as("Category page header for item: " + expectedText)
                        .isEqualToIgnoringCase(expectedText);

            } catch (Exception e) {
                softAssertions.fail("Failed to verify category item '" + expectedText + "' at index " + i + ": " + e.getMessage());
            }
            // Navigate back to prepare for the next iteration
            navigateBackToSearchPage();
        }

        softAssertions.assertAll();
    }

    private List<String> getCategoryTextsFromDropdown() {
        // Open dropdown to get category texts
        searchPage.searchInput().click();
        waitABit(1000); // Small wait for dropdown to appear

        // Get category items texts
        List<WebElementFacade> categoryItems = searchPage.searchDropdownCategoryList().getAllElements();
        List<String> categoryTexts = new ArrayList<>(categoryItems.stream()
                .map(item -> item.getText().trim())
                .filter(text -> !text.isEmpty())
                .toList());

        // Get recommended items texts
        List<WebElementFacade> recommendedItems = searchPage.searchDropdownRecommendedList().getAllElements();
        categoryTexts.addAll(recommendedItems.stream()
                .map(item -> item.getText().trim())
                .filter(text -> !text.isEmpty())
                .toList());

        closeDropdown();

        return categoryTexts;
    }

    private void clickCategoryItemByIndex(int index) {
        // Open dropdown
        searchPage.searchInput().click();
        waitABit(3000); // Wait for the dropdown to appear

        // Get fresh elements to avoid stale element exception
        List<WebElementFacade> categoryItems = searchPage.searchDropdownCategoryList().getAllElements();
        List<WebElementFacade> recommendedItems = searchPage.searchDropdownRecommendedList().getAllElements();

        List<WebElementFacade> allItems = new ArrayList<>();
        allItems.addAll(categoryItems);
        allItems.addAll(recommendedItems);

        if (index >= allItems.size()) {
            throw new RuntimeException("Index " + index + " is out of bounds. Total items: " + allItems.size());
        }

        WebElementFacade targetItem = allItems.get(index);
        targetItem.click();
    }

    private void closeDropdown() {
        searchPage.closeQuickSearchDropdownButton().click();
    }

    private void navigateBackToSearchPage() {
        try {
            Serenity.getDriver().navigate().back();
            searchPage.searchInput().waitUntilVisible(Duration.ofSeconds(10));
            waitABit(1000); // Additional wait for page stability
        } catch (Exception e) {
            // If back navigation fails, try refreshing or navigating home
            Serenity.getDriver().navigate().refresh();
            searchPage.searchInput().waitUntilVisible(Duration.ofSeconds(10));
        }
    }

    public void openTheFirstProduct() {
        searchPage.productName().waitUntilVisible(Duration.ofMinutes(1));
        productListPage.openProductPageRandomly();
        productPage.productName().waitUntilVisible(Duration.ofMinutes(1));
    }

    public void openTheSecondProduct() {
        searchPage.productName().waitUntilVisible(Duration.ofMinutes(1));
        productListPage.openProductPageRandomly();
        productPage.productName().waitUntilVisible(Duration.ofMinutes(1));
    }

    public void verifyNoResultsFoundHeader(String searchTerm) {
        SoftAssertions softAssertions = new SoftAssertions();
        searchPage.searchResultHeader().waitUntilVisible(Duration.ofMinutes(1));
        String noResultsHeaderText = searchPage.searchResultHeader().getText().trim();
        softAssertions.assertThat(noResultsHeaderText).isEqualTo("Wyniki wyszukiwania dla \"" + searchTerm + "\" (Nie znaleziono produktów)");
        //verify sort button is not displayed
        softAssertions.assertThat(searchPage.sortButton().isVisible()).isFalse();
        softAssertions.assertAll();
    }

    public void verifyNumberOfProductsHeaderIsDisplayed() {
        searchPage.numberOfProductsHeader().waitUntilVisible(Duration.ofMinutes(1));
        String numberOfProductsText = searchPage.numberOfProductsHeader().getText().trim();
        assertTrue("Number of products header is not displayed!", numberOfProductsText.contains("Liczba produktów"));
    }

    public void verifyNumberOfResultsMatchesCountShown() {
        SoftAssertions softAssertions = new SoftAssertions();
        searchPage.numberOfProductsHeader().waitUntilVisible(Duration.ofMinutes(1));
        String numberOfProductsText = searchPage.numberOfProductsHeader().getText().trim();

        // Extract the number from the header text
        String numberPart = numberOfProductsText.replaceAll("[^0-9]", "");
        int expectedCount = Integer.parseInt(numberPart);

        // Get the actual count of products displayed
        List<WebElementFacade> productElements = searchPage.productName().getAllElements();
        int actualCount = productElements.size();

        softAssertions.assertThat(actualCount).isEqualTo(expectedCount);

        softAssertions.assertAll();
    }

    public void verifyProductTitlesAndPricesAreVisibleOnResultsPage() {
        SoftAssertions softAssertions = new SoftAssertions();
        searchPage.productName().waitUntilVisible(Duration.ofMinutes(1));
        List<WebElementFacade> productNames = searchPage.productName().getAllElements();

        // Check if product names are displayed
        for (WebElementFacade productName : productNames) {
            softAssertions.assertThat(productName.isDisplayed()).isTrue();
        }
        // Check if prices are displayed
        List<WebElementFacade> productPrices = searchPage.productPriceOnSearchPage().getAllElements();
        for (WebElementFacade productPrice : productPrices) {
            softAssertions.assertThat(productPrice.isDisplayed()).isTrue();
        }

        softAssertions.assertAll();
    }

    public void verifySearchBoxIsVisible() {
        searchPage.searchInput().waitUntilVisible(Duration.ofMinutes(1));
        WebElementFacade searchInput = searchPage.searchInput().getWrappedElement();
        assertTrue("Search input is not visible!", searchInput.isVisible());
        assertTrue("Search input is enabled!", searchInput.isEnabled());
        assertTrue("Search input has no placeholder text!", Objects.requireNonNull(searchInput.getAttribute("placeholder")).contains("Szukaj"));
    }

    public void verifySearchResultsPagePersistsInDifferent(String action) {
        SoftAssertions softAssertions = new SoftAssertions();
        searchPage.searchResultHeader().waitUntilVisible(Duration.ofMinutes(1));
        String searchTerm = Storage.getStorage().getValue(IStorageKey.SEARCH_TERM);
        switch (action.toLowerCase()) {
            case "refresh page" -> {
                Serenity.getDriver().navigate().refresh();
                searchPage.searchResultHeader().waitUntilVisible(Duration.ofMinutes(1));
                softAssertions.assertThat(searchPage.searchResultHeader().getText()).contains(searchTerm);
            }
            case "open product and return to search results page" -> {
                productListPage.openFirstProduct();
                productPage.productName().waitUntilVisible(Duration.ofMinutes(1));
                softAssertions.assertThat(productPage.productName().getText()).contains(searchTerm);
                Serenity.getDriver().navigate().back();
                searchPage.searchResultHeader().waitUntilVisible(Duration.ofMinutes(1));
                softAssertions.assertThat(searchPage.searchResultHeader().getText()).contains(searchTerm);
            }
            default -> throw new IllegalArgumentException("Unknown action: " + action);
        }
        softAssertions.assertAll();
    }

    public void typeInSearchBar(String searchTerm) {
        searchPage.searchInput().waitUntilVisible(Duration.ofMinutes(1));
        searchPage.searchInput().type(searchTerm);
        //Save the search term to storage
        Storage.getStorage().saveValue(IStorageKey.SEARCH_TERM, searchTerm);
    }

    public void clickOnSearchIcon() {
        searchPage.searchIcon().waitUntilVisible(Duration.ofMinutes(1));
        searchPage.searchIcon().click();
        searchPage.searchResultHeader().waitUntilVisible(Duration.ofMinutes(1));
    }

    private void typeAndEnterInSearchBar(String searchTerm) {
        searchPage.searchInput().waitUntilVisible(Duration.ofMinutes(1));
        searchPage.searchInput().typeAndEnter(searchTerm);
        //wait a bit for the page to load
        waitABit(3000);
    }

    public void searchForProductByCode(String productCode) {
        searchPage.searchInput().waitUntilVisible(Duration.ofSeconds(10));
        typeAndEnterInSearchBar(productCode);
        productPage.addToCartButton().waitUntilVisible(Duration.ofSeconds(10));
    }
}
