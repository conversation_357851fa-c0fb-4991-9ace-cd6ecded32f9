package actions;

import net.serenitybdd.core.Serenity;
import net.serenitybdd.core.steps.UIInteractionSteps;
import org.openqa.selenium.WebDriver;
import utils.serenity.SerenityConfigReader;

public class OpenApplication extends UIInteractionSteps {
    String url = SerenityConfigReader.getProperty("patio.ui.base.url");
    WebDriver driver = Serenity.getDriver();

    public void openApplication() {
        driver.get(url);
    }
}
