package actions;

import net.serenitybdd.core.Serenity;
import net.serenitybdd.core.steps.UIInteractionSteps;
import pages.CategoryPage;
import pages.ProductPage;
import pages.SearchPage;

import java.time.Duration;

import static common.constants.Constants.Buttons.SEE_MORE_PRODUCTS;

public class CategoryPageActions extends UIInteractionSteps {
    private SearchPage searchPage;
    private ProductListPageActions productListPage;
    private ProductPage productPage;
    private BaseActions baseActions;
    private CategoryPage categoryPage;

    public void openProductFromCategoryPage() {
        searchPage.productName().waitUntilVisible(Duration.ofMinutes(1));
        productListPage.openProductPageRandomly();
        productPage.productName().waitUntilVisible(Duration.ofMinutes(1));
    }

    public void navigateToShowAllInCategory(String mainCategory) {
        baseActions.selectMainCategory(mainCategory);
        baseActions.selectSubCategory(SEE_MORE_PRODUCTS);
        waitABit(3000); // Wait for page to load
    }

    public void loadAllProductsInCategory() {
        while (categoryPage.seeMoreProductsButton().isVisible()) {
            categoryPage.seeMoreProductsButton().click();
            waitABit(3000); // Wait for more products to load
        }
        //for better ui navigation, scroll sort button into view
        searchPage.sortButton().scrollElementToCenter();
    }
}
