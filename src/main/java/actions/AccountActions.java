package actions;

import common.constants.Constants;
import common.constants.IStorageKey;
import common.test_data.AccountPageTestData;
import helpers.AccountPageHelper;
import net.serenitybdd.core.steps.UIInteractionSteps;
import org.assertj.core.api.SoftAssertions;
import pages.AccountPage;
import pages.BasePage;
import pages.CheckoutPage;
import utils.storage.Storage;
import net.serenitybdd.annotations.Steps;

import java.time.Duration;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

public class AccountActions extends UIInteractionSteps {

    @Steps
    private AccountPageHelper accountPageHelper;
    @Steps
    private BasePage basePage;
    @Steps
    private LoginActions loginActions;
    @Steps
    private CartActions cartActions;
    @Steps
    private ProductPageActions productPageActions;
    @Steps
    private SearchActions searchActions;
    @Steps
    private AccountPage accountPage;
    @Steps
    private CheckoutPage checkoutPage;

    public void navigateToOrderHistory() {
        accountPage.orderHistoryLink().click();
        accountPage.orderDetailsButton().waitUntilVisible(Duration.ofSeconds(10));
    }

    public void verifyTheRecentOrderIsDisplayedInOrderHistory() {
        //click the first order details button by index 0
        accountPage.orderDetailsButton().clickFirst();
        accountPage.orderNumberHeader().waitUntilVisible(Duration.ofSeconds(10));
    }

    public void verifyOrderDetailsMatchPlacedOrder() {
        SoftAssertions softAssertions = new SoftAssertions();

        //Verify order number
        String orderNumberFromSuccessPage = Storage.getStorage().getValue(IStorageKey.ORDER_NUMBER);
        String orderNumberOnAccountPage = accountPage.orderNumberHeader().getText().replaceAll("[^0-9]", "");
        softAssertions.assertThat(orderNumberOnAccountPage).contains(orderNumberFromSuccessPage.substring(orderNumberFromSuccessPage.length() - 4));

        //Verify product name
        String productName = Storage.getStorage().getValue(IStorageKey.PRODUCT_NAME_FOR_ORDER);
        softAssertions.assertThat(accountPage.orderedProductName().getText()).contains(productName);

        //Verify product price
        String productPrice = Storage.getStorage().getValue(IStorageKey.PRODUCT_PRICE_FOR_ORDER);
        softAssertions.assertThat(accountPage.orderedProductPrice().getText()).isEqualTo(productPrice);

        //Verify order date with tolerance for time zone differences
        LocalDate today = LocalDate.now();
        LocalDate yesterday = today.minusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd.MM.yyyy");
        String todayFormatted = today.format(formatter);
        String yesterdayFormatted = yesterday.format(formatter);

        String actualOrderDate = accountPage.orderPlacementDateValue().getText();
        softAssertions.assertThat(actualOrderDate).as("Order date should be either today or yesterday due to possible time zone differences").isIn(todayFormatted, yesterdayFormatted);

        //Verify payment method
        String paymentMethod = Storage.getStorage().getValue(IStorageKey.PAYMENT_METHOD);
        if (paymentMethod.equals("Blik")) {
            softAssertions.assertThat(accountPage.paymentMethodValue().getText()).contains(Constants.PaymentMethods.BLIK_WITH_CODE);
        } else {
            softAssertions.assertThat(accountPage.paymentMethodValue().getText()).contains(paymentMethod);
        }

        //Verify order status
        softAssertions.assertThat(accountPage.orderStatusValue().getText()).isEqualTo(Constants.AccountPage.ORDER_STATUS_ACCEPTED);

        // Verify shipping status
        softAssertions.assertThat(accountPage.shippingStatusValue().getText()).isEqualTo(Constants.AccountPage.SHIPPING_STATUS_WAITING);

        //Verify the shipping address if available
        String shippingAddress = Storage.getStorage().getValue(IStorageKey.SELECTED_SHIPPING_ADDRESS);
        if (shippingAddress != null) {
            softAssertions.assertThat(accountPage.deliveryAddressValue().getText()).isEqualTo(shippingAddress);
        } else {
            // If the shipping address is not stored, just verify it's not empty
            softAssertions.assertThat(accountPage.deliveryAddressValue().getText()).isNotEmpty();
            // Store it for future reference
            Storage.getStorage().saveValue(IStorageKey.SELECTED_SHIPPING_ADDRESS, accountPage.deliveryAddressValue().getText());
        }

        //Verify billing address if available
        String billingAddress = Storage.getStorage().getValue(IStorageKey.SELECTED_BILLING_ADDRESS);
        if (billingAddress != null) {
            // Get the actual billing address text from the page
            String actualBillingAddress = accountPage.billingAddressValue().getText();

            // Normalize NIP format: replace "NIP: " with "NIP " or vice versa to ensure consistent comparison
            if (billingAddress.contains("NIP ") && actualBillingAddress.contains("NIP: ")) {
                billingAddress = billingAddress.replace("NIP ", "NIP: ");
            } else if (billingAddress.contains("NIP: ") && actualBillingAddress.contains("NIP ")) {
                billingAddress = billingAddress.replace("NIP: ", "NIP ");
            }

            softAssertions.assertThat(actualBillingAddress).isEqualTo(billingAddress);
        } else {
            //If the billing address is not stored, just verify it's not empty
            softAssertions.assertThat(accountPage.billingAddressValue().getText()).isNotEmpty();
            //Store it for future reference
            Storage.getStorage().saveValue(IStorageKey.SELECTED_BILLING_ADDRESS, accountPage.billingAddressValue().getText());
        }

        softAssertions.assertAll();
    }

    public void verifyUserIsLoggedOut() {
        SoftAssertions softAssertions = new SoftAssertions();
        softAssertions.assertThat(accountPage.accountPageHeader().isVisible()).isFalse();
        softAssertions.assertAll();
    }

    // ========== TICKET CREATION METHODS ==========

    public void clickOnContactWithTheStoreLink() {
        accountPageHelper.clickContactWithStoreLink();
    }

    public void clickOnCreateATicketLink() {
        accountPageHelper.clickCreateTicketLink();
    }

    public void clickOnReasonForContactDropdown() {
        accountPage.reasonForContactDropdownSelect().waitUntilVisible(Duration.ofSeconds(10));
        accountPage.reasonForContactDropdownSelect().click();
    }

    public void selectRandomReasonForContact() {
        accountPageHelper.selectRandomReasonForContact();
    }

    public void clickOnOrderToCreateATicketDropdown() {
        accountPage.orderToCreateATicketDropdownSelect().waitUntilVisible(Duration.ofSeconds(10));
        accountPage.orderToCreateATicketDropdownSelect().click();
    }

    public void verifyFirstOrderMatchesRecentlyCreatedOrder() {
        accountPageHelper.verifyFirstOrderMatchesRecentOrder();
    }

    public void selectFirstOrderFromDropdown() {
        accountPageHelper.selectFirstOrderForTicket();
    }

    public void enterTicketMessage(String message) {
        accountPageHelper.enterTicketMessage(message);
    }

    public void clickOnSubmitRequestButton() {
        accountPageHelper.submitTicketRequest();
    }

    public void verifyRequestSubmittedText(String expectedText) {
        accountPageHelper.verifyRequestSubmittedText(expectedText);
    }

    public void verifyTicketCreatedDateIsCurrentDate() {
        accountPageHelper.verifyTicketCreatedDateIsCurrent();
    }

    public void verifyTicketHeaderContainsOrderInformation() {
        accountPageHelper.verifyTicketHeaderContainsOrderInfo();
    }

    public void verifyTicketLastMessageHeader(String expectedHeader) {
        accountPageHelper.verifyTicketLastMessageHeader(expectedHeader);
    }

    public void verifyTicketLastMessage(String expectedMessage) {
        accountPageHelper.verifyTicketLastMessage(expectedMessage);
    }

    public void logout() {
        loginActions.logout();
    }

    public void loginWithAlternativeCredentials() {
        if (isUserLoggedIn()) {
            loginActions.logout();
        }
        loginActions.navigateToLoginPage();
        loginActions.loginWithCredentials(common.test_data.UserCredentials.alternativeCredentials());
    }

    private boolean isUserLoggedIn() {
        // Check if the account page header or user account icon is visible
        try {
            return accountPage.accountPageHeader().isVisible();
        } catch (Exception e) {
            return false;
        }
    }

    public void verifyUserIsLoggedIn() {
        loginActions.verifyLoginSuccessful();
    }

    public void verifyProductIsStillInCart() {
        cartActions.verifyProductIsDisplayedInCart();
    }

    public void navigateToAccountDetails() {
        basePage.myAccountButton().waitUntilVisible();
        basePage.myAccountButton().click();
        accountPage.myProfileLink().waitUntilVisible(Duration.ofSeconds(10));
        accountPage.myProfileLink().scrollElementToCenter();
        accountPage.myProfileLink().waitUntilVisible(Duration.ofSeconds(10));
        accountPage.myProfileLink().click();
        accountPage.userProfileHeader().waitUntilVisible(Duration.ofSeconds(10));
    }

    // ========== PROFILE MANAGEMENT METHODS ==========

    public void changeNameAndLastName(String name, String lastName) {
        accountPageHelper.changeUserNameAndLastName(name, lastName);
    }

    /**
     * Changes name and last name with random data to verify actual updates
     */
    public void changeNameAndLastNameWithRandomData() {
        accountPageHelper.changeUserNameAndLastNameWithRandomData();
    }

    /**
     * Generates and stores random name data for later use in verification
     */
    public void generateAndStoreRandomNameData() {
        accountPageHelper.generateAndStoreRandomNameData();
    }

    /**
     * Verifies that name and last name have been updated with stored random data
     */
    public void verifyNameAndLastNameIsUpdated() {
        accountPageHelper.verifyNameAndLastNameIsUpdated();
    }

    /**
     * Changes username and last name based on provided parameters
     * Handles "RANDOM" keyword to generate random names using proper generators
     */
    public void changeUserNameAndLastNameWithParameters(String name, String lastName) {
        // Use the existing random data generation method when both are "RANDOM"
        if ("RANDOM".equalsIgnoreCase(name) && "RANDOM".equalsIgnoreCase(lastName)) {
            changeNameAndLastNameWithRandomData();
        } else {
            // Handle specific names or mixed scenarios
            String actualFirstName = name;
            String actualLastName = lastName;

            if ("RANDOM".equalsIgnoreCase(name)) {
                actualFirstName = accountPageHelper.generateRandomFirstName();
            }
            if ("RANDOM".equalsIgnoreCase(lastName)) {
                actualLastName = accountPageHelper.generateRandomLastName();
            }

            changeNameAndLastName(actualFirstName, actualLastName);
        }
    }

    public void saveProfileChanges() {
        accountPageHelper.saveProfileChanges();
    }

    public void verifyUserProfileHeader(String expected) {
        accountPageHelper.verifyUserProfileHeader(expected);
    }

    public void verifyNameAndLastNameHeader(String expected) {
        accountPageHelper.verifyNameAndLastNameHeader(expected);
    }

    public void verifyNameAndLastNameValue(String expected) {
        accountPageHelper.verifyNameAndLastNameValue(expected);
    }

    public void verifyPasswordHeader(String expected) {
        accountPageHelper.verifyPasswordHeader(expected);
    }

    public void verifyPasswordValue(String expected) {
        accountPageHelper.verifyPasswordValue(expected);
    }

    public void verifyEmailValue(String expectedEmail) {
        accountPageHelper.verifyEmailValue(expectedEmail);
    }

    public void navigateToBillingAddress() {
        basePage.myAccountButton().waitUntilVisible();
        basePage.myAccountButton().click();
        accountPage.myProfileLink().waitUntilVisible(Duration.ofSeconds(10));
        accountPage.myProfileLink().click();
        accountPage.editSavedBillingAddressButton().waitUntilVisible(Duration.ofSeconds(10));
        accountPage.editSavedBillingAddressButton().clickFirst();
    }

    // ========== ADDRESS MANAGEMENT METHODS ==========

    public void editBillingAddressWithValidData() {
        // Use random billing data to ensure we're actually updating with different information
        var billingData = AccountPageTestData.generateRandomCompanyBillingAddress();
        accountPageHelper.fillBillingAddressForm(billingData);
        AccountPageTestData.storeBillingAddressDataForEditing(billingData);
    }

    public void verifyBillingAddressIsUpdated() {
        // Wait a bit for the save operation to complete and page to load
        waitABit(3000);

        // Navigate back to profile page to verify the update
        basePage.myAccountButton().waitUntilVisible(Duration.ofSeconds(15));
        basePage.myAccountButton().click();
        accountPage.myProfileLink().waitUntilVisible(Duration.ofSeconds(15));
        // Scroll to element and use action click to avoid click interception
        accountPage.myProfileLink().scrollElementToCenter();
        waitABit(1000);
        accountPage.myProfileLink().click();

        // Get the random data that was actually used for the update
        String expectedAddress = Storage.getStorage().getValue(IStorageKey.UPDATED_BILLING_ADDRESS);
        String expectedCity = Storage.getStorage().getValue(IStorageKey.UPDATED_BILLING_CITY);
        String expectedCompany = Storage.getStorage().getValue(IStorageKey.UPDATED_BILLING_COMPANY);
        String expectedPhone = Storage.getStorage().getValue(IStorageKey.UPDATED_BILLING_PHONE);

        // Use the billing address value element from the profile page with longer wait
        accountPage.savedBillingAddressValues().waitUntilVisible(Duration.ofSeconds(15));
        String actualAddressText = accountPage.savedBillingAddressValues().getTextByIndex(0);

        SoftAssertions softAssertions = new SoftAssertions();
        // Use case-insensitive comparison since the UI displays city in uppercase
        softAssertions.assertThat(actualAddressText.toLowerCase()).contains(expectedAddress.toLowerCase());
        softAssertions.assertThat(actualAddressText.toLowerCase()).contains(expectedCity.toLowerCase());
        softAssertions.assertThat(actualAddressText).contains(expectedCompany);
        softAssertions.assertThat(actualAddressText).contains(expectedPhone);
        softAssertions.assertAll();
    }

    public void navigateToShippingAddress() {
        basePage.myAccountButton().waitUntilVisible();
        basePage.myAccountButton().click();
        accountPage.myProfileLink().waitUntilVisible(Duration.ofSeconds(10));
        accountPage.myProfileLink().click();
        accountPage.editSavedShippingAddressButton().waitUntilVisible(Duration.ofSeconds(10));
        accountPage.editSavedShippingAddressButton().clickFirst();
    }

    public void editShippingAddressWithValidData() {
        // Use random shipping data to ensure we're actually updating with different information
        var shippingData = AccountPageTestData.generateRandomShippingAddress();
        accountPageHelper.fillShippingAddressForm(shippingData);
        AccountPageTestData.storeShippingAddressDataForEditing(shippingData);
    }

    public void verifyShippingAddressIsUpdated() {
        // Wait for save operation to complete and navigate back to profile
        waitABit(Constants.AccountPage.DEFAULT_WAIT_TIME);
        navigateToAccountDetails();

        // Get stored data and verify address
        String expectedAddress = Storage.getStorage().getValue(IStorageKey.UPDATED_SHIPPING_ADDRESS);
        String expectedCity = Storage.getStorage().getValue(IStorageKey.UPDATED_SHIPPING_CITY);
        String expectedFirstName = Storage.getStorage().getValue(IStorageKey.UPDATED_SHIPPING_FIRST_NAME);
        String expectedLastName = Storage.getStorage().getValue(IStorageKey.UPDATED_SHIPPING_LAST_NAME);
        String expectedPhone = Storage.getStorage().getValue(IStorageKey.UPDATED_SHIPPING_PHONE);
        String expectedPostalCode = Storage.getStorage().getValue(IStorageKey.UPDATED_SHIPPING_POSTAL_CODE);

        accountPage.savedShippingAddressValues().waitUntilVisible(Duration.ofSeconds(15));
        String actualAddressText = accountPage.savedShippingAddressValues().getElementByIndex(0).getText();

        SoftAssertions softAssertions = new SoftAssertions();
        softAssertions.assertThat(actualAddressText.toLowerCase()).contains(expectedAddress.toLowerCase());
        softAssertions.assertThat(actualAddressText.toLowerCase()).contains(expectedCity.toLowerCase());
        softAssertions.assertThat(actualAddressText).contains(expectedFirstName);
        softAssertions.assertThat(actualAddressText).contains(expectedLastName);
        softAssertions.assertThat(actualAddressText).contains(expectedPhone);
        softAssertions.assertThat(actualAddressText).contains(expectedPostalCode);

        // Verify no company indicators are present
        for (String indicator : Constants.AccountPage.COMPANY_INDICATORS) {
            softAssertions.assertThat(actualAddressText.toLowerCase()).doesNotContain(indicator);
        }
        softAssertions.assertAll();
    }

    public void clickAddNewShippingAddress() {
        navigateToAccountDetails();
        accountPage.addNewShippingAddressButton().waitUntilVisible(Duration.ofSeconds(10));
        accountPage.addNewShippingAddressButton().click();
    }

    public void enterValidShippingAddressData() {
        var shippingData = AccountPageTestData.generateRandomShippingAddress();
        accountPageHelper.fillShippingAddressForm(shippingData);
        AccountPageTestData.storeShippingAddressDataForNewAddress(shippingData);
    }

    public void saveNewAddress() {
        accountPageHelper.saveProfileChanges();
    }

    public void verifyNewShippingAddressIsAdded() {
        // Get the random data that was actually used for the new address
        String expectedAddress = Storage.getStorage().getValue(IStorageKey.NEW_SHIPPING_ADDRESS);
        String expectedCity = Storage.getStorage().getValue(IStorageKey.NEW_SHIPPING_CITY);
        String expectedFirstName = Storage.getStorage().getValue(IStorageKey.NEW_SHIPPING_FIRST_NAME);
        String expectedLastName = Storage.getStorage().getValue(IStorageKey.NEW_SHIPPING_LAST_NAME);
        String expectedPhone = Storage.getStorage().getValue(IStorageKey.NEW_SHIPPING_PHONE);
        String expectedPostalCode = Storage.getStorage().getValue(IStorageKey.NEW_SHIPPING_POSTAL_CODE);

        accountPage.savedShippingAddressValues().waitUntilVisible(Duration.ofSeconds(10));
        // Get the last address (newly added addresses are typically added at the end)
        int addressCount = accountPage.savedShippingAddressValues().getSize();
        String actualAddressText = accountPage.savedShippingAddressValues().getTextByIndex(addressCount - 1);

        SoftAssertions softAssertions = new SoftAssertions();
        // Verify only the fields that were actually added (no company name for shipping)
        softAssertions.assertThat(actualAddressText.toLowerCase()).contains(expectedAddress.toLowerCase());
        softAssertions.assertThat(actualAddressText.toLowerCase()).contains(expectedCity.toLowerCase());
        softAssertions.assertThat(actualAddressText).contains(expectedFirstName);
        softAssertions.assertThat(actualAddressText).contains(expectedLastName);
        softAssertions.assertThat(actualAddressText).contains(expectedPhone);
        softAssertions.assertThat(actualAddressText).contains(expectedPostalCode);

        // Verify that NO company name appears in the new shipping address (since it was set to null)
        // Check that common company indicators are not present
        softAssertions.assertThat(actualAddressText.toLowerCase()).doesNotContain("sp. z o.o.");
        softAssertions.assertThat(actualAddressText.toLowerCase()).doesNotContain("s.a.");
        softAssertions.assertThat(actualAddressText.toLowerCase()).doesNotContain("ltd");
        softAssertions.assertThat(actualAddressText.toLowerCase()).doesNotContain("company");
        softAssertions.assertThat(actualAddressText.toLowerCase()).doesNotContain("solutions");
        softAssertions.assertThat(actualAddressText.toLowerCase()).doesNotContain("tech");
        softAssertions.assertThat(actualAddressText.toLowerCase()).doesNotContain("business");

        softAssertions.assertAll();
    }

    public void clickAddNewBillingAddress() {
        // Navigate to account page first
        basePage.myAccountButton().waitUntilVisible(Duration.ofSeconds(10));
        basePage.myAccountButton().click();
        accountPage.myProfileLink().waitUntilVisible(Duration.ofSeconds(10));
        accountPage.myProfileLink().click();
        accountPage.addNewBillingAddressButton().waitUntilVisible(Duration.ofSeconds(10));
        accountPage.addNewBillingAddressButton().click();
    }

    public void selectCompanyBillingAddressOption() {
        accountPage.companyBillingAddressOption().waitUntilVisible(Duration.ofSeconds(10));
        accountPage.companyBillingAddressOption().click();
    }

    public void enterValidCompanyBillingAddressData() {
        var billingData = AccountPageTestData.generateRandomCompanyBillingAddress();
        accountPageHelper.fillBillingAddressForm(billingData);
        AccountPageTestData.storeBillingAddressDataForNewAddress(billingData);
    }

    public void verifyNewCompanyBillingAddressIsAdded() {
        // Wait for save operation to complete
        waitABit(3000);

        // Navigate to addresses page to see the saved addresses
        basePage.myAccountButton().waitUntilVisible(Duration.ofSeconds(15));
        basePage.myAccountButton().click();
        accountPage.myProfileLink().waitUntilVisible(Duration.ofSeconds(15));
        accountPage.myProfileLink().click();

        // Get the random data that was actually used for the new address
        String expectedAddress = Storage.getStorage().getValue(IStorageKey.NEW_BILLING_ADDRESS);
        String expectedCity = Storage.getStorage().getValue(IStorageKey.NEW_BILLING_CITY);
        String expectedCompany = Storage.getStorage().getValue(IStorageKey.NEW_BILLING_COMPANY);
        String expectedPhone = Storage.getStorage().getValue(IStorageKey.NEW_BILLING_PHONE);
        String expectedNip = Storage.getStorage().getValue(IStorageKey.NEW_BILLING_NIP);

        accountPage.savedBillingAddressValues().waitUntilVisible(Duration.ofSeconds(10));
        // Get the last address (newly added addresses are typically added at the end)
        int addressCount = accountPage.savedBillingAddressValues().getSize();
        String actualAddressText = accountPage.savedBillingAddressValues().getTextByIndex(addressCount - 1);

        SoftAssertions softly = new SoftAssertions();
        // Use case-insensitive comparison since the UI displays city in uppercase
        softly.assertThat(actualAddressText.toLowerCase()).contains(expectedAddress.toLowerCase());
        softly.assertThat(actualAddressText.toLowerCase()).contains(expectedCity.toLowerCase());
        softly.assertThat(actualAddressText).contains(expectedCompany);
        softly.assertThat(actualAddressText).contains(expectedPhone);
        softly.assertThat(actualAddressText).contains(expectedNip);
        softly.assertAll();
    }

    public void selectIndividualBillingAddressOption() {
        accountPage.individualBillingAddressOption().waitUntilVisible(Duration.ofSeconds(10));
        accountPage.individualBillingAddressOption().click();
    }

    public void enterValidIndividualBillingAddressData() {
        var billingData = AccountPageTestData.generateRandomIndividualBillingAddress();
        accountPageHelper.fillBillingAddressForm(billingData);
        AccountPageTestData.storeBillingAddressDataForNewAddress(billingData);
    }

    public void verifyNewIndividualBillingAddressIsAdded() {
        waitABit(3000);

        basePage.myAccountButton().waitUntilVisible(Duration.ofSeconds(15));
        basePage.myAccountButton().click();
        accountPage.myProfileLink().waitUntilVisible(Duration.ofSeconds(15));
        accountPage.myProfileLink().click();
        // Get the random data that was actually used for the new address
        String expectedAddress = Storage.getStorage().getValue(IStorageKey.NEW_BILLING_ADDRESS);
        String expectedCity = Storage.getStorage().getValue(IStorageKey.NEW_BILLING_CITY);
        String expectedName = Storage.getStorage().getValue(IStorageKey.NEW_BILLING_COMPANY);
        String expectedPhone = Storage.getStorage().getValue(IStorageKey.NEW_BILLING_PHONE);

        accountPage.savedBillingAddressValues().waitUntilVisible(Duration.ofSeconds(10));
        // Get the last address (newly added addresses are typically added at the end)
        int addressCount = accountPage.savedBillingAddressValues().getSize();
        String actualAddressText = accountPage.savedBillingAddressValues().getTextByIndex(addressCount - 1);

        SoftAssertions softAssertions = new SoftAssertions();
        // Use case-insensitive comparison since the UI displays city in uppercase
        softAssertions.assertThat(actualAddressText.toLowerCase()).contains(expectedAddress.toLowerCase());
        softAssertions.assertThat(actualAddressText.toLowerCase()).contains(expectedCity.toLowerCase());
        softAssertions.assertThat(actualAddressText).contains(expectedName);
        softAssertions.assertThat(actualAddressText).contains(expectedPhone);
        softAssertions.assertAll();
    }

    public void enterShippingAddressDataWithoutPhoneNumber() {
        var shippingData = AccountPageTestData.createShippingAddressWithoutPhone();
        accountPageHelper.fillShippingAddressForm(shippingData);
    }

    public void verifyRequiredFieldErrorForPhoneNumber() {
        accountPageHelper.verifyRequiredFieldErrorForPhoneNumber();
    }

    // ========== SHIPPING ADDRESS VALIDATION METHODS ==========

    public void enterShippingAddressDataWithoutName() {
        var shippingData = AccountPageTestData.createShippingAddressWithoutName();
        accountPageHelper.fillShippingAddressForm(shippingData);
    }

    public void enterShippingAddressDataWithoutLastName() {
        var shippingData = AccountPageTestData.createShippingAddressWithoutLastName();
        accountPageHelper.fillShippingAddressForm(shippingData);
    }

    public void enterShippingAddressDataWithoutAddress() {
        var shippingData = AccountPageTestData.createShippingAddressWithoutAddress();
        accountPageHelper.fillShippingAddressForm(shippingData);
    }

    public void enterShippingAddressDataWithoutCity() {
        var shippingData = AccountPageTestData.createShippingAddressWithoutCity();
        accountPageHelper.fillShippingAddressForm(shippingData);
    }

    public void enterShippingAddressDataWithoutPostCode() {
        var shippingData = AccountPageTestData.createShippingAddressWithoutPostCode();
        accountPageHelper.fillShippingAddressForm(shippingData);
    }

    public void verifyRequiredFieldErrorForName() {
        accountPageHelper.verifyRequiredFieldErrorForName();
    }

    public void verifyRequiredFieldErrorForLastName() {
        accountPageHelper.verifyRequiredFieldErrorForLastName();
    }

    public void verifyRequiredFieldErrorForAddress() {
        accountPageHelper.verifyRequiredFieldErrorForAddress();
    }

    public void verifyRequiredFieldErrorForCity() {
        accountPageHelper.verifyRequiredFieldErrorForCity();
    }

    public void verifyRequiredFieldErrorForPostCode() {
        accountPageHelper.verifyRequiredFieldErrorForPostCode();
    }

    // ========== BILLING ADDRESS VALIDATION METHODS ==========

    public void enterBillingAddressDataWithoutName() {
        var billingData = AccountPageTestData.createBillingAddressWithoutName();
        accountPageHelper.fillBillingAddressForm(billingData);
    }

    public void enterBillingAddressDataWithoutLastName() {
        var billingData = AccountPageTestData.createBillingAddressWithoutLastName();
        accountPageHelper.fillBillingAddressForm(billingData);
    }

    public void enterBillingAddressDataWithoutAddress() {
        var billingData = AccountPageTestData.createBillingAddressWithoutAddress();
        accountPageHelper.fillBillingAddressForm(billingData);
    }

    public void enterBillingAddressDataWithoutCity() {
        var billingData = AccountPageTestData.createBillingAddressWithoutCity();
        accountPageHelper.fillBillingAddressForm(billingData);
    }

    public void enterBillingAddressDataWithoutPostCode() {
        var billingData = AccountPageTestData.createBillingAddressWithoutPostCode();
        accountPageHelper.fillBillingAddressForm(billingData);
    }


    /**
     * Deletes the newly added shipping address (last address in the list)
     */
    public void deleteNewlyAddedShippingAddress() {
        accountPageHelper.deleteNewlyAddedShippingAddress();
    }

    /**
     * Deletes the newly added billing address (last address in the list)
     */
    public void deleteNewlyAddedBillingAddress() {
        accountPageHelper.deleteNewlyAddedBillingAddress();
    }

    // ========== CHAT FUNCTIONALITY METHODS ==========

    public void openChatHistory() {
        basePage.myAccountButton().waitUntilVisible();
        basePage.myAccountButton().click();
        // Get and store username from profile before entering chat
        accountPageHelper.getUserNameFromProfileAndStore();

        accountPage.contactWithTheStoreLink().waitUntilVisible(Duration.ofSeconds(10));
        accountPage.contactWithTheStoreLink().click();
        accountPageHelper.waitForStability(Constants.AccountPage.DEFAULT_WAIT_TIME);
        accountPage.contactChatHistoryLinks().clickRandomly();
        accountPage.chatInputField().waitUntilVisible(Duration.ofSeconds(10));
    }

    public void typeAndSendChatMessage(String message) {
        accountPageHelper.typeAndSendChatMessage(message);
    }

    public void verifyChatUserName() {
        accountPageHelper.verifyChatUserName();
    }

    public void verifyChatDateAndTime() {
        accountPageHelper.verifyChatDateAndTime();
    }

    public void verifyChatSentMessage() {
        accountPageHelper.verifyChatSentMessage();
    }

    public void verifyMessageReceivedHeader() {
        accountPageHelper.verifyMessageReceivedHeader();
    }

    // ========== NEWSLETTER AGREEMENT CHECKBOX METHODS ==========

    public void checkNewsletterAgreementCheckbox() {
        accountPageHelper.checkNewsletterAgreementCheckbox();
    }

    public void uncheckNewsletterAgreementCheckbox() {
        accountPageHelper.uncheckNewsletterAgreementCheckbox();
    }

    public void verifyNewsletterAgreementCheckboxLabel() {
        accountPageHelper.verifyNewsletterAgreementCheckboxLabel();
    }

    public void verifyNewsletterAgreementCheckboxIsChecked() {
        accountPageHelper.verifyNewsletterAgreementCheckboxIsChecked();
    }

    public void verifyNewsletterAgreementCheckboxIsUnchecked() {
        accountPageHelper.verifyNewsletterAgreementCheckboxIsUnchecked();
    }

    // ========== FILE ATTACHMENT METHODS ==========

    public void typeMessageAndAttachFile(String message, String fileName) {
        accountPageHelper.typeMessageAndAttachFile(message, fileName);
    }

    public void verifyAttachedFileName(String expectedFileName) {
        accountPageHelper.verifyAttachedFileName(expectedFileName);
    }

    public void sendMessageWithAttachedFile() {
        accountPageHelper.sendMessageWithAttachedFile();
    }

    public void verifyAttachedFileInChat() {
        accountPageHelper.verifyAttachedFileInChat();
    }

    public void verifyAttachedFileDownload() {
        accountPageHelper.verifyAttachedFileDownload();
    }

    public void verifyInvalidFileTypeError() {
        accountPageHelper.verifyInvalidFileTypeError();
    }

    public void attachUnsupportedFile(String message, String fileName) {
        accountPageHelper.attachUnsupportedFile(message, fileName);
    }

    public void cleanupDownloadedTestFiles() {
        accountPageHelper.cleanupDownloadedTestFiles();
    }

    public void performPreTestCleanup() {
        accountPageHelper.performPreTestCleanup();
    }

    public void performPostTestCleanup() {
        accountPageHelper.performPostTestCleanup();
    }
    public void attemptToSubmitEmptyTicketRequest() {
        accountPageHelper.attemptToSubmitEmptyTicketRequest();
    }

    public void verifyEmptyMessageFieldError() {
        accountPageHelper.verifyEmptyMessageFieldError();
    }

    public void verifyReasonForContactNotSelectedError() {
        accountPageHelper.verifyReasonForContactNotSelectedError();
    }
    public void enterCompanyBillingAddressDataWithoutCompanyName() {
        var billingData = AccountPageTestData.createCompanyBillingAddressWithoutCompanyName();
        accountPageHelper.fillBillingAddressForm(billingData);
    }

    public void verifyRequiredFieldErrorForCompanyName() {
        accountPageHelper.verifyRequiredFieldErrorForCompanyName();
    }

    public void verifyShippingAddressCanBeSavedAfterFixingErrors() {
        // First create an address with all valid data to fix the errors
        var validShippingData = AccountPageTestData.generateRandomShippingAddress();
        accountPageHelper.fillShippingAddressForm(validShippingData);
        // Store the data for verification
        AccountPageTestData.storeShippingAddressDataForNewAddress(validShippingData);
        accountPageHelper.saveProfileChanges();

        // Verify the address appears in the shipping address list
        verifyNewShippingAddressIsAdded();
    }

    public void verifyPersonalBillingAddressCanBeSavedAfterFixingErrors() {
        // First create an address with all valid data to fix the errors
        var validBillingData = AccountPageTestData.generateRandomIndividualBillingAddress();
        accountPageHelper.fillBillingAddressForm(validBillingData);
        // Store the data for verification
        AccountPageTestData.storeBillingAddressDataForNewAddress(validBillingData);
        accountPageHelper.saveProfileChanges();

        // Verify the address appears in the billing address list
        verifyNewIndividualBillingAddressIsAdded();
    }

    public void verifyCompanyBillingAddressCanBeSavedAfterFixingErrors() {
        // First create an address with all valid data to fix the errors
        var validBillingData = AccountPageTestData.generateRandomCompanyBillingAddress();
        accountPageHelper.fillBillingAddressForm(validBillingData);
        // Store the data for verification
        AccountPageTestData.storeBillingAddressDataForNewAddress(validBillingData);
        accountPageHelper.saveProfileChanges();

        // Verify the address appears in the billing address list
        verifyNewCompanyBillingAddressIsAdded();
    }
    public void verifyCompanyBillingAddressCanBeSavedWithoutPhone() {
        // The address was already saved in the previous step, now just verify it appears in the list
        // Store the data for verification (matching what was entered)
        var billingData = AccountPageTestData.createCompanyBillingAddressWithoutPhone();
        AccountPageTestData.storeBillingAddressDataForNewAddress(billingData);

        // Verify the address appears in the billing address list
        verifyNewCompanyBillingAddressIsAdded();
    }

    public void verifyPersonalBillingAddressCanBeSavedWithoutPhone() {
        // The address was already saved in the previous step, now just verify it appears in the list
        // Store the data for verification (matching what was entered)
        var billingData = AccountPageTestData.createPersonalBillingAddressWithoutPhone();
        AccountPageTestData.storeBillingAddressDataForNewAddress(billingData);

        // Verify the address appears in the billing address list
        verifyNewIndividualBillingAddressIsAdded();
    }

    public void verifyCompanyBillingAddressCanBeSavedWithPhone() {
        // The address was already saved in the previous step, now just verify it appears in the list
        // Store the data for verification (matching what was entered)
        var billingData = AccountPageTestData.createCompanyBillingAddressWithPhone();
        AccountPageTestData.storeBillingAddressDataForNewAddress(billingData);

        // Verify the address appears in the billing address list
        verifyNewCompanyBillingAddressIsAdded();
    }

    public void verifyPersonalBillingAddressCanBeSavedWithPhone() {
        // The address was already saved in the previous step, now just verify it appears in the list
        // Store the data for verification (matching what was entered)
        var billingData = AccountPageTestData.createPersonalBillingAddressWithPhone();
        AccountPageTestData.storeBillingAddressDataForNewAddress(billingData);

        // Verify the address appears in the billing address list
        verifyNewIndividualBillingAddressIsAdded();
    }  public void enterCompanyBillingAddressDataWithoutPhone() {
        var billingData = AccountPageTestData.createCompanyBillingAddressWithoutPhone();
        accountPageHelper.fillBillingAddressForm(billingData);
    }

    public void enterPersonalBillingAddressDataWithoutPhone() {
        var billingData = AccountPageTestData.createPersonalBillingAddressWithoutPhone();
        accountPageHelper.fillBillingAddressForm(billingData);
    }

    public void enterCompanyBillingAddressDataWithPhone() {
        var billingData = AccountPageTestData.createCompanyBillingAddressWithPhone();
        accountPageHelper.fillBillingAddressForm(billingData);
    }

    public void enterPersonalBillingAddressDataWithPhone() {
        var billingData = AccountPageTestData.createPersonalBillingAddressWithPhone();
        accountPageHelper.fillBillingAddressForm(billingData);
    }
    public void verifyRequiredFieldErrorForNip() {
        accountPageHelper.verifyRequiredFieldErrorForNip();
    }
    public void enterCompanyBillingAddressDataWithoutNip() {
        var billingData = AccountPageTestData.createCompanyBillingAddressWithoutNip();
        accountPageHelper.fillBillingAddressForm(billingData);
    }

    public void enterCompanyBillingAddressDataWithoutAddress() {
        var billingData = AccountPageTestData.createCompanyBillingAddressWithoutAddress();
        accountPageHelper.fillBillingAddressForm(billingData);
    }

    public void enterCompanyBillingAddressDataWithoutCity() {
        var billingData = AccountPageTestData.createCompanyBillingAddressWithoutCity();
        accountPageHelper.fillBillingAddressForm(billingData);
    }

    public void enterCompanyBillingAddressDataWithoutPostCode() {
        var billingData = AccountPageTestData.createCompanyBillingAddressWithoutPostCode();
        accountPageHelper.fillBillingAddressForm(billingData);
    }

    // ========== INVALID PHONE NUMBER PREFIX VALIDATION METHODS ==========

    public void enterShippingAddressDataWithInvalidPhonePrefix() {
        var shippingData = AccountPageTestData.createShippingAddressWithInvalidPhonePrefix();
        accountPageHelper.fillShippingAddressForm(shippingData);
    }

    public void enterPersonalBillingAddressDataWithInvalidPhonePrefix() {
        var billingData = AccountPageTestData.createPersonalBillingAddressWithInvalidPhonePrefix();
        accountPageHelper.fillBillingAddressForm(billingData);
    }

    public void enterCompanyBillingAddressDataWithInvalidPhonePrefix() {
        var billingData = AccountPageTestData.createCompanyBillingAddressWithInvalidPhonePrefix();
        accountPageHelper.fillBillingAddressForm(billingData);
    }

    public void verifyInvalidPhoneNumberErrorForShippingAddress() {
        accountPageHelper.verifyInvalidPhoneNumberErrorForAddress();
    }

    public void verifyInvalidPhoneNumberErrorForBillingAddress() {
        accountPageHelper.verifyInvalidPhoneNumberErrorForAddress();
    }

}
