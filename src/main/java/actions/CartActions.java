package actions;

import common.constants.IStorageKey;
import common.constants.Constants;
import net.serenitybdd.core.pages.WebElementFacade;
import net.serenitybdd.core.steps.UIInteractionSteps;
import org.assertj.core.api.SoftAssertions;
import pages.BasePage;
import pages.CartPage;
import pages.CategoryPage;
import utils.Buttons;
import utils.storage.Storage;

import java.time.Duration;
import java.util.List;
import java.util.Random;

public class CartActions extends UIInteractionSteps {
    private BasePage basePage;
    private CartPage cartPage;
    private CategoryPage categoryPage;
    private Buttons buttons;
    private MainMenuActions mainMenuActions;

    public void verifyCartCounterShows(String productCount) {
        SoftAssertions softAssertions = new SoftAssertions();
        String cartCounterText = basePage.cartCounter().getText();
        softAssertions.assertThat(cartCounterText).isEqualTo("(" + productCount + ")");
    }

    public void verifyProductIsDisplayedInCart() {
        SoftAssertions softAssertions = new SoftAssertions();
        String savedProductPrice = Storage.getStorage().getValue(IStorageKey.FIRST_PRODUCT_PRICE);
        String savedProductQuantity = Storage.getStorage().getValue(IStorageKey.FIRST_PRODUCT_QUANTITY);
        String savedProductName = Storage.getStorage().getValue(IStorageKey.FIRST_PRODUCT_NAME);
        if (!cartPage.discountPriceInCart().isEmpty()) {
            //verify other cart components
            softAssertions.assertThat(cartPage.discountPriceInCart().getElementByIndex(0).getText()).isEqualTo(savedProductPrice);
        } else {
            //verify other cart components
            softAssertions.assertThat(cartPage.productPriceInCart().getElementByIndex(0).getText()).isEqualTo(savedProductPrice);
        }
        //verify the cart message is displayed
        softAssertions.assertThat(cartPage.cartMessage().getText()).isEqualTo(Constants.CartMessages.CART_WARNING);
        //verify the product name and quantity in the cart
        softAssertions.assertThat(cartPage.productNameInCart().getElementByIndex(0).getText()).isEqualTo(savedProductName);
        //verify the product quantity in the cart
        softAssertions.assertThat(cartPage.productQuantityInCart().getElementByIndex(0).getValue()).isEqualTo(savedProductQuantity);
        softAssertions.assertAll();
    }

    public void verifyProductDetailsAreCorrectInCart() {
        SoftAssertions softAssertions = new SoftAssertions();
        waitABit(5000);
        String savedProductName = Storage.getStorage().getValue(IStorageKey.FIRST_PRODUCT_NAME);
        String savedProductPrice = Storage.getStorage().getValue(IStorageKey.FIRST_PRODUCT_PRICE);
        String savedProductQuantity = Storage.getStorage().getValue(IStorageKey.FIRST_PRODUCT_QUANTITY);
        softAssertions.assertThat(cartPage.productNameInCart().getElementByIndex(0).getText()).isEqualTo(savedProductName);

        softAssertions.assertThat(cartPage.productPriceInCart().getElementByIndex(0).getText()).isEqualTo(savedProductPrice);

        softAssertions.assertThat(cartPage.productQuantityInCart().getElementByIndex(0).getValue()).isEqualTo(savedProductQuantity);
        softAssertions.assertAll();
    }


    public void verifyBothProductsAreDisplayedInCart() {
        SoftAssertions softAssertions = new SoftAssertions();
        waitABit(5000);

        // Get all product elements in cart
        List<WebElementFacade> discountPricesInCart = cartPage.discountPriceInCart().getAllElements();
        List<WebElementFacade> regularPricesInCart = cartPage.productPriceInCart().getAllElements();
        List<WebElementFacade> productNamesInCart = cartPage.productNameInCart().getAllElements();
        List<WebElementFacade> productQuantitiesInCart = cartPage.productQuantityInCart().getAllElements();

        // Verify we have at least 2 products in the cart
        softAssertions.assertThat(productNamesInCart.size()).isGreaterThanOrEqualTo(2);

        // Get saved product details
        String savedFirstProductName = Storage.getStorage().getValue(IStorageKey.FIRST_PRODUCT_NAME);
        String savedFirstProductPrice = Storage.getStorage().getValue(IStorageKey.FIRST_PRODUCT_PRICE);
        String savedFirstProductQuantity = Storage.getStorage().getValue(IStorageKey.FIRST_PRODUCT_QUANTITY);

        String savedSecondProductName = Storage.getStorage().getValue(IStorageKey.SECOND_PRODUCT_NAME);
        String savedSecondProductPrice = Storage.getStorage().getValue(IStorageKey.SECOND_PRODUCT_PRICE);
        String savedSecondProductQuantity = Storage.getStorage().getValue(IStorageKey.SECOND_PRODUCT_QUANTITY);

        // Create a list to track products in cart
        boolean firstProductFound = false;
        boolean secondProductFound = false;

        // Search for both products in the cart regardless of order
        for (int i = 0; i < productNamesInCart.size(); i++) {
            String currentProductName = productNamesInCart.get(i).getText();
            String currentProductQuantity = productQuantitiesInCart.get(i).getValue();

            // Check if this is the first product
            if (currentProductName.equals(savedFirstProductName) &&
                    currentProductQuantity.equals(savedFirstProductQuantity) &&
                    !firstProductFound) {

                firstProductFound = true;

                // Verify price for first product
                String currentPrice = getCurrentProductPrice(i, discountPricesInCart, regularPricesInCart);
                softAssertions.assertThat(currentPrice).isEqualTo(savedFirstProductPrice);

            }
            // Check if this is the second product
            else if (currentProductName.equals(savedSecondProductName) &&
                    currentProductQuantity.equals(savedSecondProductQuantity) &&
                    !secondProductFound) {

                secondProductFound = true;

                // Verify price for second product
                String currentPrice = getCurrentProductPrice(i, discountPricesInCart, regularPricesInCart);
                softAssertions.assertThat(currentPrice).isEqualTo(savedSecondProductPrice);
            }
        }

        // Verify both products were found
        softAssertions.assertThat(firstProductFound).isTrue();
        softAssertions.assertThat(secondProductFound).isTrue();

        softAssertions.assertAll();
    }

    /**
     * Helper method to get the current price for a product at a specific index
     * Handles both discount and regular prices by trying both and returning the non-empty one
     */
    private String getCurrentProductPrice(int productIndex, List<WebElementFacade> discountPrices, List<WebElementFacade> regularPrices) {
        String price = "";

        // Try to get discount price first (if available at this index)
        if (productIndex < discountPrices.size()) {
            try {
                String discountPrice = discountPrices.get(productIndex).getText();
                if (discountPrice != null && !discountPrice.trim().isEmpty()) {
                    price = discountPrice;
                }
            } catch (Exception e) {
                // Discount price not available at this index, continue to regular price
            }
        }

        // If no discount price found, try regular price
        if ((price == null || price.trim().isEmpty()) && productIndex < regularPrices.size()) {
            try {
                String regularPrice = regularPrices.get(productIndex).getText();
                if (regularPrice != null && !regularPrice.trim().isEmpty()) {
                    price = regularPrice;
                }
            } catch (Exception e) {
                // Regular price not available at this index
            }
        }

        return price != null ? price : "";
    }

    // Generic method to verify multiple products in cart (updated for your storage pattern)

    public void verifyCartIsEmpty() {
        SoftAssertions softAssertions = new SoftAssertions();
        cartPage.cartMessage().waitUntilNotVisible(Duration.ofSeconds(10));
        String emptyCartMessageHeader = cartPage.emptyCartMessageHeader().getText();
        softAssertions.assertThat(emptyCartMessageHeader).isEqualTo(Constants.CartMessages.EMPTY_CART_HEADER);
        //verify empty cart message description
        String emptyCartMessageDescription = cartPage.emptyCartMessageDescription().getText();
        softAssertions.assertThat(emptyCartMessageDescription).isEqualTo(Constants.CartMessages.EMPTY_CART_DESCRIPTION);
        softAssertions.assertAll();
    }

    // Method to verify specific product by index in cart (updated for your storage pattern)
    public void verifyProductAtIndexInCart(int productIndex) {
        SoftAssertions softAssertions = new SoftAssertions();
        waitABit(3000);

        // Get all product elements in cart
        List<WebElementFacade> productNamesInCart = cartPage.productNameInCart().getAllElements();
        List<WebElementFacade> productPricesInCart = cartPage.productPriceInCart().getAllElements();
        List<WebElementFacade> productQuantitiesInCart = cartPage.productQuantityInCart().getAllElements();

        // Verify the product exists at given index
        softAssertions.assertThat(productNamesInCart.size()).isGreaterThan(productIndex);

        // Determine storage keys based on index
        String nameKey, priceKey, quantityKey;

        if (productIndex == 0) {
            // First product uses FIRST_PRODUCT_* keys
            nameKey = IStorageKey.FIRST_PRODUCT_NAME;
            priceKey = IStorageKey.FIRST_PRODUCT_PRICE;
            quantityKey = IStorageKey.FIRST_PRODUCT_QUANTITY;
        } else if (productIndex == 1) {
            // Second product uses SECOND_PRODUCT_* keys
            nameKey = IStorageKey.SECOND_PRODUCT_NAME;
            priceKey = IStorageKey.SECOND_PRODUCT_PRICE;
            quantityKey = IStorageKey.SECOND_PRODUCT_QUANTITY;
        } else {
            // For third and later products, you'd need to add more keys
            throw new RuntimeException("Storage keys for product at index " + productIndex + " not defined. Please add more product keys to IStorageKey.");
        }

        String savedProductName = Storage.getStorage().getValue(nameKey);
        String savedProductPrice = Storage.getStorage().getValue(priceKey);
        String savedProductQuantity = Storage.getStorage().getValue(quantityKey);

        softAssertions.assertThat(productNamesInCart.get(productIndex).getText()).isEqualTo(savedProductName);
        softAssertions.assertThat(productPricesInCart.get(productIndex).getText()).isEqualTo(savedProductPrice);
        softAssertions.assertThat(productQuantitiesInCart.get(productIndex).getValue()).isEqualTo(savedProductQuantity);

        softAssertions.assertAll();
    }

    public void changeProductQuantityInCart(String quantity) {
        //Save the total quantity to storage
        String currentTotalCartPrice = cartPage.totalPriceInCart().getText();
        Storage.getStorage().saveValue(IStorageKey.TOTAL_PRODUCT_QUANTITY, currentTotalCartPrice);
        cartPage.productQuantityInput().waitUntilVisible();
        cartPage.productQuantityInput().clearText();
        cartPage.productQuantityInput().type(quantity);
        //save the quantity to storage
        Storage.getStorage().saveValue(IStorageKey.PRODUCT_QUANTITY, quantity);
        waitABit(5000);
    }

    public void changeProductQuantityToRandomAmount() {
        Random random = new Random();
        int randomQuantity = random.nextInt(48) + 10; // nextInt(48) gives 0-47, +2 gives 2-49
        changeProductQuantityInCart(String.valueOf(randomQuantity));
    }

    public void verifyTotalPriceIsUpdatedCorrectly() {
        SoftAssertions softAssertions = new SoftAssertions();
        String currentTotalCartPrice = cartPage.totalPriceInCart().getText();
        String savedTotalCartPrice = Storage.getStorage().getValue(IStorageKey.TOTAL_PRODUCT_QUANTITY);
        softAssertions.assertThat(currentTotalCartPrice).isEqualTo(savedTotalCartPrice);
        softAssertions.assertAll();
    }

    public void verifyQuantityShowsInCart(String quantity) {
        SoftAssertions softAssertions = new SoftAssertions();
        cartPage.productQuantityInCart().waitUntilVisible();
        String currentQuantity = cartPage.productQuantityInCart().getElementByIndex(0).getValue();
        softAssertions.assertThat(currentQuantity).isEqualTo(quantity);
        softAssertions.assertAll();
    }

    public void removeProductFromCart() {
        cartPage.removeItemButton().waitUntilVisible();
        cartPage.removeItemButton().clickByIndex(0);
        waitABit(5000);
    }

    public void clickOnProductNameInCart() {
        cartPage.productNameInCart().waitUntilVisible();
        //add extra wait to ensure the product name is loaded and stable
        waitABit(3000);
        // Click on the first product name in the cart
        cartPage.productNameInCart().getElementByIndex(0).click();
        // Wait for the product page to load
        buttons.buttonWithText(Constants.Buttons.ADD_TO_CART).waitUntilVisible(Duration.ofSeconds(10));

    }

    public void verifyContinueShoppingButtonIsDisplayed() {
        SoftAssertions softAssertions = new SoftAssertions();
        cartPage.continueShoppingButton().waitUntilVisible();
        softAssertions.assertThat(cartPage.continueShoppingButton().isVisible()).isTrue();
        softAssertions.assertThat(cartPage.continueShoppingButton().isEnabled()).isTrue();
        softAssertions.assertAll();

    }

    public void clickContinueShopping() {
        SoftAssertions softAssertions = new SoftAssertions();
        cartPage.continueShoppingButton().waitUntilVisible();
        cartPage.continueShoppingButton().click();
        // Wait for the page to load after clicking continue shopping
        categoryPage.categoryPageHeader().waitUntilVisible();
        softAssertions.assertThat(categoryPage.categoryPageHeader().isVisible()).isTrue();

    }

    public void verifyProductIsStillInCart() {
        SoftAssertions softAssertions = new SoftAssertions();
        //get the product name from storage
        String savedProductName = Storage.getStorage().getValue(IStorageKey.FIRST_PRODUCT_NAME);
        // Verify the product name in the cart
        cartPage.productNameInCart().waitUntilVisible(Duration.ofSeconds(10));
        String productNameInCart = cartPage.productNameInCart().getElementByIndex(0).getText();
        softAssertions.assertThat(productNameInCart).isEqualTo(savedProductName);
        softAssertions.assertAll();
    }

    public void verifyTotalPriceIsUpdatedCorrectlyToTimesTheProductPrice(String quantity) {
        SoftAssertions softAssertions = new SoftAssertions();
        // Get the product price from storage
        String productPrice = Storage.getStorage().getValue(IStorageKey.FIRST_PRODUCT_PRICE);
        // Calculate the expected total price
        double expectedTotalPrice = Double.parseDouble(productPrice.replace(" zł", "").replace(",", ".")) * Integer.parseInt(quantity);
        // Get the current total price from cart
        String currentTotalPrice = cartPage.totalPriceInCart().getText();
        // Remove "zł" and convert to double for comparison
        double currentTotalPriceValue = Double.parseDouble(currentTotalPrice.replace(" zł", "").replace(",", "."));
        softAssertions.assertThat(currentTotalPriceValue).isEqualTo(expectedTotalPrice);
        softAssertions.assertAll();
    }

    public void verifySubtotalIsCalculatedCorrectly() {
        SoftAssertions softAssertions = new SoftAssertions();
        // Get the product price from storage
        String firstProductPrice = Storage.getStorage().getValue(IStorageKey.FIRST_PRODUCT_PRICE);
        String secondProductPrice = Storage.getStorage().getValue(IStorageKey.SECOND_PRODUCT_PRICE);
        // Get the product quantity from cart

        double expectedSubtotal = Double.parseDouble(firstProductPrice.replace(" zł", "").replace(",", ".")) + Double.parseDouble(secondProductPrice.replace(" zł", "").replace(",", "."));
        // Get the current subtotal from cart
        cartPage.productPriceInCart().waitUntilVisible();
        String currentSubtotal = cartPage.totalPriceInCart().getText();
        // Remove "zł" and convert to double for comparison
        double currentSubtotalValue = Double.parseDouble(currentSubtotal.replace(" zł", "").replace(",", "."));
        softAssertions.assertThat(currentSubtotalValue).isEqualTo(expectedSubtotal);
        softAssertions.assertAll();
    }

    public void verifyVatCostIsDisplayedCorrectly() {
        //vat is 23% of the subtotal price
        SoftAssertions softAssertions = new SoftAssertions();
        //verify the vat cost header is displayed
        softAssertions.assertThat(cartPage.vatHeader().getText()).isEqualTo(Constants.PriceFormats.VAT_COST_HEADER);
        //verify the total price header is displayed
        softAssertions.assertThat(cartPage.totalPriceInCartHeader().getText()).isEqualTo(Constants.PriceFormats.TOTAL_PRICE_HEADER);
        String subtotalPrice = cartPage.totalPriceInCart().getText();

        // Check if shipping price is present and add it to the total for VAT calculation
        double totalForVatCalculation = Double.parseDouble(subtotalPrice.replace(" zł", "").replace(",", "."));
        if (cartPage.shippingPriceInCart().isVisible() && cartPage.productNameInCart().isEmpty()) {
            // Only add shipping price to VAT calculation when not on cart page (i.e., at checkout)
            // In cart, shipping shows "od X.XX" (availability), at checkout it shows actual price "X.XX zł"
            String shippingPrice = cartPage.shippingPriceInCart().getText();
            // Remove "od " prefix, currency, and normalize decimal separator
            double shippingPriceValue = Double.parseDouble(shippingPrice.replace("od ", "").replace(" zł", "").replace(",", ".").replace(" ", ""));
            totalForVatCalculation += shippingPriceValue;
        }
        // VAT is included in total price, so VAT = Total × (23/123)
        double vatValue = totalForVatCalculation * (23.0 / 123.0);
        String vatCost = cartPage.vatAmount().getText();
        double vatCostValue = Double.parseDouble(vatCost.replace(" zł", "").replace(",", "."));
        softAssertions.assertThat(vatCostValue).isCloseTo(vatValue, org.assertj.core.data.Offset.offset(0.01));
    }

    public void clickOnPlusButtonToIncreaseQuantity() {
        cartPage.addProductQuantityButton().waitUntilVisible();
        cartPage.addProductQuantityButton().click();
        // Wait for the quantity to update
        cartPage.productQuantityInCart().getElementByIndex(0).waitUntilVisible(Duration.ofSeconds(5));
        //add a wait to ensure the UI has updated
        waitABit(5000);
    }


    public void verifyErrorMessageForInsufficientStock() {
        SoftAssertions softAssertions = new SoftAssertions();

        // Wait for the error message to be visible
        cartPage.outOfStockMessageAtCart().waitUntilVisible(Duration.ofSeconds(10));

        // Get the current quantity from the cart input field
        String currentQuantity = cartPage.productQuantityInCart().getElementByIndex(0).getValue();

        // Get the actual error message
        String errorMessage = cartPage.outOfStockMessageAtCart().getText();

        // Verify that the error message contains the current quantity
        softAssertions.assertThat(errorMessage).contains(currentQuantity);

        // Optional: More specific verification - check if it contains the expected Polish text pattern
        String expectedPattern = "Aktualnie można dodać do koszyka maksymalnie " + currentQuantity + " sztuk.";
        softAssertions.assertThat(errorMessage).isEqualTo(expectedPattern);

        softAssertions.assertAll();
    }

    public void clickOnMinusButtonToDecreaseQuantity() {
        cartPage.subtractProductQuantityButton().waitUntilVisible();
        cartPage.subtractProductQuantityButton().click();
        // Wait for the quantity to update
        cartPage.productQuantityInCart().getElementByIndex(0).waitUntilVisible(Duration.ofSeconds(5));
        //add a wait to ensure the UI has updated
        waitABit(5000);
    }

    //calculate the total price in cart base on the product price only, for all products in the cart
    public void calculateTotalPriceInCart() {
        double totalPrice = 0.0;
        List<WebElementFacade> productPrices = cartPage.productPriceInCart().getAllElements();
        for (WebElementFacade priceElement : productPrices) {
            String priceText = priceElement.getText().replace(" zł", "").replace(",", ".").replace(" ", "");
            totalPrice += Double.parseDouble(priceText);
        }
    }


    public void verifyQuantityInputShows(String quantity) {
        SoftAssertions softAssertions = new SoftAssertions();
        cartPage.productQuantityInCart().getElementByIndex(0).waitUntilVisible(Duration.ofSeconds(5));
        String currentQuantity = cartPage.productQuantityInCart().getElementByIndex(0).getValue();
        softAssertions.assertThat(currentQuantity).isEqualTo(quantity);
        softAssertions.assertAll();
    }

    public void removeOneProductFromCart() {
        SoftAssertions softAssertions = new SoftAssertions();
        //Save the total price in cart to storage before removing the product
        String totalPriceBeforeRemoval = cartPage.totalPriceInCart().getText();
        Storage.getStorage().saveValue(IStorageKey.TOTAL_CART_PRICE_BEFORE, totalPriceBeforeRemoval);
        //Save the second product name to storage before removing the product
        String secondProductName = cartPage.productNameInCart().getElementByIndex(1).getText();
        Storage.getStorage().saveValue(IStorageKey.PRODUCT_NAME_IN_CART, secondProductName);
        //get the name of the product to be removed
        String productName = cartPage.productNameInCart().getElementByIndex(0).getText();
        cartPage.removeItemButton().waitUntilVisible(Duration.ofSeconds(10));
        cartPage.removeItemButton().clickByIndex(0);
        waitABit(5000);
        softAssertions.assertThat(cartPage.productNameInCart().getElementByIndex(0).getText()).isNotEqualTo(productName);
    }

    public void verifyRemainingProductIsDisplayedInCart() {
        SoftAssertions softAssertions = new SoftAssertions();
        // Get the product name from storage
        String savedProductName = Storage.getStorage().getValue(IStorageKey.PRODUCT_NAME_IN_CART);
        // Verify the remaining product name in the cart
        cartPage.productNameInCart().waitUntilVisible();
        String productNameInCart = cartPage.productNameInCart().getElementByIndex(0).getText();
        softAssertions.assertThat(productNameInCart).isEqualTo(savedProductName);
        softAssertions.assertAll();
    }

    public void verifyTotalPriceIsUpdatedCorrectlyForRemainingProduct() {
        SoftAssertions softAssertions = new SoftAssertions();
        // Get the total price before removal from storage
        String totalPriceBeforeRemoval = Storage.getStorage().getValue(IStorageKey.TOTAL_CART_PRICE_BEFORE);
        // Get the product price from storage
        String productPrice = Storage.getStorage().getValue(IStorageKey.SECOND_PRODUCT_PRICE);
        // Calculate the expected total price for the remaining product
        double expectedTotalPrice = Double.parseDouble(productPrice.replace(" zł", "").replace(",", "."));
        // Get the current total price from cart
        String currentTotalPrice = cartPage.totalPriceInCart().getText();
        // Remove "zł" and convert to double for comparison
        double currentTotalPriceValue = Double.parseDouble(currentTotalPrice.replace(" zł", "").replace(",", "."));
        softAssertions.assertThat(currentTotalPriceValue).isEqualTo(expectedTotalPrice);
        softAssertions.assertAll();
    }

    public void verifyFreeShippingThresholdMessageIsDisplayed() {
        SoftAssertions softAssertions = new SoftAssertions();
        //get the total cart price
        String totalCartPrice = cartPage.totalPriceInCart().getText();
        //get the remaining amount for free shipping
        String remainingAmountForFreeShipping = cartPage.freeShippingThresholdRemainingValue().getText();
        //get the delivery price in cart
        String deliveryPrice = cartPage.deliveryPriceHeader().getText();
        System.out.println("Delivery Price: " + deliveryPrice);
        //the remaining amount for free shipping is the difference between 300 minus the total cart price
        double totalCartPriceValue = Double.parseDouble(totalCartPrice.replace(" zł", "").replace(",", "."));
        double remainingAmountValue = 300.0 - totalCartPriceValue;

        // Wait for the free shipping message to be visible
        cartPage.freeShippingThresholdMessage().waitUntilVisible(Duration.ofSeconds(10));
        // Verify the free shipping message text
        String expectedMessage = "Do darmowej dostawy brakuje";
        String actualMessage = cartPage.freeShippingThresholdMessage().getText();
        softAssertions.assertThat(actualMessage).isEqualTo(expectedMessage);
        // Verify the delivery price header is displayed
        softAssertions.assertThat(deliveryPrice).contains("Dostawa\n" + "od 12,99 zł");
        // Verify the remaining amount for free shipping is displayed correctly
        softAssertions.assertThat(remainingAmountForFreeShipping).isEqualTo(String.format("%.2f zł", remainingAmountValue).replace(".", ","));
        softAssertions.assertAll();
    }

    public void verifyFreeDeliveryMessageIsDisplayedInCart() {
        SoftAssertions softAssertions = new SoftAssertions();
        // Wait for the free delivery message to be visible
        cartPage.freeDeliveryMessage().waitUntilVisible(Duration.ofSeconds(10));
        // Verify the free delivery message text
        String actualMessage = cartPage.freeDeliveryMessage().getText();
        softAssertions.assertThat(actualMessage).isEqualTo(Constants.CartMessages.FREE_DELIVERY_MESSAGE);
        softAssertions.assertAll();
    }

    public void applyDiscountCode(String discountCode) {
        //before applying the discount code, save the total price in cart to storage
        String totalPriceBeforeDiscount = cartPage.totalPriceInCart().getText();
        Storage.getStorage().saveValue(IStorageKey.TOTAL_CART_PRICE_BEFORE_DISCOUNT, totalPriceBeforeDiscount);
        // Save the applied discount code to storage for later verification
        Storage.getStorage().saveValue(IStorageKey.APPLIED_DISCOUNT_CODE, discountCode);
        // Wait for the coupon code input to be visible
        cartPage.couponCodeInput().waitUntilVisible(Duration.ofSeconds(10));
        // Type the discount code into the input field
        cartPage.couponCodeInput().typeAndEnter(discountCode);
        waitABit(5000);
        if (!cartPage.appliedDiscountHeader().isVisible()) {
            cartPage.applyCouponButton().click();
        }
    }

    public void verifyDiscountIsAppliedCorrectlyAndTotalPriceIsUpdatedWithDiscountApplied() {
        SoftAssertions softAssertions = new SoftAssertions();
        // Wait for the active coupon code header to be visible
        cartPage.activeCouponCodeHeader().waitUntilVisible(Duration.ofSeconds(10));

        // Get current URL to determine language
        String currentUrl = getDriver().getCurrentUrl();
        assert currentUrl != null;
        String expectedHeader = currentUrl.contains("/en") ? "Active code:" : "Aktywny kod:";

        // Verify the header matches the expected language version
        String actualHeader = cartPage.activeCouponCodeHeader().getText();
        softAssertions.assertThat(actualHeader).isEqualTo(expectedHeader);

        // Verify the applied discount name is displayed correctly
        String appliedDiscountName = cartPage.appliedDiscountName().getText();
        softAssertions.assertThat(appliedDiscountName).isEqualTo("Test");
        // Verify the discount value is displayed correctly
        cartPage.appliedDiscountHeader().waitUntilVisible(Duration.ofSeconds(10));
        String discountValue = cartPage.appliedDiscountValue().getText();
        String totalPriceInCart = cartPage.totalPriceInCart().getText();
        double discountValueDouble = Double.parseDouble(discountValue.replace(" zł", "").replace("PLN", "").replace(",", ".").replace(" ", ""));
        double currentTotalPriceValue = Double.parseDouble(totalPriceInCart.replace(" zł", "").replace("PLN", "").replace(",", ".").replace(" ", ""));
        softAssertions.assertThat(discountValueDouble).isEqualTo(currentTotalPriceValue);
        softAssertions.assertAll();
    }

    public void removeDiscountCode() {
        cartPage.removeCouponButton().waitUntilVisible(Duration.ofSeconds(10));
        cartPage.removeCouponButton().waitUntilClickable(Duration.ofSeconds(10));

        // Store initial state to verify removal
        boolean initialDiscountValueVisible = cartPage.appliedDiscountValue().isVisible();

        // Click the remove discount button
        cartPage.removeCouponButton().click();

        // Add a short wait for the UI to start processing the removal
        waitABit(3000);

        //retry mechanism if the first click doesn't work
        int maxRetries = 3;
        int retryCount = 0;

        while (retryCount < maxRetries && cartPage.activeCouponCodeHeader().isVisible()) {
            retryCount++;
            // Wait a bit longer and try clicking again
            waitABit(3000);

            if (cartPage.removeCouponButton().isVisible()) {
                cartPage.removeCouponButton().click();
                waitABit(2000);
            }
        }
        // Wait for all discount-related elements to disappear
        // Use multiple verification points to ensure complete removal
        try {
            cartPage.activeCouponCodeHeader().waitUntilNotVisible(Duration.ofSeconds(5));

            // Wait for the applied discount value to not be visible
            if (initialDiscountValueVisible) {
                cartPage.appliedDiscountValue().waitUntilNotVisible(Duration.ofSeconds(5));
            }

            // Wait for the applied discount header to not be visible
            cartPage.appliedDiscountHeader().waitUntilNotVisible(Duration.ofSeconds(5));

            // Additional verification - wait for the coupon input to be visible again
            cartPage.couponCodeInput().waitUntilVisible(Duration.ofSeconds(5));

        } catch (Exception e) {
            waitABit(5000);
            // Final check - if discount elements are still visible, throw a more descriptive error
            if (cartPage.activeCouponCodeHeader().isVisible()) {
                throw new RuntimeException("Failed to remove discount code - active coupon header still visible after multiple attempts");
            }
        }

        // Final verification that the discount has been completely removed
        verifyDiscountCodeIsCompletelyRemoved();
    }

    private void verifyDiscountCodeIsCompletelyRemoved() {
        if (cartPage.activeCouponCodeHeader().isVisible()) {
            throw new RuntimeException("Discount removal failed: Active coupon code header is still visible");
        }

        if (cartPage.appliedDiscountValue().isVisible()) {
            throw new RuntimeException("Discount removal failed: Applied discount value is still visible");
        }

        if (cartPage.appliedDiscountHeader().isVisible()) {
            throw new RuntimeException("Discount removal failed: Applied discount header is still visible");
        }

        if (!cartPage.couponCodeInput().isVisible()) {
            throw new RuntimeException("Discount removal failed: Coupon code input field is not visible");
        }
    }

    public void verifyDiscountIsRemovedAndTotalPriceIsUpdatedCorrectly() {
        SoftAssertions softAssertions = new SoftAssertions();

        // Add a wait to ensure the UI has fully updated after discount removal
        waitABit(3000);
        // Verify the active coupon code header is not visible
        softAssertions.assertThat(cartPage.activeCouponCodeHeader().isVisible()).isFalse();
        // Verify the applied discount header is not visible
        softAssertions.assertThat(cartPage.appliedDiscountHeader().isVisible()).isFalse();
        // Verify the discount value is not displayed
        softAssertions.assertThat(cartPage.appliedDiscountValue().isVisible()).isFalse();
        // Verify the coupon code input is visible and ready for new codes
        softAssertions.assertThat(cartPage.couponCodeInput().isVisible()).isTrue();
        // Verify the total price in cart is updated correctly after removing the discount
        String totalPriceBeforeDiscount = Storage.getStorage().getValue(IStorageKey.TOTAL_CART_PRICE_BEFORE_DISCOUNT);
        String totalPriceInCart = cartPage.totalPriceInCart().getText();
        double totalPriceBeforeDiscountValue = Double.parseDouble(totalPriceBeforeDiscount.replace(" zł", "").replace(",", "."));
        double currentTotalPriceValue = Double.parseDouble(totalPriceInCart.replace(" zł", "").replace(",", "."));
        softAssertions.assertThat(currentTotalPriceValue).isEqualTo(totalPriceBeforeDiscountValue);

        softAssertions.assertAll();
    }

    public void verifyErrorMessageForInvalidDiscountCode() {
        SoftAssertions softAssertions = new SoftAssertions();
        // Wait for the error message to be visible
        cartPage.invalidDiscountCodeMessage().waitUntilVisible(Duration.ofSeconds(10));
        // Verify the error message text
        String actualErrorMessage = cartPage.invalidDiscountCodeMessage().getText();
        softAssertions.assertThat(actualErrorMessage).isEqualTo(Constants.ErrorMessages.INVALID_DISCOUNT_CODE);
        softAssertions.assertAll();
    }

    public void verifyTotalPriceRemainsUnchanged() {
        SoftAssertions softAssertions = new SoftAssertions();
        // Get the total price before applying the discount
        String totalPriceBeforeDiscount = Storage.getStorage().getValue(IStorageKey.TOTAL_CART_PRICE_BEFORE_DISCOUNT);
        // Get the current total price in cart
        String currentTotalPrice = cartPage.totalPriceInCart().getText();
        // Remove "zł" and convert to double for comparison
        double totalPriceBeforeDiscountValue = Double.parseDouble(totalPriceBeforeDiscount.replace(" zł", "").replace(",", "."));
        double currentTotalPriceValue = Double.parseDouble(currentTotalPrice.replace(" zł", "").replace(",", "."));
        softAssertions.assertThat(currentTotalPriceValue).isEqualTo(totalPriceBeforeDiscountValue);
        softAssertions.assertAll();
    }

    public void verifyCartContentsArePreserved() {
        SoftAssertions softAssertions = new SoftAssertions();
        //open cart if is not already open
        if (!cartPage.cartMessage().isVisible()) {
            mainMenuActions.openCart();
        }
        // Verify the product name in the cart
        cartPage.productNameInCart().waitUntilVisible();
        String productNameInCart = cartPage.productNameInCart().getElementByIndex(0).getText();
        softAssertions.assertThat(productNameInCart).isEqualTo(Storage.getStorage().getValue(IStorageKey.FIRST_PRODUCT_NAME));
        softAssertions.assertAll();
    }

    public void verifyCouponCodePersistenceAfterLogin() {
        SoftAssertions softAssertions = new SoftAssertions();

        // Verify cart contents are preserved
        cartPage.productNameInCart().waitUntilVisible(Duration.ofSeconds(10));
        String productNameInCart = cartPage.productNameInCart().getElementByIndex(0).getText();
        String expectedProductName = Storage.getStorage().getValue(IStorageKey.FIRST_PRODUCT_NAME);
        softAssertions.assertThat(productNameInCart).isEqualTo(expectedProductName);

        // Verify coupon code is still applied
        cartPage.activeCouponCodeHeader().waitUntilVisible(Duration.ofSeconds(10));
        String expectedCouponHeader = "Aktywny kod:";
        String actualCouponHeader = cartPage.activeCouponCodeHeader().getText();
        softAssertions.assertThat(actualCouponHeader).isEqualTo(expectedCouponHeader);

        // Verify the applied discount name is still displayed correctly
        String appliedDiscountName = cartPage.appliedDiscountName().getText();
        softAssertions.assertThat(appliedDiscountName).isEqualTo("Test");

        // Verify the discount is still being applied (discount header and value are visible)
        softAssertions.assertThat(cartPage.appliedDiscountHeader().isVisible()).isTrue();
        softAssertions.assertThat(cartPage.appliedDiscountValue().isVisible()).isTrue();

        // Verify the discount value is still calculated correctly
        String discountValue = cartPage.appliedDiscountValue().getText();
        String totalPriceInCart = cartPage.totalPriceInCart().getText();
        double discountValueDouble = Double.parseDouble(discountValue.replace(" zł", "").replace(",", "."));
        double currentTotalPriceValue = Double.parseDouble(totalPriceInCart.replace(" zł", "").replace(",", "."));
        // The applied discount is 50% of the total price in cart
        softAssertions.assertThat(discountValueDouble).isEqualTo(currentTotalPriceValue);

        softAssertions.assertAll();
    }

    public void verifyTotalPriceIsUpdatedCorrectlyAfterModifyingQuantity() {
        //get the product quantity from storage
        String productQuantity = Storage.getStorage().getValue(IStorageKey.PRODUCT_QUANTITY);
        SoftAssertions softAssertions = new SoftAssertions();
        //get the product price from storage
        String productPrice = Storage.getStorage().getValue(IStorageKey.FIRST_PRODUCT_PRICE);
        //get the current total price in cart
        String currentTotalPrice = cartPage.totalPriceInCart().getText();
        //remove "zł" and convert to double for comparison
        double productPriceValue = Double.parseDouble(productPrice.replace(" zł", "").replace(",", "."));
        double currentTotalPriceValue = Double.parseDouble(currentTotalPrice.replace(" zł", "").replace(",", "."));
        softAssertions.assertThat(currentTotalPriceValue).isEqualTo(productPriceValue * Integer.parseInt(productQuantity));
        softAssertions.assertAll();
    }

    public void removeProductFromCartIfPresent() {
        String cartCounterText = basePage.cartCounter().getText();

        if (cartCounterText != null && !cartCounterText.isEmpty() && !cartCounterText.equals("(0)")) {
            if (!cartPage.cartMessage().isVisible()) {
                mainMenuActions.openCart();
            }
            waitABit(2000);
            List<WebElementFacade> removeButtons = cartPage.removeItemButton().getAllElements();
            while (!removeButtons.isEmpty() && removeButtons.size() > 0) {
                cartPage.removeItemButton().clickByIndex(0);
                waitABit(3000);
                removeButtons = cartPage.removeItemButton().getAllElements();
            }
            // Verify the cart is empty
            cartPage.emptyCartMessageHeader().waitUntilVisible(Duration.ofSeconds(10));
        }
    }

    public void verifyFreeShippingThresholdProgressBarShowsCorrectPercentage() {
        SoftAssertions softAssertions = new SoftAssertions();
        String totalCartPrice = cartPage.totalPriceInCart().getText();
        double totalCartPriceValue = Double.parseDouble(totalCartPrice.replace(" zł", "").replace(",", "."));
        double percentage = (totalCartPriceValue / Constants.PriceFormats.FREE_SHIPPING_THRESHOLD) * 100;

        if (percentage > 100) {
            percentage = 100;
        }
        int roundedPercentage = (int) Math.round(percentage);
        String expectedWidth = String.valueOf(roundedPercentage);
        cartPage.freeShippingThresholdProcessBar(expectedWidth).waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(cartPage.freeShippingThresholdProcessBar(expectedWidth).isVisible()).isTrue();
        softAssertions.assertThat(cartPage.vatHeader().getText()).isEqualTo(Constants.PriceFormats.VAT_COST_HEADER);
        softAssertions.assertThat(cartPage.totalPriceInCartHeader().getText()).isEqualTo(Constants.PriceFormats.TOTAL_PRICE_HEADER);

        double totalForVatCalculation = totalCartPriceValue;
        if (cartPage.shippingPriceInCart().isVisible() && cartPage.productNameInCart().isEmpty()) {
            // Only add shipping price to VAT calculation when not on cart page (i.e., at checkout)
            // In cart, shipping shows "od X.XX" (availability), at checkout it shows actual price "X.XX zł"
            String shippingPrice = cartPage.shippingPriceInCart().getText();
            // Remove "od " prefix, currency, and normalize decimal separator
            double shippingPriceValue = Double.parseDouble(shippingPrice.replace("od ", "").replace(" zł", "").replace(",", ".").replace(" ", ""));
            totalForVatCalculation += shippingPriceValue;
        }

        String actualVatCost = cartPage.vatAmount().getText();
        double actualVatCostValue = Double.parseDouble(actualVatCost.replace(" zł", "").replace(",", "."));
        // VAT is included in total price, so VAT = Total × (23/123)
        double expectedVatValue = totalForVatCalculation * (23.0 / 123.0);
        softAssertions.assertThat(actualVatCostValue).isCloseTo(expectedVatValue, org.assertj.core.data.Offset.offset(0.01));
        softAssertions.assertAll();
    }

    /**
     * Verify remaining amount for free shipping is correct
     */
    public void verifyRemainingAmountForFreeShippingIsCorrect() {
        SoftAssertions softAssertions = new SoftAssertions();
        String totalCartPrice = cartPage.totalPriceInCart().getText();
        double totalCartPriceValue = Double.parseDouble(totalCartPrice.replace(" zł", "").replace(",", "."));
        double expectedRemainingAmount = Constants.PriceFormats.FREE_SHIPPING_THRESHOLD - totalCartPriceValue;
        if (totalCartPriceValue < Constants.PriceFormats.FREE_SHIPPING_THRESHOLD) {
            cartPage.remainingAmountForFreeShipping().waitUntilVisible(Duration.ofSeconds(10));
            String displayedRemainingAmount = cartPage.remainingAmountForFreeShipping().getText();
            String expectedAmountFormatted = String.format("%.2f zł", expectedRemainingAmount).replace(".", ",");
            softAssertions.assertThat(displayedRemainingAmount).isEqualTo(expectedAmountFormatted);

        } else {
            softAssertions.assertThat(cartPage.remainingAmountForFreeShipping().isVisible()).isFalse();
        }

        softAssertions.assertAll();
    }
}
