package actions;

import common.constants.IStorageKey;
import common.constants.Constants;
import net.serenitybdd.core.pages.WebElementFacade;
import net.serenitybdd.core.steps.UIInteractionSteps;
import pages.ProductListPage;
import pages.ProductPage;
import pages.SearchPage;
import utils.BrowserUtils;
import utils.Buttons;
import utils.storage.Storage;

import java.time.Duration;
import java.util.*;

public class ProductListPageActions extends UIInteractionSteps {
    private ProductListPage productListPage;
    private Buttons buttons;
    private SearchPage searchPage;
    private ProductPage productPage;
    private final Random random = new Random();
    private final Set<Integer> visitedIndices = new HashSet<>();
    private static final Set<Integer> visitedProductIndices = new HashSet<>();
    private final Set<String> addedToCartProducts = new HashSet<>();
    private final Set<String> addedProductNames = new HashSet<>();
    // Method to get the count of products added to cart
    private int productCounter = 0;
    private int productAddedCount = 0;


    public void openTheFirstProduct() {
        searchPage.productName().waitUntilVisible(Duration.ofMinutes(1));
        openProductPageRandomly();
        productPage.productName().waitUntilVisible(Duration.ofMinutes(1));
    }

    public void openTheSecondProduct() {
        searchPage.productName().waitUntilVisible(Duration.ofMinutes(1));
        openProductPageRandomly();
        productPage.productName().waitUntilVisible(Duration.ofMinutes(1));
    }

    public void selectProductRandomly() {
        // Reset visited indices if all products have been tried
        int products = productListPage.productList().getSize();
        if (visitedIndices.size() >= products) {
            throw new RuntimeException(Constants.ErrorMessages.NO_PRODUCT_WITH_ADD_TO_CART);
        }

        while (true) {
            // Get a random unvisited product index
            int randomIndex;
            do {
                randomIndex = random.nextInt(products);
            } while (visitedIndices.contains(randomIndex));

            // Mark the index as visited
            visitedIndices.add(randomIndex);

            // Click on the random product to open its page
            var randomProduct = productListPage.productList().getElementByIndex(randomIndex);
            randomProduct.click();

            // Check if the "Do koszyka" button is present
            try {
                var doKoszykaButton = buttons.buttonWithText(Constants.Buttons.ADD_TO_CART);
                doKoszykaButton.isPresent();
                return; // Button found, exit the method
            } catch (NoSuchElementException e) {
                // Button not found, go back to the product list page
                BrowserUtils.navigateBack();
            }

            // If all products are visited and no button is found
            if (visitedIndices.size() >= products) {
                throw new RuntimeException(Constants.ErrorMessages.NO_PRODUCT_WITH_ADD_TO_CART);
            }

        }
    }

    public void verifyDoKoszykaButton() {
        // Check if the "Do koszyka" button is present
        try {
            var doKoszykaButton = buttons.buttonWithText(Constants.Buttons.ADD_TO_CART);
            doKoszykaButton.isPresent();
        } catch (NoSuchElementException e) {
            throw new RuntimeException("No '" + Constants.Buttons.ADD_TO_CART + "' button found");
        }
    }

    public void openFirstProduct() {
        productListPage.productList()
                .getElementByIndex(4)
                .click();

    }

    public void openSecondProduct() {
        productListPage.productList()
                .getElementByIndex(9)
                .click();
    }

    private boolean isAddToCartButtonAvailable() {
        var addToCartButton = buttons.buttonWithText(Constants.Buttons.ADD_TO_CART);
        try {
            waitABit(1000);
            return addToCartButton.isPresent() && addToCartButton.isVisible() && addToCartButton.isEnabled();
        } catch (Exception e) {
            return false;
        }
    }

    private void navigateBackToSearchResults() {
        getDriver().navigate().back();
        waitABit(2000);
    }

    public void openProductPageRandomly() {
        // Configuration constants
        final int MAX_GLOBAL_ATTEMPTS = 15;
        final int MAX_CYCLE_ATTEMPTS = 3;
        final long TIMEOUT_MINUTES = 5;

        long startTime = System.currentTimeMillis();
        long timeoutMs = Duration.ofMinutes(TIMEOUT_MINUTES).toMillis();
        int globalAttempts = 0;
        int cycleCount = 0;

        // Wait for products to be visible
        searchPage.productName().waitUntilVisible(Duration.ofMinutes(1));
        List<WebElementFacade> allProducts = searchPage.productName().getAllElements();
        int totalProducts = allProducts.size();

        if (totalProducts == 0) {
            throw new RuntimeException("No products found in search results");
        }

        // Check if all products are already processed
        if (addedToCartProducts.size() >= totalProducts) {
            throw new RuntimeException("All available products have already been added to cart");
        }

        while (true) {
            // Global timeout check
            if (System.currentTimeMillis() - startTime > timeoutMs) {
                throw new RuntimeException("Method timeout after " + TIMEOUT_MINUTES + " minutes");
            }

            // Global attempt limit check
            if (globalAttempts++ > MAX_GLOBAL_ATTEMPTS) {
                throw new RuntimeException("Maximum global attempts (" + MAX_GLOBAL_ATTEMPTS + ") exceeded");
            }

            // Reset visited indices if we've tried all products
            if (visitedProductIndices.size() >= totalProducts) {
                if (cycleCount++ >= MAX_CYCLE_ATTEMPTS) {
                    throw new RuntimeException("No suitable products found after " + MAX_CYCLE_ATTEMPTS + " complete cycles");
                }
                visitedProductIndices.clear();
            }

            // Find an unvisited product index
            int randomIndex = findUnvisitedProductIndex(totalProducts);
            if (randomIndex == -1) {
                // This shouldn't happen due to the check above, but safety net
                throw new RuntimeException("Unable to find unvisited product index");
            }

            visitedProductIndices.add(randomIndex);

            try {
                // Click on the selected product
                WebElementFacade selectedProduct = allProducts.get(randomIndex);
                selectedProduct.click();
                productPage.productName().waitUntilVisible(Duration.ofSeconds(10));

                // Check if this product was already processed
                String currentProductName = productPage.productName().getText();
                if (addedToCartProducts.contains(currentProductName)) {
                    // Product already processed, go back and try another
                    navigateBackToSearchResults();
                    searchPage.productName().waitUntilVisible(Duration.ofSeconds(10));
                    allProducts = refreshProductList();
                    totalProducts = allProducts.size();
                    continue;
                }

                // Check if Add to Cart button is available
                if (isAddToCartButtonAvailable()) {
                    saveProductDetailsToStorage();
                    return; // Successfully found and saved a new product
                }

                // Add to Cart not available, go back and try another
                waitABit(5000);
                navigateBackToSearchResults();
                searchPage.productName().waitUntilVisible(Duration.ofSeconds(10));
                allProducts = refreshProductList();
                totalProducts = allProducts.size();

            } catch (Exception e) {
                // Handle any exception by going back and trying another product
                try {
                    navigateBackToSearchResults();
                    searchPage.productName().waitUntilVisible(Duration.ofSeconds(10));
                    allProducts = refreshProductList();
                    totalProducts = allProducts.size();
                } catch (Exception navigationException) {
                    throw new RuntimeException("Failed to navigate back to search results after exception", navigationException);
                }
            }
        }
    }

    private int findUnvisitedProductIndex(int totalProducts) {
        int attempts = 0;
        int maxAttempts = totalProducts * 2; // Safety limit to prevent infinite loop

        while (attempts < maxAttempts) {
            int randomIndex = random.nextInt(totalProducts);
            if (!visitedProductIndices.contains(randomIndex)) {
                return randomIndex;
            }
            attempts++;
        }

        return -1; // No unvisited index found
    }

    private List<WebElementFacade> refreshProductList() {
        List<WebElementFacade> products = searchPage.productName().getAllElements();

        // If the product count changed significantly, clearly visited indices to adapt
        if (products.size() != visitedProductIndices.size() &&
                Math.abs(products.size() - visitedProductIndices.size()) > products.size() * 0.2) {
            visitedProductIndices.clear();
        }

        return products;
    }

    public void saveProductDetailsToStorage() {
        String productPrice;

        try {
            String productName = productPage.productName().getText();
            //get the product price, checking for discount price first
            if (productPage.discountPrice().isPresent()) {
                // If discount price is available, use it
                productPrice = productPage.discountPrice().getText();
            } else {
                // Otherwise, use the regular price
                productPrice = productPage.productPrice().getText();
            }
            String productQuantity = productPage.productQuantity().getValue();

            // Save with different keys based on how many products have been added
            if (productAddedCount == 0) {
                // First product
                Storage.getStorage().saveValue(IStorageKey.FIRST_PRODUCT_NAME, productName);
                Storage.getStorage().saveValue(IStorageKey.FIRST_PRODUCT_PRICE, productPrice);
                Storage.getStorage().saveValue(IStorageKey.FIRST_PRODUCT_QUANTITY, productQuantity);
            } else {
                // Second and subsequent products
                Storage.getStorage().saveValue(IStorageKey.SECOND_PRODUCT_NAME, productName);
                Storage.getStorage().saveValue(IStorageKey.SECOND_PRODUCT_PRICE, productPrice);
                Storage.getStorage().saveValue(IStorageKey.SECOND_PRODUCT_QUANTITY, productQuantity);
            }
            //Track this product as processed so it won't be selected again
            addedToCartProducts.add(productName);
            productAddedCount++;

        } catch (Exception e) {
            System.err.println("Error saving product details to storage: " + e.getMessage());
        }
    }


    // Method to reset the tracking when starting a new test scenario
    public void resetProductTracking() {
        addedProductNames.clear();
        productCounter = 0;
        visitedProductIndices.clear(); // Also clear visited indices for fresh start
    }

    // Method to check if a product has been added to cart
    public boolean isProductAddedToCart(String productName) {
        return addedProductNames.contains(productName);
    }

    // Method to get all products that have been added to cart
    public Set<String> getAddedProducts() {
        return new HashSet<>(addedProductNames); // Return copy to prevent external modification
    }

    // Method to get the count of products added to cart
    public int getAddedProductsCount() {
        return productCounter;
    }

    // Method to check if maximum products limit reached (useful for test scenarios)
    public boolean hasReachedMaxProducts(int maxProducts) {
        return productCounter >= maxProducts;
    }
}
