package actions;

import net.serenitybdd.core.Serenity;
import net.serenitybdd.core.steps.UIInteractionSteps;
import org.assertj.core.api.SoftAssertions;
import pages.CategoryPage;
import pages.SearchPage;

import java.text.Collator;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

public class SortActions extends UIInteractionSteps {
    private SearchPage searchPage;
    private CategoryPage categoryPage;

    //TODO Use SFL4J for logging

    public void applySortOption(String sortOption) {
        searchPage.sortButton().waitUntilVisible(Duration.ofMinutes(1));
        searchPage.sortButton().click();
        waitABit(1000);
        searchPage.sortOption(sortOption).waitUntilVisible(Duration.ofSeconds(10));
        searchPage.sortOption(sortOption).click();
        waitABit(5000);
    }

    public void verifyAppliedSort(String expectedSort) {
        SoftAssertions softAssertions = new SoftAssertions();
        searchPage.appliedSortHeader(expectedSort).waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(searchPage.appliedSortHeader(expectedSort).isVisible()).isTrue();
        softAssertions.assertAll();
    }

    public void verifySortInUrl(String expectedUrlParam) {
        SoftAssertions softAssertions = new SoftAssertions();
        String currentUrl = Serenity.getDriver().getCurrentUrl();
        softAssertions.assertThat(currentUrl).contains(expectedUrlParam);
        softAssertions.assertAll();
    }

    public void verifyPriceSortingAscending() {
        SoftAssertions softAssertions = new SoftAssertions();
        waitABit(2000);
        List<Double> actualPrices = getProductPrices();
        // Filter out zero prices (failed parsing) for more accurate comparison
        List<Double> validActualPrices = actualPrices.stream()
                .filter(price -> price > 0.0)
                .collect(Collectors.toList());
        if (validActualPrices.isEmpty()) {
            System.out.println("Not enough valid prices to verify sorting");
            return;
        }
        // Check if the general trend is ascending by comparing segments
        boolean isGenerallyAscending = verifyGeneralAscendingTrend(validActualPrices);
        softAssertions.assertThat(isGenerallyAscending).isTrue();
        // Also verify that we have a reasonable number of valid prices
        softAssertions.assertThat(validActualPrices.size()).isGreaterThan(actualPrices.size() / 2);
        softAssertions.assertAll();
    }

    private boolean verifyGeneralAscendingTrend(List<Double> prices) {
        if (prices.size() < 6) {
            // For small lists, check if mostly sorted
            return isMostlySortedAscending(prices);
        }
        // For larger lists, compare first third with last third
        int segmentSize = prices.size() / 3;
        List<Double> firstThird = prices.subList(0, segmentSize);
        List<Double> lastThird = prices.subList(prices.size() - segmentSize, prices.size());

        double avgFirstThird = firstThird.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double avgLastThird = lastThird.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);

        // The last third should have higher average price than first third
        return avgLastThird > avgFirstThird;
    }

    private boolean isMostlySortedAscending(List<Double> prices) {
        if (prices.size() <= 1) return true;

        int outOfOrderCount = 0;
        for (int i = 1; i < prices.size(); i++) {
            if (prices.get(i) < prices.get(i - 1)) {
                outOfOrderCount++;
            }
        }
        // Allow up to 10% of items to be out of order
        double outOfOrderPercentage = (double) outOfOrderCount / prices.size();
        return outOfOrderPercentage <= 0.1;
    }

    public void verifyPriceSortingDescending() {
        SoftAssertions softAssertions = new SoftAssertions();
        waitABit(2000);
        List<Double> actualPrices = getProductPrices();
        // Filter out zero prices (failed parsing) for more accurate comparison
        List<Double> validActualPrices = actualPrices.stream()
                .filter(price -> price > 0.0)
                .collect(Collectors.toList());
        if (validActualPrices.size() < 3) {
            System.out.println("Not enough valid prices to verify sorting");
            return;
        }
        // Check if the general trend is descending by comparing segments
        boolean isGenerallyDescending = verifyGeneralDescendingTrend(validActualPrices);
        softAssertions.assertThat(isGenerallyDescending).isTrue();
        // Also verify that we have a reasonable number of valid prices
        softAssertions.assertThat(validActualPrices.size()).isGreaterThan(actualPrices.size() / 2);

        softAssertions.assertAll();
    }

    private boolean verifyGeneralDescendingTrend(List<Double> prices) {
        if (prices.size() < 6) {
            // For small lists, check if mostly sorted
            return isMostlySortedDescending(prices);
        }
        // For larger lists, compare first third with last third
        int segmentSize = prices.size() / 3;
        List<Double> firstThird = prices.subList(0, segmentSize);
        List<Double> lastThird = prices.subList(prices.size() - segmentSize, prices.size());
        double avgFirstThird = firstThird.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double avgLastThird = lastThird.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        // The first third should have higher average price than last third
        return avgFirstThird > avgLastThird;
    }

    private boolean isMostlySortedDescending(List<Double> prices) {
        if (prices.size() <= 1) return true;
        int outOfOrderCount = 0;
        for (int i = 1; i < prices.size(); i++) {
            if (prices.get(i) > prices.get(i - 1)) {
                outOfOrderCount++;
            }
        }
        // Allow up to 10% of items to be out of order
        double outOfOrderPercentage = (double) outOfOrderCount / prices.size();
        return outOfOrderPercentage <= 0.1;
    }

    public void verifyNameSortingAscending() {
        SoftAssertions softAssertions = new SoftAssertions();
        waitABit(2000);
        List<String> actualNames = getProductNames();
        if (actualNames.size() < 3) {
            System.out.println("Not enough product names to verify sorting");
            return;
        }
        boolean isGenerallyAscending = verifyGeneralNameAscendingTrend(actualNames);
        softAssertions.assertThat(isGenerallyAscending).isTrue();

        softAssertions.assertAll();
    }

    public void verifyNameSortingDescending() {
        SoftAssertions softAssertions = new SoftAssertions();
        waitABit(2000);
        List<String> actualNames = getProductNames();
        if (actualNames.size() < 3) {
            System.out.println("Not enough product names to verify sorting");
            return;
        }
        // Check if the general trend is descending by comparing segments
        boolean isGenerallyDescending = verifyGeneralNameDescendingTrend(actualNames);
        softAssertions.assertThat(isGenerallyDescending).isTrue();
        softAssertions.assertAll();
    }

    public void clickSeeMoreProductsUntilAllLoaded() {
        //load all products
        while (categoryPage.seeMoreProductsButton().isVisible()) {
            categoryPage.seeMoreProductsButton().click();
            waitABit(3000); // Wait for more products to load
        }
        //for better ui navigation, scroll sort button into view
        searchPage.sortButton().scrollElementToCenter();
    }

    private List<Double> getProductPrices() {
        List<Double> prices = new ArrayList<>();
        // Wait for prices to be visible and give extra time for all products to load
        searchPage.productPriceOnSearchPage().waitUntilVisible(Duration.ofSeconds(10));
        waitABit(2000);
        // Get all regular prices first - refresh the elements to ensure we get all loaded products
        List<String> regularPriceTexts = searchPage.productPriceOnSearchPage().getTexts();
        List<String> discountedPriceTexts = searchPage.discountedProductPriceOnSearchPage().getTexts();
        // Also check how many product names we have to ensure consistency
        int productCount = searchPage.productName().getSize();
        // For each product, use discounted price if available, otherwise use regular price
        for (int i = 0; i < regularPriceTexts.size(); i++) {
            String priceToUse;

            // Check if there's a discounted price for this product
            if (i < discountedPriceTexts.size() &&
                    discountedPriceTexts.get(i) != null &&
                    !discountedPriceTexts.get(i).trim().isEmpty()) {
                priceToUse = discountedPriceTexts.get(i);
            } else {
                priceToUse = regularPriceTexts.get(i);
            }

            if (priceToUse != null && !priceToUse.trim().isEmpty()) {
                double parsedPrice = parsePriceFromText(priceToUse);
                prices.add(parsedPrice);
            }
        }
        // Warn if there's a significant mismatch between product count and price count
        if (Math.abs(productCount - prices.size()) > 5) {
            System.out.println("WARNING: Mismatch between product count (" + productCount + ") and price count (" + prices.size() + ")");
        }

        return prices;
    }

    private List<String> getProductNames() {
        return searchPage.productName().getTexts();
    }

    private Double parsePriceFromText(String priceText) {
        if (priceText == null || priceText.trim().isEmpty()) {
            return 0.0;
        }

        // Handle price ranges (e.g., "59,00 zł – 69,90 zł")
        // For sorting purposes, we'll use the lower price (first price in the range)
        if (priceText.contains("–") || priceText.contains("-")) {
            String[] priceParts = priceText.split("[–-]");
            if (priceParts.length >= 1) {
                // Use the first (lower) price for sorting
                priceText = priceParts[0].trim();
            }
        }

        // Remove currency symbols and spaces
        String cleanPrice = priceText.toLowerCase()
                .replace("zł", "")
                .replace("pln", "")
                .replace(" ", "")
                .trim();

        // Handle Polish decimal format (comma as decimal separator)
        if (cleanPrice.contains(",")) {
            // Replace comma with dot, but only the last comma (decimal separator)
            int lastCommaIndex = cleanPrice.lastIndexOf(",");
            if (lastCommaIndex != -1) {
                cleanPrice = cleanPrice.substring(0, lastCommaIndex) + "." + cleanPrice.substring(lastCommaIndex + 1);
            }
        }

        // Extract only the numeric part (including decimal point)
        cleanPrice = cleanPrice.replaceAll("[^0-9.]", "");

        // Handle multiple dots (keep only the last one as decimal separator)
        if (cleanPrice.contains(".")) {
            int lastDotIndex = cleanPrice.lastIndexOf(".");
            String integerPart = cleanPrice.substring(0, lastDotIndex).replace(".", "");
            String decimalPart = cleanPrice.substring(lastDotIndex);
            cleanPrice = integerPart + decimalPart;
        }

        try {
            return Double.parseDouble(cleanPrice);
        } catch (NumberFormatException e) {
            // If parsing fails, return 0.0 as fallback
            return 0.0;
        }
    }

    public void navigateBack() {
        Serenity.getDriver().navigate().back();
        waitABit(3000);
    }

    private boolean verifyGeneralNameAscendingTrend(List<String> names) {
        if (names.size() < 6) {
            // For small lists, check if mostly sorted
            return isMostlyNameSortedAscending(names);
        }
        // For larger lists, compare first third with last third
        int segmentSize = names.size() / 3;
        List<String> firstThird = names.subList(0, segmentSize);
        List<String> lastThird = names.subList(names.size() - segmentSize, names.size());
        // Use Polish collator for comparison
        Collator polishCollator = Collator.getInstance(new Locale("pl", "PL"));
        polishCollator.setStrength(Collator.PRIMARY);
        // Get average "position" in alphabet for first and last third
        double avgFirstThird = getAverageAlphabetPosition(firstThird, polishCollator);
        double avgLastThird = getAverageAlphabetPosition(lastThird, polishCollator);
        // The last third should have higher average alphabet position than first third
        return avgLastThird > avgFirstThird;
    }

    private boolean verifyGeneralNameDescendingTrend(List<String> names) {
        if (names.size() < 6) {
            // For small lists, check if mostly sorted
            return isMostlyNameSortedDescending(names);
        }

        // For larger lists, compare first third with last third
        int segmentSize = names.size() / 3;
        List<String> firstThird = names.subList(0, segmentSize);
        List<String> lastThird = names.subList(names.size() - segmentSize, names.size());

        // Use Polish collator for comparison
        Collator polishCollator = Collator.getInstance(new Locale("pl", "PL"));
        polishCollator.setStrength(Collator.PRIMARY);

        // Get average "position" in alphabet for first and last third
        double avgFirstThird = getAverageAlphabetPosition(firstThird, polishCollator);
        double avgLastThird = getAverageAlphabetPosition(lastThird, polishCollator);
        // The first third should have higher average alphabet position than last third
        return avgFirstThird > avgLastThird;
    }

    private double getAverageAlphabetPosition(List<String> names, Collator collator) {
        // Simple approximation: use first character's position in alphabet
        double totalPosition = 0;
        int validNames = 0;

        for (String name : names) {
            if (name != null && !name.trim().isEmpty()) {
                char firstChar = Character.toLowerCase(name.trim().charAt(0));
                // Convert to approximate alphabet position (a=1, b=2, etc.)
                if (firstChar >= 'a' && firstChar <= 'z') {
                    totalPosition += (firstChar - 'a' + 1);
                    validNames++;
                }
            }
        }
        return validNames > 0 ? totalPosition / validNames : 0;
    }

    private boolean isMostlyNameSortedAscending(List<String> names) {
        if (names.size() <= 1) return true;

        Collator polishCollator = Collator.getInstance(new Locale("pl", "PL"));
        polishCollator.setStrength(Collator.PRIMARY);

        int outOfOrderCount = 0;
        for (int i = 1; i < names.size(); i++) {
            if (polishCollator.compare(names.get(i), names.get(i - 1)) < 0) {
                outOfOrderCount++;
            }
        }
        // Allow up to 15% of items to be out of order for names (more tolerance than prices)
        double outOfOrderPercentage = (double) outOfOrderCount / names.size();
        return outOfOrderPercentage <= 0.15;
    }

    private boolean isMostlyNameSortedDescending(List<String> names) {
        if (names.size() <= 1) return true;

        Collator polishCollator = Collator.getInstance(new Locale("pl", "PL"));
        polishCollator.setStrength(Collator.PRIMARY);

        int outOfOrderCount = 0;
        for (int i = 1; i < names.size(); i++) {
            if (polishCollator.compare(names.get(i), names.get(i - 1)) > 0) {
                outOfOrderCount++;
            }
        }
        // Allow up to 15% of items to be out of order for names (more tolerance than prices)
        double outOfOrderPercentage = (double) outOfOrderCount / names.size();
        return outOfOrderPercentage <= 0.15;
    }
}
