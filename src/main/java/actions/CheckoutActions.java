package actions;

import common.constants.CustomerType;
import common.constants.Constants;
import common.constants.IStorageKey;
import common.test_data.*;
import helpers.CheckoutFormHelper;
import lombok.val;
import net.serenitybdd.annotations.Step;
import net.serenitybdd.annotations.Steps;
import net.serenitybdd.core.Serenity;
import net.serenitybdd.core.pages.WebElementFacade;
import net.serenitybdd.core.steps.UIInteractionSteps;
import org.assertj.core.api.SoftAssertions;
import org.openqa.selenium.*;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import pages.CartPage;
import pages.CheckoutPage;
import pages.ProductPage;
import utils.Buttons;
import utils.RandomDataGenerator;
import utils.storage.Storage;

import java.time.Duration;
import java.util.*;

public class CheckoutActions extends UIInteractionSteps {
    @Steps
    private CheckoutPage checkoutPage;

    @Steps
    private Buttons buttons;

    @Steps
    private OrderSuccessActions orderSuccessActions;
    @Steps
    private CartPage cartPage;
    @Steps
    private ProductPage productPage;

    private CheckoutFormHelper checkoutFormHelper;
    private final Set<Integer> selectedShippingAddressIndices = new HashSet<>();
    private final Set<Integer> selectedBillingAddressIndices = new HashSet<>();
    private String parentWindowHandle;

    /**
     * Initialize the checkout form helper
     */
    private CheckoutFormHelper getCheckoutFormHelper() {
        if (checkoutFormHelper == null) {
            checkoutFormHelper = new CheckoutFormHelper(checkoutPage);
        }
        return checkoutFormHelper;
    }

    // Use Serenity lifecycle hook to initialize this after dependencies are injected
    public void initialize() {
        this.checkoutFormHelper = new CheckoutFormHelper(checkoutPage);
    }

    public void clickOnContinueButton() {
        //wait a bit for stability
        waitABit(2000);
        if (checkoutPage.getContinueButtonForDelivery().isVisible()) {
            checkoutPage.getContinueButtonForDelivery().click();
        } else {
            buttons.buttonWithText(Constants.Buttons.CONTINUE).click();
        }
        waitABit(3000);

        // Check if the continue button is still visible after the first click, (sometimes it's still there)
        // and click it again if it is
        if (checkoutPage.getContinueButtonForDelivery().isVisible()) {
            checkoutPage.getContinueButtonForDelivery().click();
        } else if (buttons.buttonWithText(Constants.Buttons.CONTINUE).isVisible()) {
            buttons.buttonWithText(Constants.Buttons.CONTINUE).click();
        }
        waitABit(2000);
    }

    public void selectPickUpDeliveryMethod() {
        checkoutPage.getRadioButtonsOnCheckoutPage(Constants.DeliveryMethods.PERSONAL_PICKUP).click();
    }

    public void selectCourierDeliveryMethod() {
        checkoutPage.getRadioButtonsOnCheckoutPage(Constants.DeliveryMethods.COURIER).click();
    }

    public void selectInpostPaczkomatDeliveryMethod() {
        checkoutPage.getRadioButtonsOnCheckoutPage(Constants.DeliveryMethods.INPOST_PARCEL_LOCKER).click();
    }

    public void clickOnAgreeToTermsCheckbox() {
        //wait until the checkbox is visible
        checkoutPage.getPaymentAgreementCheckbox().waitUntilVisible(Duration.ofSeconds(15));
        //scroll to the checkbox
        checkoutPage.getPaymentAgreementCheckbox().scrollElementToCenter();
        checkoutPage.getPaymentAgreementCheckbox().click();
        waitABit(3000);
        //wait until the payment methods are visible
        checkoutPage.getRadioButtonsOnCheckoutPage(Constants.PaymentMethods.BLIK_WITH_CODE).waitUntilVisible(Duration.ofSeconds(10));
    }

    public void payWithBlik() {
        checkoutPage.getRadioButtonsOnCheckoutPage(Constants.PaymentMethods.BLIK_WITH_CODE).click();
        val inputField = checkoutPage.getBlikPaymentInput();
        //click the input field to make it active
        inputField.click();
        // Generate a dynamic BLIK code starting with "777" and 3 random digits
        String blikCode = TestDataFactory.generateValidBlikCode();
        inputField.sendKeys(blikCode);
        checkoutPage.getBlikPayButton().click();
    }

    // ========== FRAME SWITCHING HELPER METHODS ==========

    /**
     * Switch to card number input frame
     */
    private void switchToCardNumberFrame() {
        switchToCardFrame(Constants.CardFrameAttributes.ENCRYPTED_CARD_NUMBER);
    }

    /**
     * Switch to card expiration date input frame
     */
    private void switchToCardExpirationFrame() {
        switchToCardFrame(Constants.CardFrameAttributes.ENCRYPTED_EXPIRY_DATE);
    }

    /**
     * Switch to card CVC input frame
     */
    private void switchToCardCvcFrame() {
        switchToCardFrame(Constants.CardFrameAttributes.ENCRYPTED_SECURITY_CODE);
    }

    /**
     * Generic method to switch to card input frames
     * Reduces code duplication and improves maintainability
     *
     * @param frameAttribute The data-cse attribute value for the frame
     */
    private void switchToCardFrame(String frameAttribute) {
        try {
            String xpath = String.format(Constants.XPathPatterns.CARD_FRAME_PATTERN, frameAttribute);
            WebElement frame = Serenity.getDriver().findElement(By.xpath(xpath));
            Serenity.getDriver().switchTo().frame(frame);
        } catch (Exception e) {
            throw new RuntimeException(Constants.ErrorMessages.FAILED_FRAME_SWITCH + frameAttribute, e);
        }
    }

    /**
     * Switch back to default content from any frame
     */
    private void switchToDefaultContent() {
        Serenity.getDriver().switchTo().defaultContent();
    }

    /**
     * Pay with card using default valid card data
     */
    private void payWithCard() {
        PaymentTestData.CardPaymentData cardData = TestDataFactory.getDefaultValidCard();
        payWithCard(cardData);
    }

    /**
     * Pay with card using specific card data
     * Optimized method with proper error handling and frame management
     *
     * @param cardData The card payment data to use
     */
    private void payWithCard(PaymentTestData.CardPaymentData cardData) {
        try {
            // Select card payment method
            checkoutPage.getRadioButtonsOnCheckoutPage(Constants.PaymentMethods.BANK_CARD).click();

            // Fill card number
            switchToCardNumberFrame();
            checkoutPage.getCardNumberInput().sendKeys(cardData.getCardNumber());
            switchToDefaultContent();

            // Fill expiration date
            switchToCardExpirationFrame();
            checkoutPage.getCardExpirationDateInput().sendKeys(cardData.getExpirationDate());
            switchToDefaultContent();

            // Fill CVC
            switchToCardCvcFrame();
            checkoutPage.getCardSecurityCodeInput().sendKeys(cardData.getCvc());
            switchToDefaultContent();

            // Submit payment
            checkoutPage.getCardPayButton().click();

        } catch (Exception e) {
            // Ensure we're back to default content in case of error
            switchToDefaultContent();
            throw new RuntimeException(Constants.ErrorMessages.FAILED_CARD_PAYMENT, e);
        }
    }

    /**
     * Attempt to pay with invalid card for negative testing
     */
    public void payWithInvalidCard() {
        PaymentTestData.CardPaymentData cardData = TestDataFactory.getInvalidCard();
        payWithCard(cardData);
    }

    private void payWithOnlineTransfer() {
        checkoutPage.getRadioButtonsOnCheckoutPage(Constants.PaymentMethods.ONLINE_TRANSFER).click();
        checkoutPage.getOnlineTransferDropdown().click();
        checkoutPage.getOnlineBankPaymentOption(Constants.PaymentMethods.BLIK).click();
        //click on the pay button on chekout page
        checkoutPage.getOnlineTransferPayButton().click();
        //wait for the przelewy24 pay button to be visible
        checkoutPage.getPrzelewy24PayButton().waitUntilVisible(Duration.ofSeconds(15));
        checkoutPage.getPrzelewy24PayButton().click();
    }

    public void verifyThatUserIsOnThePaymentPage(String userType) {
        //fillInBillingData(userType);
        clickOnContinueButton();
        waitABit(15656);
        selectCourierDeliveryMethod();
        clickOnContinueButton();
        clickOnAgreeToTermsCheckbox();
        payWithBlik();
        orderSuccessActions.verifyOrderSuccessPage();
    }

    @Step
    public void fillInShippingDataWithCompany(String customerType, boolean includeCompanyName) {
        //wait for the page to load
        checkoutPage.getFirstNameInput().waitUntilVisible(Duration.ofSeconds(10));
        // Initialize helper if not already done
        if (checkoutFormHelper == null) {
            checkoutFormHelper = new CheckoutFormHelper(checkoutPage);
        }

        CustomerType customer = CustomerType.fromValue(customerType);
        ShippingAddress shippingData = TestDataFactory.getShippingAddress(customer, includeCompanyName);
        checkoutFormHelper.fillShippingAddress(shippingData);
    }

    @Step
    public void fillInShippingData(String customerType) {
        //wait for the page to load
        checkoutPage.getFirstNameInput().waitUntilVisible(Duration.ofSeconds(10));
        // Initialize helper if not already done
        if (checkoutFormHelper == null) {
            checkoutFormHelper = new CheckoutFormHelper(checkoutPage);
        }

        CustomerType customer = CustomerType.fromValue(customerType);
        ShippingAddress shippingData = TestDataFactory.getShippingAddress(customer);
        checkoutFormHelper.fillShippingAddress(shippingData);
    }


    @Step
    public void fillInBillingData(String customerType) {
        //wait for the page to load
        checkoutPage.getFirstNameInput().waitUntilVisible(Duration.ofSeconds(10));
        // Initialize helper if not already done
        if (checkoutFormHelper == null) {
            checkoutFormHelper = new CheckoutFormHelper(checkoutPage);
        }

        CustomerType customer = CustomerType.fromValue(customerType);
        BillingAddress billingData = TestDataFactory.getBillingAddress(customer);
        checkoutFormHelper.fillBillingAddress(billingData);
    }

    @Step
    public void fillInBillingDataWithSpecificAddress(BillingAddress billingData) {
        //wait for the page to load
        checkoutPage.getFirstNameInput().waitUntilVisible(Duration.ofSeconds(10));
        // Initialize helper if not already done
        if (checkoutFormHelper == null) {
            checkoutFormHelper = new CheckoutFormHelper(checkoutPage);
        }

        checkoutFormHelper.fillBillingAddress(billingData);
    }

    public void selectDeliveryMethod(String deliveryMethod) {
        // Check if change delivery method button is present and visible
        if (checkoutPage.changeDeliveryMethodButton().isPresent() &&
                checkoutPage.changeDeliveryMethodButton().isVisible()) {
            // Click the change button to show delivery method options
            checkoutPage.changeDeliveryMethodButton().click();
            waitABit(2000); // Wait for options to become visible
        }

        //wait until the delivery method is visible
        checkoutPage.getRadioButtonsOnCheckoutPage(deliveryMethod).waitUntilVisible(Duration.ofSeconds(10));
        switch (deliveryMethod) {
            case Constants.DeliveryMethods.PERSONAL_PICKUP:
                selectPickUpDeliveryMethod();
                break;
            case Constants.DeliveryMethods.COURIER:
                selectCourierDeliveryMethod();
                break;
            case Constants.DeliveryMethods.INPOST_PARCEL_LOCKER:
                selectInpostPaczkomatDeliveryMethod();
                break;
            default:
                throw new IllegalArgumentException("Unknown delivery method: " + deliveryMethod);
        }
    }

    public void chooseToPayWith(String paymentMethod) {
        // Store payment method in Storage for later verification
        Storage.getStorage().saveValue(IStorageKey.PAYMENT_METHOD, paymentMethod);

        //use a switch case to select the payment method
        switch (paymentMethod) {
            case "Blik":
                payWithBlik();
                break;
            case "Card":
                payWithCard();
                break;
            case "Online transfer":
                payWithOnlineTransfer();
                break;
        }
    }

    public void verifyOrderSuccessPage() {
        orderSuccessActions.verifyOrderSuccessPage();
    }

    public void verifyOrderIsNotCreated() {
        orderSuccessActions.verifyOrderSuccessPageIsNotDisplayed();
    }

    public void verifyDeliveryCostIsZeroAtCheckout() {
        SoftAssertions softAssertions = new SoftAssertions();
        //verify the delivery cost is zero
        softAssertions.assertThat(checkoutPage.getDeliveryPriceHeader().getText()).isEqualTo(Constants.PriceFormats.DELIVERY_HEADER_WITH_ZERO);
        softAssertions.assertAll();
    }

    public void verifyDeliveryCostIsZeroOnTheRightSideOf(String deliveryMethod) {
        SoftAssertions softAssertions = new SoftAssertions();
        //verify the delivery cost is zero
        softAssertions.assertThat(checkoutPage.deliveryCostOnDeliveryMethodSelection(deliveryMethod).getText()).isEqualTo(Constants.PriceFormats.ZERO_PRICE);
        softAssertions.assertAll();
    }

    public void clickOnTheSameAsDeliveryCheckbox() {
        checkoutPage.sameAsDeliveryCheckbox().click();
    }

    public void enterInvalidBlikCode() {
        //select the blik payment method
        checkoutPage.getRadioButtonsOnCheckoutPage(Constants.PaymentMethods.BLIK_WITH_CODE).click();
        //enter invalid blik code using test data factory
        String invalidBlikCode = TestDataFactory.generateInvalidBlikCode();
        checkoutPage.getBlikPaymentInput().sendKeys(invalidBlikCode);
        //click on the pay button
        checkoutPage.getBlikPayButton().click();
    }

    public void verifyErrorMessageForInvalidBlikCode() {
        SoftAssertions softAssertions = new SoftAssertions();
        //verify the error message is displayed
        softAssertions.assertThat(checkoutPage.getInvalidBlikCodeMessage().getText()).isEqualTo(Constants.ErrorMessages.INVALID_BLIK_CODE);
        softAssertions.assertAll();
    }

    public void selectPaymentMethod(String paymentMethod) {
        switch (paymentMethod) {
            case "Blik":
                checkoutPage.getRadioButtonsOnCheckoutPage(Constants.PaymentMethods.BLIK_WITH_CODE).click();
                break;
            case "Card":
                checkoutPage.getRadioButtonsOnCheckoutPage(Constants.PaymentMethods.BANK_CARD).click();
                break;
            case "Online transfer":
                checkoutPage.getRadioButtonsOnCheckoutPage(Constants.PaymentMethods.ONLINE_TRANSFER).click();
                break;
        }
    }


    public void selectOnlineBankPaymentOption() {
        checkoutPage.getOnlineTransferDropdown().click();
        checkoutPage.getOnlineBankPaymentOption(Constants.PaymentMethods.BANK_MILLENNIUM).click();
        //click on the pay button on chekout page
        checkoutPage.getOnlineTransferPayButton().click();
        //wait for the przelewy24 pay button to be visible
        checkoutPage.getPrzelewy24PayButton().waitUntilVisible(Duration.ofSeconds(15));
    }

    public void navigateBackToCheckoutPage() {
        Serenity.getDriver().navigate().back();
        //if the checkout page is not visible, click on the back button again
        if (!checkoutPage.getFirstNameInput().isVisible()) {
            Serenity.getDriver().navigate().back();
        }
    }

    public void payWithInvalidCardNumber() {
        PaymentTestData.CardPaymentData cardData = TestDataFactory.getInvalidCard();
        payWithCard(cardData);
    }

    public void verifyErrorMessageForInvalidCardNumber() {
        SoftAssertions softAssertions = new SoftAssertions();
        //change the frame to default
        switchToDefaultContent();
        //or click on some other page area
        checkoutPage.getInvalidCardNumberMessage().waitUntilVisible(Duration.ofSeconds(10));
        //verify the error message is displayed
        softAssertions.assertThat(checkoutPage.getInvalidCardNumberMessage().getText()).isEqualTo(Constants.ErrorMessages.INVALID_CARD_NUMBER_ERROR);
        softAssertions.assertAll();
    }

    public void clickOnChangeOrderDetailsButton() {
        checkoutPage.getChangeOrderDetailsButton().click();
        waitABit(10000);
    }


    public void selectOneSavedShippingAddressFromTheList() {
        SoftAssertions softAssertions = new SoftAssertions();

        // Save the current address as previous address if it exists
        String currentAddress = Storage.getStorage().getValue(IStorageKey.SELECTED_SHIPPING_ADDRESS);
        if (currentAddress != null && !currentAddress.isEmpty()) {
            Storage.getStorage().saveValue(IStorageKey.PREVIOUS_SHIPPING_ADDRESS, currentAddress);
        }

        // Get the total number of available addresses
        int totalAddresses = checkoutPage.getSavedShippingAddressList().getSize();

        // If all addresses have been visited, reset the tracking
        if (selectedShippingAddressIndices.size() >= totalAddresses) {
            selectedShippingAddressIndices.clear();
        }

        // Get a random unvisited index
        int selectedIndex;
        do {
            selectedIndex = new Random().nextInt(totalAddresses);
        } while (selectedShippingAddressIndices.contains(selectedIndex) && selectedShippingAddressIndices.size() < totalAddresses);

        // Mark this index as visited
        selectedShippingAddressIndices.add(selectedIndex);

        // Click on the selected address
        checkoutPage.getSavedShippingAddressList().clickByIndex(selectedIndex);

        // Save the text values of the selected address to storage
        Storage.getStorage().saveValue(IStorageKey.SELECTED_SHIPPING_ADDRESS,
                checkoutPage.getSavedShippingAddressList().getElementByIndex(selectedIndex).getText());

        // Verify the address is selected
        softAssertions.assertThat(checkoutPage.getSelectedSavedAddressCheckMark().isDisplayed()).isTrue();
        softAssertions.assertAll();
    }

    /**
     * Select a saved billing address from the list based on a customer type
     *
     * @param customerType The type of customer ("company" or "person")
     */
    public void selectOneSavedBillingAddressFromTheList(String customerType) {
        SoftAssertions softAssertions = new SoftAssertions();

        // First, select the appropriate billing type option
        if ("company".equalsIgnoreCase(customerType)) {
            checkoutPage.getInvoiceForCompanyOption().click();
        } else if ("person".equalsIgnoreCase(customerType)) {
            checkoutPage.getInvoiceForPrivateOption().click();
        } else {
            throw new IllegalArgumentException("Unknown customer type: " + customerType + ". Expected 'company' or 'person'.");
        }

        // Wait for the address list to be updated based on selection
        waitABit(2000);

        // Save the current address as previous address if it exists
        String currentAddress = Storage.getStorage().getValue(IStorageKey.SELECTED_BILLING_ADDRESS);
        if (currentAddress != null && !currentAddress.isEmpty()) {
            Storage.getStorage().saveValue(IStorageKey.PREVIOUS_BILLING_ADDRESS, currentAddress);
        }

        // Get the total number of available addresses
        int totalAddresses = checkoutPage.getSavedBillingAddressList().getSize();

        // If no addresses are available, throw an exception
        if (totalAddresses == 0) {
            throw new RuntimeException("No saved billing addresses found for " + customerType + " type.");
        }

        // If only one address is available, we can't select a different one
        if (totalAddresses == 1) {
            checkoutPage.getSavedBillingAddressList().clickByIndex(0);
            Storage.getStorage().saveValue(IStorageKey.SELECTED_BILLING_ADDRESS,
                    checkoutPage.getSavedBillingAddressList().getElementByIndex(0).getText());
            softAssertions.assertThat(checkoutPage.getSelectedSavedAddressCheckMark().isDisplayed()).isTrue();
            softAssertions.assertAll();
            waitABit(3000);
            return;
        }

        // Get the previously selected index if available
        Integer previousIndex = null;
        for (int i = 0; i < totalAddresses; i++) {
            String addressText = checkoutPage.getSavedBillingAddressList().getElementByIndex(i).getText();
            if (addressText.equals(currentAddress)) {
                previousIndex = i;
                break;
            }
        }

        // Select a different index than the previous one
        int selectedIndex;
        if (previousIndex != null) {
            // Choose any index except the previous one
            do {
                selectedIndex = new Random().nextInt(totalAddresses);
            } while (selectedIndex == previousIndex);
        } else {
            // No previous selection, choose any index
            selectedIndex = new Random().nextInt(totalAddresses);
        }

        // Click on the selected address
        checkoutPage.getSavedBillingAddressList().clickByIndex(selectedIndex);

        // Save the text values of the selected address to storage
        Storage.getStorage().saveValue(IStorageKey.SELECTED_BILLING_ADDRESS,
                checkoutPage.getSavedBillingAddressList().getElementByIndex(selectedIndex).getText());

        // Verify the address is selected
        softAssertions.assertThat(checkoutPage.getSelectedSavedAddressCheckMark().isDisplayed()).isTrue();
        softAssertions.assertAll();
        waitABit(3000);
    }

    public void verifyTheShippingAndBillingAddressesAreTheSame() {
        // Wait a bit for stability
        waitABit(3000);
        SoftAssertions softAssertions = new SoftAssertions();

        // Get the selected shipping address from storage
        String selectedShippingAddressFromStorage = Storage.getStorage().getValue(IStorageKey.SELECTED_SHIPPING_ADDRESS);

        // Get the selected billing and shipping address from the page
        String selectedBillingAddress = checkoutPage.getSelectedBillingAddress().getText();
        String selectedShippingAddress = checkoutPage.getSelectedShippingAddress().getText();

        // Normalize NIP format: handle format differences between storage and page display
        if (selectedShippingAddressFromStorage != null && selectedBillingAddress != null) {
            if (selectedShippingAddressFromStorage.contains("NIP ") && selectedBillingAddress.contains("NIP: ")) {
                selectedShippingAddressFromStorage = selectedShippingAddressFromStorage.replace("NIP ", "NIP: ");
            } else if (selectedShippingAddressFromStorage.contains("NIP: ") && selectedBillingAddress.contains("NIP ")) {
                selectedShippingAddressFromStorage = selectedShippingAddressFromStorage.replace("NIP: ", "NIP ");
            }
        }

        // Verify the addresses are the same
        softAssertions.assertThat(selectedBillingAddress).isEqualTo(selectedShippingAddressFromStorage);
        softAssertions.assertThat(selectedShippingAddress).isEqualTo(selectedShippingAddressFromStorage);
        softAssertions.assertAll();
    }

    public void uncheckTheSameAsDeliveryCheckbox() {
        //uncheck the checkbox if it's checked
        if (checkoutPage.sameAsDeliveryCheckbox().isSelected()) {
            checkoutPage.sameAsDeliveryCheckbox().click();
        }
    }

    public void verifyTheShippingAndBillingAddressesAreDifferent() {
        waitABit(3000);
        SoftAssertions softAssertions = new SoftAssertions();
        String selectedShippingAddressFromStorage = Storage.getStorage().getValue(IStorageKey.SELECTED_SHIPPING_ADDRESS);
        String selectedBillingAddressFromStorage = Storage.getStorage().getValue(IStorageKey.SELECTED_BILLING_ADDRESS);
        String selectedBillingAddress = checkoutPage.getSelectedBillingAddress().getText();
        String selectedShippingAddress = checkoutPage.getSelectedShippingAddress().getText();

        // Normalize NIP format: handle format differences between storage and page display
        if (selectedBillingAddressFromStorage != null && selectedBillingAddress != null) {
            if (selectedBillingAddressFromStorage.contains("NIP ") && selectedBillingAddress.contains("NIP: ")) {
                selectedBillingAddressFromStorage = selectedBillingAddressFromStorage.replace("NIP ", "NIP: ");
            } else if (selectedBillingAddressFromStorage.contains("NIP: ") && selectedBillingAddress.contains("NIP ")) {
                selectedBillingAddressFromStorage = selectedBillingAddressFromStorage.replace("NIP: ", "NIP ");
            }
        }

        softAssertions.assertThat(selectedBillingAddress).isEqualTo(selectedBillingAddressFromStorage);
        softAssertions.assertThat(selectedShippingAddress).isEqualTo(selectedShippingAddressFromStorage);
        softAssertions.assertThat(selectedBillingAddress).isNotEqualTo(selectedShippingAddress);
        softAssertions.assertThat(selectedShippingAddress).isNotEqualTo(selectedBillingAddressFromStorage);
        softAssertions.assertAll();
    }

    public void verifyTheNewlySelectedShippingAddressIsDifferentFromThePreviousOne() {
        waitABit(3000);
        SoftAssertions softAssertions = new SoftAssertions();
        String previousShippingAddress = Storage.getStorage().getValue(IStorageKey.PREVIOUS_SHIPPING_ADDRESS);
        String currentShippingAddress = checkoutPage.getSelectedShippingAddress().getText();
        String newlySelectedShippingAddress = Storage.getStorage().getValue(IStorageKey.SELECTED_SHIPPING_ADDRESS);
        softAssertions.assertThat(currentShippingAddress).isNotEqualTo(previousShippingAddress);
        softAssertions.assertThat(currentShippingAddress).isEqualTo(newlySelectedShippingAddress);
        softAssertions.assertAll();
    }

    public void verifyTheNewlySelectedBillingAddressIsDifferentFromThePreviousOne() {
        SoftAssertions softAssertions = new SoftAssertions();
        String previousBillingAddress = Storage.getStorage().getValue(IStorageKey.PREVIOUS_BILLING_ADDRESS);
        String currentBillingAddress = checkoutPage.getSelectedBillingAddress().getText();
        String newlySelectedBillingAddress = Storage.getStorage().getValue(IStorageKey.SELECTED_BILLING_ADDRESS);

        // Normalize NIP format: handle format differences between storage and page display
        if (newlySelectedBillingAddress != null && currentBillingAddress != null) {
            if (newlySelectedBillingAddress.contains("NIP ") && currentBillingAddress.contains("NIP: ")) {
                newlySelectedBillingAddress = newlySelectedBillingAddress.replace("NIP ", "NIP: ");
            } else if (newlySelectedBillingAddress.contains("NIP: ") && currentBillingAddress.contains("NIP ")) {
                newlySelectedBillingAddress = newlySelectedBillingAddress.replace("NIP: ", "NIP ");
            }
        }

        // Also normalize the previous billing address for comparison
        if (previousBillingAddress != null && currentBillingAddress != null) {
            if (previousBillingAddress.contains("NIP ") && currentBillingAddress.contains("NIP: ")) {
                previousBillingAddress = previousBillingAddress.replace("NIP ", "NIP: ");
            } else if (previousBillingAddress.contains("NIP: ") && currentBillingAddress.contains("NIP ")) {
                previousBillingAddress = previousBillingAddress.replace("NIP: ", "NIP ");
            }
        }

        softAssertions.assertThat(currentBillingAddress).isNotEqualTo(previousBillingAddress);
        softAssertions.assertThat(currentBillingAddress).isEqualTo(newlySelectedBillingAddress);
        softAssertions.assertAll();
    }

    public void verifyTheNewlySelectedShippingAndBillingAddressesAreDifferentFromThePreviousOnes() {
    }

    public void checkTheSameAsDeliveryCheckbox() {
        //check the checkbox if it's not checked
        if (!checkoutPage.sameAsDeliveryCheckbox().isSelected()) {
            checkoutPage.sameAsDeliveryCheckbox().click();
        }
        waitABit(3000);
    }

    public void clickOnAddNewShippingAddressButton() {
        waitABit(3000);
        //scrill to the button
        checkoutPage.addNewShippingAddressButton().scrollElementToCenter();
        checkoutPage.addNewShippingAddressButton().click();
    }

    /**
     * Add a new shipping address for a logged-in user
     * Similar to fillInShippingData but skips the email field
     *
     * @param customerType The type of customer ("person" or "company")
     */
    public void addNewShippingAddress(String customerType) {
        checkoutPage.getFirstNameInput().waitUntilVisible(Duration.ofSeconds(10));
        // Initialize helper if not already done
        if (checkoutFormHelper == null) {
            checkoutFormHelper = new CheckoutFormHelper(checkoutPage);
        }

        // Get shipping data based on a customer type
        CustomerType customer = CustomerType.fromValue(customerType);
        ShippingAddress shippingData = TestDataFactory.getShippingAddress(customer);

        // Fill in shipping address fields manually, skipping email
        checkoutPage.getFirstNameInput().sendKeys(shippingData.getFirstName());
        checkoutPage.getLastNameInput().sendKeys(shippingData.getLastName());
        checkoutPage.getAddressInput().sendKeys(shippingData.getAddress());
        checkoutPage.getCityInput().sendKeys(shippingData.getCity());
        checkoutPage.getPostalCodeInput().sendKeys(shippingData.getPostalCode());
        checkoutPage.getPhoneInput().sendKeys(shippingData.getPhone());

        // Fill company name if provided (optional for individuals)
        if (shippingData.hasCompanyName()) {
            checkoutPage.getCompanyNameInput().sendKeys(shippingData.getCompanyName());
        }
        // Save this address to storage for later verification
        // Format to match the display format on the page
        String addressText = String.format("%s %s\n%s\n%s %s, Polska\n+48%s",
                shippingData.getFirstName(),
                shippingData.getLastName(),
                shippingData.getAddress(),
                shippingData.getPostalCode(),
                shippingData.getCity().toUpperCase(),
                shippingData.getPhone());

        if (shippingData.hasCompanyName()) {
            addressText = shippingData.getCompanyName() + "\n" + addressText;
        }

        Storage.getStorage().saveValue(IStorageKey.SELECTED_SHIPPING_ADDRESS, addressText);
    }

    /**
     * Add a new billing address for a logged-in user
     *
     * @param customerType The type of customer ("person" or "company")
     */
    public void addNewBillingAddress(String customerType) {
        // Wait for the page to load
        waitABit(3500);

        // Initialize helper if not already done
        if (checkoutFormHelper == null) {
            checkoutFormHelper = new CheckoutFormHelper(checkoutPage);
        }
        // Get billing data based on a customer type
        CustomerType customer = CustomerType.fromValue(customerType);
        BillingAddress billingData = TestDataFactory.getBillingAddress(customer);
        // Uncheck "same as delivery" if it's selected
        if (checkoutPage.sameAsDeliveryCheckbox().isSelected()) {
            checkoutPage.sameAsDeliveryCheckbox().click();
        }

        if (billingData.isCompanyBilling()) {
            // Fill company billing information
            checkoutPage.getInvoiceForCompanyOption().click();
            checkoutPage.getCompanyBillingNameInput().sendKeys(billingData.getCompanyName());
            checkoutPage.getNipBillingInput().sendKeys(billingData.getNip());
            checkoutPage.getAsCompanyStreetInput().sendKeys(billingData.getAddress());
            checkoutPage.getAsCompanyCityInput().sendKeys(billingData.getCity());
            checkoutPage.getAsCompanyPostCodeInput().sendKeys(billingData.getPostalCode());
            checkoutPage.companyPhoneInput().sendKeys(billingData.getPhone());

            // Save this address to storage for later verification
            String addressText = String.format("%s\nNIP: %s\n%s\n%s %s, Polska\n+48%s",
                    billingData.getCompanyName(),
                    billingData.getNip(),
                    billingData.getAddress(),
                    billingData.getPostalCode(),
                    billingData.getCity().toUpperCase(),
                    billingData.getPhone());

            Storage.getStorage().saveValue(IStorageKey.SELECTED_BILLING_ADDRESS, addressText);
        } else {
            // Fill individual billing information
            checkoutPage.getInvoiceForPrivateOption().click();
            // For individual billing, use company name as full name
            String[] nameParts = billingData.getCompanyName().split(" ", 2);
            String firstName = nameParts.length > 0 ? nameParts[0] : "Test";
            String lastName = nameParts.length > 1 ? nameParts[1] : "User";

            checkoutPage.getAsPersonGivenNameInput().sendKeys(firstName);
            checkoutPage.getAsPersonLastNameInput().sendKeys(lastName);
            checkoutPage.getAsPersonPhoneInput().sendKeys(billingData.getPhone());
            checkoutPage.getAsPersonAddressInput().sendKeys(billingData.getAddress());
            checkoutPage.getAsPersonPostalCodeInput().sendKeys(billingData.getPostalCode());
            checkoutPage.getAsPersonCityInput().sendKeys(billingData.getCity());

            // Save this address to storage for later verification
            String addressText = String.format("%s %s\n%s\n%s %s, Polska\n+48%s",
                    firstName,
                    lastName,
                    billingData.getAddress(),
                    billingData.getPostalCode(),
                    billingData.getCity().toUpperCase(),
                    billingData.getPhone());

            Storage.getStorage().saveValue(IStorageKey.SELECTED_BILLING_ADDRESS, addressText);
        }
    }

    public void clickOnAddNewBillingAddressButton() {
        checkoutPage.addNewBillingAddressButton().waitUntilVisible(Duration.ofSeconds(10));
        checkoutPage.addNewBillingAddressButton().click();
    }

    public void selectDifferentCountryForBillingAddress() {
        checkoutPage.getBillingCountrySelector().click();
        checkoutPage.getBillingCountrySelectByName("Niemcy").click();
        //save the selected country to storage
        Storage.getStorage().saveValue(IStorageKey.BILLING_COUNTRY, "Niemcy");
    }

    /**
     * Add a new billing address with a different country for a logged-in user
     *
     * @param customerType The type of customer ("person" or "company")
     */
    public void addNewBillingAddressWithDifferentCountry(String customerType) {
        // Wait for the page to load
        waitABit(3000);

        // Initialize helper if not already done
        if (checkoutFormHelper == null) {
            checkoutFormHelper = new CheckoutFormHelper(checkoutPage);
        }

        // Get billing data based on a customer type
        CustomerType customer = CustomerType.fromValue(customerType);
        BillingAddress billingData = TestDataFactory.getBillingAddress(customer);

        // Uncheck "same as delivery" if it's selected
        if (checkoutPage.sameAsDeliveryCheckbox().isSelected()) {
            checkoutPage.sameAsDeliveryCheckbox().click();
        }

        // Select a different country
        checkoutPage.getBillingCountrySelector().click();
        waitABit(1000);

        // Get all country options and select one randomly (excluding Poland)
        List<WebElementFacade> countryOptions = checkoutPage.getBillingCountryOptions().getAllElements();
        int totalCountries = countryOptions.size();

        // Default to Germany if random selection fails
        String selectedCountry = "Niemcy";

        if (totalCountries > 1) {
            // Try to select a random country that's not Poland
            for (int attempts = 0; attempts < 5; attempts++) {
                int randomIndex = new Random().nextInt(totalCountries);
                WebElement countryElement = countryOptions.get(randomIndex);
                String countryName = countryElement.getText();

                if (!countryName.equals("Polska")) {
                    selectedCountry = countryName;
                    countryElement.click();
                    break;
                }
            }
        } else {
            // If there's only one option or no options, use Germany
            checkoutPage.getBillingCountrySelectByName("Niemcy").click();
        }
        // Save the selected country to storage
        Storage.getStorage().saveValue(IStorageKey.BILLING_COUNTRY, selectedCountry);

        // Get country-specific phone and postal code from CountryTestData
        String phoneNumber = CountryTestData.getPhoneNumberForCountry(selectedCountry);
        String postalCode = CountryTestData.getPostalCodeForCountry(selectedCountry);

        if (billingData.isCompanyBilling()) {
            // Fill company billing information
            checkoutPage.getInvoiceForCompanyOption().click();
            checkoutPage.getCompanyBillingNameInput().sendKeys(billingData.getCompanyName());
            checkoutPage.getNipBillingInput().sendKeys(billingData.getNip());
            checkoutPage.getAsCompanyStreetInput().sendKeys(billingData.getAddress());
            checkoutPage.getAsCompanyCityInput().sendKeys(billingData.getCity());
            checkoutPage.getAsCompanyPostCodeInput().sendKeys(postalCode);
            checkoutPage.companyPhoneInput().sendKeys(phoneNumber);

            // Save this address to storage for later verification
            String addressText = String.format("%s\nNIP: %s\n%s\n%s %s, %s\n%s",
                    billingData.getCompanyName(),
                    billingData.getNip(),
                    billingData.getAddress(),
                    postalCode,
                    billingData.getCity().toUpperCase(),
                    selectedCountry,
                    phoneNumber);

            Storage.getStorage().saveValue(IStorageKey.SELECTED_BILLING_ADDRESS, addressText);
        } else {
            // Fill individual billing information
            checkoutPage.getInvoiceForPrivateOption().click();
            // For individual billing, use company name as full name
            String[] nameParts = billingData.getCompanyName().split(" ", 2);
            String firstName = nameParts.length > 0 ? nameParts[0] : "Test";
            String lastName = nameParts.length > 1 ? nameParts[1] : "User";

            checkoutPage.getAsPersonGivenNameInput().sendKeys(firstName);
            checkoutPage.getAsPersonLastNameInput().sendKeys(lastName);
            checkoutPage.getAsPersonPhoneInput().sendKeys(phoneNumber);
            checkoutPage.getAsPersonAddressInput().sendKeys(billingData.getAddress());
            checkoutPage.getAsPersonPostalCodeInput().sendKeys(postalCode);
            checkoutPage.getAsPersonCityInput().sendKeys(billingData.getCity());

            // Save this address to storage for later verification
            String addressText = String.format("%s %s\n%s\n%s %s, %s\n%s",
                    firstName,
                    lastName,
                    billingData.getAddress(),
                    postalCode,
                    billingData.getCity().toUpperCase(),
                    selectedCountry,
                    phoneNumber);

            Storage.getStorage().saveValue(IStorageKey.SELECTED_BILLING_ADDRESS, addressText);
        }

        waitABit(2000);
    }

    public void applyCouponCodeAtCheckout(String couponCode) {
        String totalPriceBeforeDiscount = cartPage.totalPriceInCart().getText();
        Storage.getStorage().saveValue(IStorageKey.TOTAL_CART_PRICE_BEFORE_DISCOUNT, totalPriceBeforeDiscount);
        cartPage.couponCodeInput().waitUntilVisible(Duration.ofSeconds(10));
        cartPage.couponCodeInput().clearText();
        cartPage.couponCodeInput().typeAndEnter(couponCode);
        waitABit(3000);

        Storage.getStorage().saveValue(IStorageKey.APPLIED_DISCOUNT_CODE, couponCode);
    }

    public void verifyCouponCodeIsAppliedAtCheckout() {
        SoftAssertions softAssertions = new SoftAssertions();
        cartPage.activeCouponCodeHeader().waitUntilVisible(Duration.ofSeconds(10));

        String expectedHeader = "Aktywny kod:";
        String actualHeader = cartPage.activeCouponCodeHeader().getText();
        softAssertions.assertThat(actualHeader).isEqualTo(expectedHeader);

        String appliedDiscountName = cartPage.appliedDiscountName().getText();
        softAssertions.assertThat(appliedDiscountName).isEqualTo("Test");
        cartPage.appliedDiscountHeader().waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(cartPage.appliedDiscountHeader().getText()).contains("Rabat");
        softAssertions.assertThat(cartPage.appliedDiscountValue().isVisible()).isTrue();
        String discountValue = cartPage.appliedDiscountValue().getText();
        String totalPriceInCart = cartPage.totalPriceInCart().getText();
        double discountValueDouble = Double.parseDouble(discountValue.replace(" zł", "").replace(",", "."));
        double currentTotalPriceValue = Double.parseDouble(totalPriceInCart.replace(" zł", "").replace(",", "."));
        softAssertions.assertThat(discountValueDouble).isEqualTo(currentTotalPriceValue);
        String totalPriceBeforeDiscount = Storage.getStorage().getValue(IStorageKey.TOTAL_CART_PRICE_BEFORE_DISCOUNT);
        if (totalPriceBeforeDiscount != null) {
            double originalPrice = Double.parseDouble(totalPriceBeforeDiscount.replace(" zł", "").replace(",", "."));
            softAssertions.assertThat(currentTotalPriceValue).isLessThan(originalPrice);
            softAssertions.assertThat(currentTotalPriceValue).isEqualTo(originalPrice / 2);
        }

        softAssertions.assertAll();
    }

    public void verifyCouponCodeErrorAtCheckout() {
        SoftAssertions softAssertions = new SoftAssertions();
        cartPage.invalidDiscountCodeMessage().waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(cartPage.invalidDiscountCodeMessage().isVisible()).isTrue();
        softAssertions.assertThat(cartPage.invalidDiscountCodeMessage().getText()).contains("Nieprawidłowy kod rabatowy");
        softAssertions.assertAll();
    }

    public void verifyCouponCodeIsPreservedAtCheckout() {
        SoftAssertions softAssertions = new SoftAssertions();

        // Wait for and verify the active coupon code header
        cartPage.activeCouponCodeHeader().waitUntilVisible(Duration.ofSeconds(10));
        String expectedHeader = "Aktywny kod:";
        String actualHeader = cartPage.activeCouponCodeHeader().getText();
        softAssertions.assertThat(actualHeader).isEqualTo(expectedHeader);

        // Verify the applied discount name is displayed
        String appliedDiscountName = cartPage.appliedDiscountName().getText();
        softAssertions.assertThat(appliedDiscountName).isEqualTo("Test");

        // Verify discount header and value are visible and contain expected text
        cartPage.appliedDiscountHeader().waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(cartPage.appliedDiscountHeader().getText()).contains("Rabat");
        softAssertions.assertThat(cartPage.appliedDiscountValue().isVisible()).isTrue();
        softAssertions.assertThat(cartPage.appliedDiscountValue().getText()).contains("zł");

        // Verify that the discount is actually being applied to the total price
        String discountValue = cartPage.appliedDiscountValue().getText();
        String totalPriceInCart = cartPage.totalPriceInCart().getText();
        double discountValueDouble = Double.parseDouble(discountValue.replace(" zł", "").replace(",", "."));
        double currentTotalPriceValue = Double.parseDouble(totalPriceInCart.replace(" zł", "").replace(",", "."));
        softAssertions.assertThat(discountValueDouble).isEqualTo(currentTotalPriceValue);

        // Verify the total price is less than the original price before discount
        String totalPriceBeforeDiscount = Storage.getStorage().getValue(IStorageKey.TOTAL_CART_PRICE_BEFORE_DISCOUNT);
        if (totalPriceBeforeDiscount != null) {
            double originalPrice = Double.parseDouble(totalPriceBeforeDiscount.replace(" zł", "").replace(",", "."));
            softAssertions.assertThat(currentTotalPriceValue).isLessThan(originalPrice);
        }

        softAssertions.assertAll();
    }

    public void verifyDiscountAmountIsDisplayedCorrectlyAtCheckout() {
        SoftAssertions softAssertions = new SoftAssertions();
        cartPage.appliedDiscountHeader().waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(cartPage.appliedDiscountHeader().getText()).contains("Rabat");
        softAssertions.assertThat(cartPage.appliedDiscountValue().isVisible()).isTrue();
        String discountValue = cartPage.appliedDiscountValue().getText();
        softAssertions.assertThat(discountValue).isNotEmpty();
        softAssertions.assertThat(discountValue).contains("zł");
        String totalPriceInCart = cartPage.totalPriceInCart().getText();
        double discountValueDouble = Double.parseDouble(discountValue.replace(" zł", "").replace(",", "."));
        double currentTotalPriceValue = Double.parseDouble(totalPriceInCart.replace(" zł", "").replace(",", "."));
        softAssertions.assertThat(discountValueDouble).isEqualTo(currentTotalPriceValue);
        String totalPriceBeforeDiscount = Storage.getStorage().getValue(IStorageKey.TOTAL_CART_PRICE_BEFORE_DISCOUNT);
        if (totalPriceBeforeDiscount != null) {
            double originalPrice = Double.parseDouble(totalPriceBeforeDiscount.replace(" zł", "").replace(",", "."));
            softAssertions.assertThat(currentTotalPriceValue).isLessThan(originalPrice);
        }
        softAssertions.assertAll();
    }

    public void verifySubtotalIsCalculatedCorrectlyAtCheckout() {
        SoftAssertions softAssertions = new SoftAssertions();
        cartPage.totalPriceInCart().waitUntilVisible(Duration.ofSeconds(10));
        String subtotal = cartPage.totalPriceInCart().getText();
        softAssertions.assertThat(subtotal).isNotEmpty();
        softAssertions.assertThat(subtotal).contains("zł");
        softAssertions.assertAll();
    }

    public void verifyFinalTotalIsCalculatedCorrectlyAtCheckout() {
        SoftAssertions softAssertions = new SoftAssertions();
        String totalPriceInCart = cartPage.totalPriceInCart().getText();
        softAssertions.assertThat(totalPriceInCart).isNotEmpty();
        softAssertions.assertThat(totalPriceInCart).contains("zł");
        if (cartPage.appliedDiscountValue().isVisible()) {
            String discountValue = cartPage.appliedDiscountValue().getText();
            double discountValueDouble = Double.parseDouble(discountValue.replace(" zł", "").replace(",", "."));
            double currentTotalPriceValue = Double.parseDouble(totalPriceInCart.replace(" zł", "").replace(",", "."));
            softAssertions.assertThat(discountValueDouble).isEqualTo(currentTotalPriceValue);
            String totalPriceBeforeDiscount = Storage.getStorage().getValue(IStorageKey.TOTAL_CART_PRICE_BEFORE_DISCOUNT);
            if (totalPriceBeforeDiscount != null) {
                double originalPrice = Double.parseDouble(totalPriceBeforeDiscount.replace(" zł", "").replace(",", "."));
                softAssertions.assertThat(currentTotalPriceValue).isEqualTo(originalPrice / 2);
            }
        }

        softAssertions.assertAll();
    }

    public void verifyNoCouponCodeIsAppliedAtCheckout() {
        SoftAssertions softAssertions = new SoftAssertions();
        softAssertions.assertThat(cartPage.appliedDiscountHeader().isVisible()).isFalse();
        softAssertions.assertThat(cartPage.activeCouponCodeHeader().isVisible()).isFalse();
        softAssertions.assertAll();
    }

    public void verifySubtotalEqualsFinalTotalAtCheckout() {
        SoftAssertions softAssertions = new SoftAssertions();
        String totalText = cartPage.totalPriceInCart().getText();
        softAssertions.assertThat(cartPage.appliedDiscountHeader().isVisible()).isFalse();
        softAssertions.assertThat(cartPage.activeCouponCodeHeader().isVisible()).isFalse();
        String totalPriceBeforeDiscount = Storage.getStorage().getValue(IStorageKey.TOTAL_CART_PRICE_BEFORE_DISCOUNT);
        if (totalPriceBeforeDiscount != null) {
            double originalPrice = Double.parseDouble(totalPriceBeforeDiscount.replace(" zł", "").replace(",", "."));
            double currentPrice = Double.parseDouble(totalText.replace(" zł", "").replace(",", "."));
            softAssertions.assertThat(Math.abs(currentPrice - originalPrice)).isLessThan(0.01);
        }
        softAssertions.assertThat(totalText).isNotEmpty();
        softAssertions.assertThat(totalText).contains("zł");
        softAssertions.assertAll();
    }

    public void goBackToDeliveryMethodSelection() {
        checkoutPage.changeDeliveryMethodButton().waitUntilVisible(Duration.ofSeconds(10));
        checkoutPage.changeDeliveryMethodButton().click();
        waitABit(2000);
    }

    public void removeCouponCodeAtCheckout() {
        cartPage.removeCouponButton().waitUntilVisible(Duration.ofSeconds(10));
        cartPage.removeCouponButton().waitUntilClickable(Duration.ofSeconds(10));
        boolean initialDiscountValueVisible = cartPage.appliedDiscountValue().isVisible();
        cartPage.removeCouponButton().click();
        waitABit(3000);
        int maxRetries = 3;
        int retryCount = 0;

        while (retryCount < maxRetries && cartPage.activeCouponCodeHeader().isVisible()) {
            retryCount++;
            waitABit(3000);

            if (cartPage.removeCouponButton().isVisible()) {
                cartPage.removeCouponButton().click();
                waitABit(2000);
            }
        }
        try {
            cartPage.activeCouponCodeHeader().waitUntilNotVisible(Duration.ofSeconds(5));
            if (initialDiscountValueVisible) {
                cartPage.appliedDiscountValue().waitUntilNotVisible(Duration.ofSeconds(5));
            }
            cartPage.appliedDiscountHeader().waitUntilNotVisible(Duration.ofSeconds(5));
            cartPage.couponCodeInput().waitUntilVisible(Duration.ofSeconds(5));

        } catch (Exception e) {
            waitABit(5000);
            if (cartPage.activeCouponCodeHeader().isVisible()) {
                throw new RuntimeException("Failed to remove discount code at checkout - active coupon header still visible after multiple attempts");
            }
        }
        verifyCouponCodeIsCompletelyRemovedAtCheckout();
    }

    private void verifyCouponCodeIsCompletelyRemovedAtCheckout() {
        if (cartPage.activeCouponCodeHeader().isVisible()) {
            throw new RuntimeException("Coupon code removal failed at checkout: Active coupon code header is still visible");
        }

        if (cartPage.appliedDiscountValue().isVisible()) {
            throw new RuntimeException("Coupon code removal failed at checkout: Applied discount value is still visible");
        }

        if (cartPage.appliedDiscountHeader().isVisible()) {
            throw new RuntimeException("Coupon code removal failed at checkout: Applied discount header is still visible");
        }

        if (!cartPage.couponCodeInput().isVisible()) {
            throw new RuntimeException("Coupon code removal failed at checkout: Coupon code input field is not visible");
        }
    }

    public void verifyCouponCodeIsRemovedAtCheckout() {
        SoftAssertions softAssertions = new SoftAssertions();
        waitABit(3000);
        softAssertions.assertThat(cartPage.activeCouponCodeHeader().isVisible()).isFalse();
        softAssertions.assertThat(cartPage.appliedDiscountHeader().isVisible()).isFalse();
        softAssertions.assertThat(cartPage.appliedDiscountValue().isVisible()).isFalse();
        softAssertions.assertThat(cartPage.couponCodeInput().isVisible()).isTrue();
        String currentTotalPrice = cartPage.totalPriceInCart().getText();
        String totalPriceBeforeDiscount = Storage.getStorage().getValue(IStorageKey.TOTAL_CART_PRICE_BEFORE_DISCOUNT);
        if (totalPriceBeforeDiscount != null) {
            double originalPrice = Double.parseDouble(totalPriceBeforeDiscount.replace(" zł", "").replace(",", "."));
            double currentPrice = Double.parseDouble(currentTotalPrice.replace(" zł", "").replace(",", "."));
            softAssertions.assertThat(Math.abs(currentPrice - originalPrice)).isLessThan(0.01);
        }

        softAssertions.assertAll();
    }

    /**
     * Fill shipping form with empty email field for validation testing
     */
    public void fillShippingFormWithEmptyEmail() {
        getCheckoutFormHelper().fillShippingFormWithEmptyEmail();
    }

    /**
     * Fill shipping form with empty first name field for validation testing
     */
    public void fillShippingFormWithEmptyFirstName() {
        getCheckoutFormHelper().fillShippingFormWithEmptyFirstName();
    }

    /**
     * Fill shipping form with empty last name field for validation testing
     */
    public void fillShippingFormWithEmptyLastName() {
        getCheckoutFormHelper().fillShippingFormWithEmptyLastName();
    }

    /**
     * Fill shipping form with empty street address field for validation testing
     */
    public void fillShippingFormWithEmptyStreetAddress() {
        getCheckoutFormHelper().fillShippingFormWithEmptyStreetAddress();
    }

    /**
     * Fill shipping form with empty city field for validation testing
     */
    public void fillShippingFormWithEmptyCity() {
        getCheckoutFormHelper().fillShippingFormWithEmptyCity();
    }

    /**
     * Fill shipping form with empty postal code field for validation testing
     */
    public void fillShippingFormWithEmptyPostalCode() {
        getCheckoutFormHelper().fillShippingFormWithEmptyPostalCode();
    }

    /**
     * Fill shipping form with empty phone number field for validation testing
     */
    public void fillShippingFormWithEmptyPhoneNumber() {
        getCheckoutFormHelper().fillShippingFormWithEmptyPhoneNumber();
    }

    /**
     * Fill shipping form with invalid phone number for validation testing
     */
    public void fillShippingFormWithInvalidPhoneNumber() {
        getCheckoutFormHelper().fillShippingFormWithInvalidPhoneNumber();
    }

    /**
     * Fill shipping form with invalid postal code for validation testing
     */
    public void fillShippingFormWithInvalidPostalCode() {
        getCheckoutFormHelper().fillShippingFormWithInvalidPostalCode();
    }

    /**
     * Fill complete shipping form with valid data for validation testing
     * Clears all fields first to ensure no invalid data remains
     */
    public void fillCompleteShippingFormWithValidData() {
        getCheckoutFormHelper().fillCompleteShippingFormWithValidData();
    }

    // ========== BILLING FORM VALIDATION METHODS ==========

    /**
     * Fill billing form with empty company name for validation testing
     */
    public void fillBillingFormWithEmptyCompanyName() {
        getCheckoutFormHelper().fillBillingFormWithEmptyCompanyName();
    }

    /**
     * Fill billing form with empty street address for validation testing
     */
    public void fillBillingFormWithEmptyStreetAddress() {
        getCheckoutFormHelper().fillBillingFormWithEmptyStreetAddress();
    }

    /**
     * Fill billing form with empty city for validation testing
     */
    public void fillBillingFormWithEmptyCity() {
        getCheckoutFormHelper().fillBillingFormWithEmptyCity();
    }

    /**
     * Fill billing form with empty NIP for validation testing
     */
    public void fillBillingFormWithEmptyNIP() {
        getCheckoutFormHelper().fillBillingFormWithEmptyNIP();
    }

    /**
     * Fill billing form with empty postal code for validation testing
     */
    public void fillBillingFormWithEmptyPostalCode() {
        getCheckoutFormHelper().fillBillingFormWithEmptyPostalCode();
    }

    /**
     * Fill billing form with invalid postal code for validation testing
     */
    public void fillBillingFormWithInvalidPostalCode() {
        getCheckoutFormHelper().fillBillingFormWithInvalidPostalCode();
    }

    /**
     * Fill complete billing form with valid data for validation testing
     * Clears all fields first to ensure no invalid data remains
     */
    public void fillCompleteBillingFormWithValidData() {
        getCheckoutFormHelper().fillCompleteBillingFormWithValidData();
    }

    // ========== VALIDATION VERIFICATION METHODS ==========

    /**
     * Verify that error message for empty required field is displayed
     */
    public void verifyEmptyRequiredFieldErrorMessage() {
        SoftAssertions softAssertions = new SoftAssertions();
        checkoutPage.errorMessageForEmptyRequiredField().waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(checkoutPage.errorMessageForEmptyRequiredField().isVisible()).isTrue();
        softAssertions.assertThat(checkoutPage.errorMessageForEmptyRequiredField().getText())
                .isEqualTo(Constants.ValidationMessages.FIELD_REQUIRED);
        softAssertions.assertAll();
    }

    /**
     * Verify that error message before continue button is displayed
     */
    public void verifyErrorMessageBeforeContinueButton() {
        SoftAssertions softAssertions = new SoftAssertions();
        checkoutPage.errorMessageBeforeContinueButton().waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(checkoutPage.errorMessageBeforeContinueButton().isVisible()).isTrue();
        softAssertions.assertThat(checkoutPage.errorMessageBeforeContinueButton().getText())
                .isEqualTo(Constants.ValidationMessages.DATA_ERRORS_SAVE_MESSAGE);
        softAssertions.assertAll();
    }

    /**
     * Verify that error message for invalid postal code is displayed
     */
    public void verifyInvalidPostalCodeErrorMessage() {
        SoftAssertions softAssertions = new SoftAssertions();
        checkoutPage.errorMessageForInvalidPostalCode().waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(checkoutPage.errorMessageForInvalidPostalCode().isVisible()).isTrue();
        softAssertions.assertThat(checkoutPage.errorMessageForInvalidPostalCode().getText())
                .isEqualTo(Constants.ValidationMessages.INVALID_POSTAL_CODE);
        softAssertions.assertAll();
    }

    /**
     * Verify that error message for invalid phone number is displayed
     */
    public void verifyInvalidPhoneNumberErrorMessage() {
        SoftAssertions softAssertions = new SoftAssertions();
        checkoutPage.errorMessageForInvalidPhoneNumber().waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(checkoutPage.errorMessageForInvalidPhoneNumber().isVisible()).isTrue();
        softAssertions.assertThat(checkoutPage.errorMessageForInvalidPhoneNumber().getText())
                .isEqualTo(Constants.ValidationMessages.INVALID_PHONE);
        softAssertions.assertAll();
    }

    /**
     * Verify that error message for invalid email is displayed
     */
    public void verifyInvalidEmailErrorMessage() {
        SoftAssertions softAssertions = new SoftAssertions();
        checkoutPage.errorMessageForInvalidEmail().waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(checkoutPage.errorMessageForInvalidEmail().isVisible()).isTrue();
        softAssertions.assertThat(checkoutPage.errorMessageForInvalidEmail().getText())
                .isEqualTo(Constants.ValidationMessages.INVALID_EMAIL);
        softAssertions.assertAll();
    }

    /**
     * Verify that continue button is disabled by checking the "disabled" attribute or button state
     */
    public void verifyContinueButtonIsDisabled() {
        SoftAssertions softAssertions = new SoftAssertions();
        checkoutPage.getContinueButton().waitUntilVisible(Duration.ofSeconds(10));

        // Check if button is disabled through attribute or enabled state
        String disabledAttribute = checkoutPage.getContinueButton().getAttribute("disabled");
        boolean isEnabled = checkoutPage.getContinueButton().isEnabled();

        // Button is considered disabled if it has disabled attribute or is not enabled
        boolean isDisabled = (disabledAttribute != null) || !isEnabled;

        softAssertions.assertThat(isDisabled)
                .describedAs("Continue button should be disabled when password validation fails")
                .isTrue();
        softAssertions.assertAll();
    }

    /**
     * Verify that continue button is enabled by checking the button state
     */
    public void verifyContinueButtonIsEnabled() {
        SoftAssertions softAssertions = new SoftAssertions();
        checkoutPage.getContinueButton().waitUntilVisible(Duration.ofSeconds(10));

        // Check if button is enabled
        boolean isEnabled = checkoutPage.getContinueButton().isEnabled();
        String disabledAttribute = checkoutPage.getContinueButton().getAttribute("disabled");

        // Button is considered enabled if it's enabled and has no disabled attribute
        boolean isActuallyEnabled = isEnabled && (disabledAttribute == null);

        softAssertions.assertThat(isActuallyEnabled)
                .describedAs("Continue button should be enabled when all validation passes")
                .isTrue();
        softAssertions.assertAll();
    }

    /**
     * Try to click continue button to trigger validation (for testing validation errors)
     * This method handles the case when the button is disabled and validation errors appear
     */
    public void tryClickContinueButtonForValidation() {
        checkoutPage.getContinueButton().waitUntilVisible(Duration.ofSeconds(10));
        checkoutPage.getContinueButton().click();
    }
    // ========== CHECKOUT LOGIN FUNCTIONALITY ==========

    /**
     * Click on the login link at checkout page
     */
    public void clickOnLoginLinkAtCheckout() {
        checkoutPage.getLoginLinkAtCheckout().waitUntilVisible(Duration.ofSeconds(10));
        checkoutPage.getLoginLinkAtCheckout().click();
        waitABit(2000);
    }

    /**
     * Login at checkout using account test credentials
     */
    public void loginAtCheckoutWithAccountTestCredentials() {
        UserCredentials credentials = TestDataFactory.getDefaultUserCredentials();
        checkoutPage.getLoginEmailInput().waitUntilVisible(Duration.ofSeconds(10));
        checkoutPage.getLoginEmailInput().sendKeys(credentials.getEmail());
        checkoutPage.getLoginPasswordInput().sendKeys(credentials.getPassword());
        checkoutPage.getLoginButton().click();
        waitABit(3000);
    }

    // ========== REGISTRATION AT CHECKOUT FUNCTIONALITY ==========

    /**
     * Select register at checkout checkbox
     */
    @Step
    public void selectRegisterAtCheckoutCheckbox() {
        checkoutPage.registerAtCheckoutCheckbox().waitUntilVisible(Duration.ofSeconds(10));
        checkoutPage.registerAtCheckoutCheckbox().click();
        waitABit(2000);
    }

    /**
     * Fill registration information at checkout (email and password)
     */
    @Step
    public void fillRegistrationInformationAtCheckout() {
        // Generate random email for registration
        String registrationEmail = RandomDataGenerator.generateEmail();
        String registrationPassword = Constants.TestData.REGISTRATION_PASSWORD;

        // Fill email field
        checkoutPage.getEmailInput().waitUntilVisible(Duration.ofSeconds(10));
        checkoutPage.getEmailInput().clearAndType(registrationEmail);

        // Fill password field (should be visible after selecting register checkbox)
        checkoutPage.getPasswordInput().waitUntilVisible(Duration.ofSeconds(10));
        checkoutPage.getPasswordInput().clearAndType(registrationPassword);

        // Store credentials for potential verification
        Storage.getStorage().saveValue(IStorageKey.REGISTRATION_EMAIL, registrationEmail);
        Storage.getStorage().saveValue(IStorageKey.REGISTRATION_PASSWORD, registrationPassword);
    }

    /**
     * Complete registration at checkout flow
     * This method combines selecting the checkbox and filling registration information
     */
    @Step
    public void registerAtCheckout() {
        selectRegisterAtCheckoutCheckbox();
        fillRegistrationInformationAtCheckout();
    }

    // ========== PASSWORD VALIDATION AT CHECKOUT ==========

    /**
     * Fill registration form with invalid password for validation testing
     * Fills all required fields except password to isolate password validation
     */
    @Step
    public void fillRegistrationFormWithInvalidPassword(String password) {
        // Generate random email for registration
        String registrationEmail = RandomDataGenerator.generateEmail();

        // Select register at checkout checkbox first
        selectRegisterAtCheckoutCheckbox();

        // Fill email field
        checkoutPage.getEmailInput().waitUntilVisible(Duration.ofSeconds(10));
        checkoutPage.getEmailInput().clearAndType(registrationEmail);

        // Fill password field with invalid password
        checkoutPage.getPasswordInput().waitUntilVisible(Duration.ofSeconds(10));
        checkoutPage.getPasswordInput().clearAndType(password);

        // Fill all other required fields to isolate password validation
        fillRequiredFieldsForPasswordValidation();

        // Store credentials for potential verification
        Storage.getStorage().saveValue(IStorageKey.REGISTRATION_EMAIL, registrationEmail);
        Storage.getStorage().saveValue(IStorageKey.REGISTRATION_PASSWORD, password);
    }

    /**
     * Fill all required fields except password for password validation testing
     */
    private void fillRequiredFieldsForPasswordValidation() {
        // Fill shipping information
        checkoutPage.getFirstNameInput().clearAndType("Test");
        checkoutPage.getLastNameInput().clearAndType("User");
        checkoutPage.getAddressInput().clearAndType("Test Address 123");
        checkoutPage.getCityInput().clearAndType("Test City");
        checkoutPage.getPostalCodeInput().clearAndType("00-000");
        checkoutPage.getPhoneInput().clearAndType("123456789");
    }

    /**
     * Verify invalid or empty password error message is displayed
     */
    @Step
    public void verifyInvalidOrEmptyPasswordErrorMessage() {
        SoftAssertions softAssertions = new SoftAssertions();
        checkoutPage.invalidOrEmptyPasswordErrorMessage().waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(checkoutPage.invalidOrEmptyPasswordErrorMessage().getText())
                .isEqualTo(Constants.ValidationMessages.INVALID_OR_EMPTY_PASSWORD_ERROR);
        softAssertions.assertAll();
    }

    /**
     * Verify minimum password length label is displayed
     */
    @Step
    public void verifyMinimumPasswordLengthLabel() {
        SoftAssertions softAssertions = new SoftAssertions();
        checkoutPage.minimumPasswordLengthLabel().waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(checkoutPage.minimumPasswordLengthLabel().getText())
                .isEqualTo(Constants.ValidationMessages.MINIMUM_PASSWORD_LENGTH_LABEL);
        softAssertions.assertAll();
    }

    /**
     * Fix invalid password by entering a valid 8-character password
     */
    @Step
    public void fixInvalidPasswordWithValidPassword() {
        checkoutPage.getPasswordInput().waitUntilVisible(Duration.ofSeconds(10));
        checkoutPage.getPasswordInput().clearAndType(Constants.TestData.VALID_EIGHT_CHAR_PASSWORD);
        Storage.getStorage().saveValue(IStorageKey.REGISTRATION_PASSWORD, Constants.TestData.VALID_EIGHT_CHAR_PASSWORD);
    }

    /**
     * Verify that register at checkout checkbox is not displayed for logged-in users
     */
    @Step
    public void verifyRegisterAtCheckoutCheckboxIsNotDisplayed() {
        SoftAssertions softAssertions = new SoftAssertions();
        // Wait a bit for the page to load completely
        waitABit(3000);
        // Verify that the register at checkout checkbox is not visible
        boolean isCheckboxVisible = checkoutPage.registerAtCheckoutCheckbox().isVisible();
        softAssertions.assertThat(isCheckboxVisible).isFalse();
        softAssertions.assertAll();
    }

    /**
     * Verify that login link at checkout is not displayed for logged-in users
     */
    @Step
    public void verifyLoginLinkAtCheckoutIsNotDisplayed() {
        SoftAssertions softAssertions = new SoftAssertions();
        waitABit(3000);
        boolean isLoginLinkVisible = checkoutPage.getLoginLinkAtCheckout().isVisible();
        softAssertions.assertThat(isLoginLinkVisible).isFalse();
        softAssertions.assertAll();
    }

    /**
     * Verify that shipping country selector is disabled at checkout
     */
    @Step
    public void verifyShippingCountrySelectorIsDisabled() {
        SoftAssertions softAssertions = new SoftAssertions();
        checkoutPage.shippingCountrySelector().waitUntilVisible(Duration.ofSeconds(10));
        String disabledAttribute = checkoutPage.shippingCountrySelector().getAttribute("disabled");
        boolean isDisabled = (disabledAttribute != null);
        boolean isEnabled = checkoutPage.shippingCountrySelector().isEnabled();
        softAssertions.assertThat(isDisabled || !isEnabled).isTrue();

        softAssertions.assertAll();
    }

    /**
     * Click on product link at checkout to navigate to PDP
     */
    @Step
    public void clickProductLinkAtCheckout() {
        checkoutPage.productLinkAtCheckout().waitUntilVisible(Duration.ofSeconds(10));
        checkoutPage.productLinkAtCheckout().click();
        waitABit(3000);
    }

    /**
     * Click on show cart link at checkout to navigate to cart
     */
    @Step
    public void clickShowCartLinkAtCheckout() {
        checkoutPage.showCartLinkAtCheckout().waitUntilVisible(Duration.ofSeconds(10));
        checkoutPage.showCartLinkAtCheckout().click();
        waitABit(3000);
    }

    /**
     * Verify user is on PDP after clicking product link from checkout
     */
    @Step
    public void verifyUserIsOnPDPFromCheckout() {
        SoftAssertions softAssertions = new SoftAssertions();
        String storedProductName = Storage.getStorage().getValue(IStorageKey.FIRST_PRODUCT_NAME);
        String storedProductPrice = Storage.getStorage().getValue(IStorageKey.FIRST_PRODUCT_PRICE);
        productPage.productName().waitUntilVisible(Duration.ofSeconds(10));
        productPage.productPrice().waitUntilVisible(Duration.ofSeconds(10));
        String actualProductName = productPage.productName().getText();
        softAssertions.assertThat(actualProductName).isEqualTo(storedProductName);
        String actualProductPrice;
        if (productPage.discountPrice().isVisible()) {
            actualProductPrice = productPage.discountPrice().getText();
        } else {
            actualProductPrice = productPage.productPrice().getText();
        }
        softAssertions.assertThat(actualProductPrice).isEqualTo(storedProductPrice);
        softAssertions.assertThat(productPage.addToCartButton().isVisible()).isTrue();
        softAssertions.assertThat(productPage.productImage().isVisible()).isTrue();
        softAssertions.assertAll();
    }

    /**
     * Verify user is on cart page after clicking show cart link from checkout
     */
    @Step
    public void verifyUserIsOnCartFromCheckout() {
        SoftAssertions softAssertions = new SoftAssertions();
        String storedProductName = Storage.getStorage().getValue(IStorageKey.FIRST_PRODUCT_NAME);
        String storedProductPrice = Storage.getStorage().getValue(IStorageKey.FIRST_PRODUCT_PRICE);
        cartPage.productNameInCart().waitUntilVisible(Duration.ofSeconds(10));
        String actualProductNameInCart = cartPage.productNameInCart().getElementByIndex(0).getText();
        softAssertions.assertThat(actualProductNameInCart).isEqualTo(storedProductName);
        String actualProductPriceInCart;
        if (!cartPage.discountPriceInCart().isEmpty()) {
            actualProductPriceInCart = cartPage.discountPriceInCart().getElementByIndex(0).getText();
        } else {
            actualProductPriceInCart = cartPage.productPriceInCart().getElementByIndex(0).getText();
        }
        softAssertions.assertThat(actualProductPriceInCart).isEqualTo(storedProductPrice);
        softAssertions.assertThat(cartPage.totalPriceInCart().isVisible()).isTrue();
        softAssertions.assertThat(cartPage.productQuantityInCart().getElementByIndex(0).isVisible()).isTrue();
        softAssertions.assertAll();
    }

    /**
     * Verify user is logged in at checkout
     */
    public void verifyUserIsLoggedInAtCheckout() {
        SoftAssertions softAssertions = new SoftAssertions();
        softAssertions.assertThat(checkoutPage.getSavedShippingAddressList().isEmpty()).isFalse();
        softAssertions.assertAll();
    }

    /**
     * Verify saved shipping addresses are listed
     */
    public void verifySavedShippingAddressesAreListed() {
        SoftAssertions softAssertions = new SoftAssertions();
        softAssertions.assertThat(checkoutPage.getSavedShippingAddressList().isEmpty()).isFalse();
        softAssertions.assertThat(checkoutPage.getSavedShippingAddressList().getSize()).isGreaterThan(0);
        softAssertions.assertAll();
    }

    /**
     * Verify saved billing addresses are listed
     */
    public void verifySavedBillingAddressesAreListed() {
        SoftAssertions softAssertions = new SoftAssertions();
        softAssertions.assertThat(checkoutPage.getSavedBillingAddressList().isEmpty()).isFalse();
        softAssertions.assertThat(checkoutPage.getSavedBillingAddressList().getSize()).isGreaterThan(0);
        softAssertions.assertAll();
    }

    /**
     * Verify free shipping threshold progress bar shows correct percentage at checkout
     */
    public void verifyFreeShippingThresholdProgressBarShowsCorrectPercentageAtCheckout() {
        SoftAssertions softAssertions = new SoftAssertions();
        String totalCartPrice = cartPage.totalPriceInCart().getText();
        double totalCartPriceValue = Double.parseDouble(totalCartPrice.replace(" zł", "").replace(",", "."));
        double percentage = (totalCartPriceValue / Constants.PriceFormats.FREE_SHIPPING_THRESHOLD) * 100;
        if (percentage > 100) {
            percentage = 100;
        }
        int roundedPercentage = (int) Math.round(percentage);
        String expectedWidth = String.valueOf(roundedPercentage);
        cartPage.freeShippingThresholdProcessBar(expectedWidth).waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(cartPage.freeShippingThresholdProcessBar(expectedWidth).isVisible()).isTrue();
        softAssertions.assertAll();
    }

    /**
     * Verify cart details are displayed correctly at checkout
     */
    public void verifyCartDetailsAreDisplayedCorrectlyAtCheckout() {
        SoftAssertions softAssertions = new SoftAssertions();
        cartPage.totalPriceInCart().waitUntilVisible(Duration.ofSeconds(10));
        String totalCartPrice = cartPage.totalPriceInCart().getText();
        softAssertions.assertThat(totalCartPrice).isNotEmpty().contains("zł");
        cartPage.totalPriceInCartHeader().waitUntilVisible(Duration.ofSeconds(10));
        String totalPriceHeader = cartPage.totalPriceInCartHeader().getText();
        softAssertions.assertThat(totalPriceHeader).isEqualTo(Constants.PriceFormats.TOTAL_PRICE_HEADER);
        softAssertions.assertThat(cartPage.vatHeader().getText()).isEqualTo(Constants.PriceFormats.VAT_COST_HEADER);
        double totalForVatCalculation = Double.parseDouble(totalCartPrice.replace(" zł", "").replace(",", "."));
        if (cartPage.shippingPriceInCart().isVisible()) {
            String shippingPrice = cartPage.shippingPriceInCart().getText();
            double shippingPriceValue = Double.parseDouble(shippingPrice.replace(" zł", "").replace(",", "."));
            totalForVatCalculation += shippingPriceValue;
        }
        String actualVatCost = cartPage.vatAmount().getText();
        double actualVatCostValue = Double.parseDouble(actualVatCost.replace(" zł", "").replace(",", "."));

        // VAT is included in total price, so VAT = Total × (23/123)
        double expectedVatValue = totalForVatCalculation * (23.0 / 123.0);
        softAssertions.assertThat(actualVatCostValue).isCloseTo(expectedVatValue, org.assertj.core.data.Offset.offset(0.01));

        softAssertions.assertAll();
    }

    /**
     * Verify all payment icons are displayed at checkout
     */
    public void verifyAllPaymentIconsAreDisplayedAtCheckout() {
        SoftAssertions softAssertions = new SoftAssertions();
        // Verify Apple Pay logo is displayed
        checkoutPage.getApplePayLogo().waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(checkoutPage.getApplePayLogo().isVisible()).isTrue();
        // Verify Google Pay logo is displayed
        checkoutPage.getGooglePayLogo().waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(checkoutPage.getGooglePayLogo().isVisible()).isTrue();
        // Verify BLIK logo is displayed
        checkoutPage.getBlikLogo().waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(checkoutPage.getBlikLogo().isVisible()).isTrue();
        // Verify Card logo is displayed
        checkoutPage.getCardLogo().waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(checkoutPage.getCardLogo().isVisible()).isTrue();
        // Verify Online Transfer logo is displayed
        checkoutPage.getOnlineTransferLogo().waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(checkoutPage.getOnlineTransferLogo().isVisible()).isTrue();

        softAssertions.assertAll();
    }

    /**
     * Verify user can select all payment methods and pay buttons are visible and enabled
     */
    public void verifyAllPaymentMethodsCanBeSelected() {
        SoftAssertions softAssertions = new SoftAssertions();

        // Verify BLIK payment method
        checkoutPage.getRadioButtonsOnCheckoutPage(Constants.PaymentMethods.BLIK_WITH_CODE).click();
        checkoutPage.getBlikPayButton().waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(checkoutPage.getBlikPayButton().isVisible()).isTrue();
        softAssertions.assertThat(checkoutPage.getBlikPayButton().isEnabled()).isTrue();

        // Verify Card payment method
        checkoutPage.getRadioButtonsOnCheckoutPage(Constants.PaymentMethods.BANK_CARD).click();
        checkoutPage.getCardPayButton().waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(checkoutPage.getCardPayButton().isVisible()).isTrue();
        softAssertions.assertThat(checkoutPage.getCardPayButton().isEnabled()).isTrue();

        // Verify Online transfer payment method
        checkoutPage.getRadioButtonsOnCheckoutPage(Constants.PaymentMethods.ONLINE_TRANSFER).click();
        checkoutPage.getOnlineTransferPayButton().waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(checkoutPage.getOnlineTransferPayButton().isVisible()).isTrue();
        softAssertions.assertThat(checkoutPage.getOnlineTransferPayButton().isEnabled()).isTrue();

        // Verify Apple Pay button
        checkoutPage.getRadioButtonsOnCheckoutPage(Constants.PaymentMethods.APPLE_PAY).click();
        checkoutPage.applePayButton().waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(checkoutPage.applePayButton().isVisible()).isTrue();
        softAssertions.assertThat(checkoutPage.applePayButton().isEnabled()).isTrue();

        // Verify Google Pay button
        checkoutPage.getRadioButtonsOnCheckoutPage(Constants.PaymentMethods.GOOGLE_PAY).click();
        checkoutPage.googlePayButton().waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(checkoutPage.googlePayButton().isVisible()).isTrue();
        softAssertions.assertThat(checkoutPage.googlePayButton().isEnabled()).isTrue();

        softAssertions.assertAll();
    }

    public void selectApplePayPaymentMethod() {
        checkoutPage.getRadioButtonsOnCheckoutPage(Constants.PaymentMethods.APPLE_PAY).click();
        waitABit(2000); // Wait for Apple Pay button to appear
    }

    public void clickApplePayButton() {
        checkoutPage.applePayButton().waitUntilVisible(Duration.ofSeconds(10));
        checkoutPage.applePayButton().click();
        waitABit(3000); // Wait for Apple Pay window to open
    }

    public void cancelApplePayPaymentWindow() {
        // Wait a bit for the Apple Pay window/modal to fully load
        waitABit(2000);

        // Simply press Escape to close the Apple Pay window
        try {
            getDriver().switchTo().activeElement().sendKeys(Keys.ESCAPE);
            System.out.println("Pressed Escape to cancel Apple Pay payment");
        } catch (Exception e) {
            System.out.println("Failed to press Escape key: " + e.getMessage());
            // Try clicking on body and then escape as fallback
            try {
                getDriver().findElement(By.tagName("body")).click();
                waitABit(500);
                getDriver().switchTo().activeElement().sendKeys(Keys.ESCAPE);
                System.out.println("Used body click + Escape as fallback");
            } catch (Exception ex) {
                System.out.println("Fallback method also failed: " + ex.getMessage());
            }
        }

        waitABit(3000); // Wait for error message to appear
    }

    public void selectGooglePayPaymentMethod() {
        checkoutPage.getRadioButtonsOnCheckoutPage(Constants.PaymentMethods.GOOGLE_PAY).click();
        waitABit(2000); // Wait for Google Pay button to appear
    }

    public void clickGooglePayButton() {
        checkoutPage.googlePayButton().waitUntilVisible(Duration.ofSeconds(10));
        checkoutPage.googlePayButton().click();
        waitABit(5000); // Wait for Google Pay window to open
        WebDriver driver = getDriver();
        String parentWindowHandle = driver.getWindowHandle(); //
        //print the name of the parent window
        System.out.println("Parent window handle: " + parentWindowHandle);
    }

    public void closeGooglePayPaymentWindow() {
        WebDriver driver = getDriver();
        String parentWindowHandle = driver.getWindowHandle(); // Save the main window handle

        // Wait for the new window to appear and get its handle
        new WebDriverWait(driver, Duration.ofSeconds(10))
                .until(ExpectedConditions.numberOfWindowsToBe(2));

        //add all window handles to arrayList
        ArrayList<String> tabs = new ArrayList<String>(driver.getWindowHandles());
        System.out.println("First window handle: " + tabs.get(0));
        System.out.println("Second window handle: " + tabs.get(1));

        //switch to the Google Pay window (second window, index 1)
        driver.switchTo().window(tabs.get(1));
        System.out.println("Switched to Google Pay window");

        // Click anywhere in the Google Pay window and then press Escape
        try {
            getDriver().findElement(By.tagName("body")).click();
            System.out.println("Clicked in Google Pay window");
            waitABit(500);
            getDriver().switchTo().activeElement().sendKeys(Keys.ESCAPE);
            System.out.println("Pressed Escape to cancel Google Pay payment");
        } catch (Exception e) {
            System.out.println("Failed to click and press Escape: " + e.getMessage());
        }

        //switch back to the parent window
        driver.switchTo().window(parentWindowHandle);
        System.out.println("Switched back to parent window");

        waitABit(3000); // Wait for error message to appear
    }

    public void verifyPaymentCancelledErrorMessage() {
        SoftAssertions softAssertions = new SoftAssertions();
        checkoutPage.paymentCancelledErrorMessage().waitUntilVisible(Duration.ofSeconds(10));
        String actualText = checkoutPage.paymentCancelledErrorMessage().getText();
        softAssertions.assertThat(actualText).isEqualTo(Constants.ErrorMessages.PAYMENT_CANCELLED_ERROR);
        softAssertions.assertAll();
    }

    /**
     * Verify remaining amount for free shipping is correct at checkout
     */
    public void verifyRemainingAmountForFreeShippingIsCorrectAtCheckout() {
        SoftAssertions softAssertions = new SoftAssertions();
        String totalCartPrice = cartPage.totalPriceInCart().getText();
        double totalCartPriceValue = Double.parseDouble(totalCartPrice.replace(" zł", "").replace(",", "."));
        double expectedRemainingAmount = Constants.PriceFormats.FREE_SHIPPING_THRESHOLD - totalCartPriceValue;
        if (totalCartPriceValue < Constants.PriceFormats.FREE_SHIPPING_THRESHOLD) {
            cartPage.remainingAmountForFreeShipping().waitUntilVisible(Duration.ofSeconds(10));
            String displayedRemainingAmount = cartPage.remainingAmountForFreeShipping().getText();
            String expectedAmountFormatted = String.format("%.2f zł", expectedRemainingAmount).replace(".", ",");
            softAssertions.assertThat(displayedRemainingAmount).isEqualTo(expectedAmountFormatted);

        } else {
            softAssertions.assertThat(cartPage.remainingAmountForFreeShipping().isVisible()).isFalse();
        }

        softAssertions.assertAll();
    }

    /**
     * Verify discount value is updated correctly after quantity change
     * This method should be called after applying coupon at checkout, navigating to cart, and changing quantity
     */
    public void verifyDiscountValueIsUpdatedCorrectlyAfterQuantityChange() {
        SoftAssertions softAssertions = new SoftAssertions();
        cartPage.appliedDiscountHeader().waitUntilVisible(Duration.ofSeconds(10));
        cartPage.appliedDiscountValue().waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(cartPage.appliedDiscountHeader().getText()).contains("Rabat");
        softAssertions.assertThat(cartPage.appliedDiscountValue().isVisible()).isTrue();
        String currentDiscountValue = cartPage.appliedDiscountValue().getText();
        String currentTotalPrice = cartPage.totalPriceInCart().getText();
        double currentDiscountValueDouble = Double.parseDouble(currentDiscountValue.replace(" zł", "").replace(",", "."));
        double currentTotalPriceValue = Double.parseDouble(currentTotalPrice.replace(" zł", "").replace(",", "."));
        softAssertions.assertThat(currentDiscountValueDouble).isEqualTo(currentTotalPriceValue);
        String totalPriceBeforeDiscount = Storage.getStorage().getValue(IStorageKey.TOTAL_CART_PRICE_BEFORE_DISCOUNT);
        if (totalPriceBeforeDiscount != null) {
            double originalPrice = Double.parseDouble(totalPriceBeforeDiscount.replace(" zł", "").replace(",", "."));
            // Get current quantity to calculate expected price
            String currentQuantity = Storage.getStorage().getValue(IStorageKey.PRODUCT_QUANTITY);
            if (currentQuantity != null) {
                int quantity = Integer.parseInt(currentQuantity);
                double expectedPriceBeforeDiscount = originalPrice * quantity;
                double expectedDiscountedPrice = expectedPriceBeforeDiscount / 2; // 50% discount
                softAssertions.assertThat(currentTotalPriceValue).isCloseTo(expectedDiscountedPrice, org.assertj.core.data.Offset.offset(0.01));
                softAssertions.assertThat(currentDiscountValueDouble).isCloseTo(expectedDiscountedPrice, org.assertj.core.data.Offset.offset(0.01));
            }
        }
        // Verify coupon code is still active
        softAssertions.assertThat(cartPage.activeCouponCodeHeader().isVisible()).isTrue();
        // Verify applied discount code is preserved
        String appliedDiscountCode = Storage.getStorage().getValue(IStorageKey.APPLIED_DISCOUNT_CODE);
        if (appliedDiscountCode != null) {
            String displayedDiscountCode = cartPage.appliedDiscountName().getText();
            softAssertions.assertThat(displayedDiscountCode).isEqualTo("Test");
        }

        softAssertions.assertAll();
    }

}
