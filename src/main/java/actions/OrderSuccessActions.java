package actions;

import common.constants.IStorageKey;
import net.serenitybdd.core.steps.UIInteractionSteps;
import org.assertj.core.api.AssertionsForClassTypes;
import org.assertj.core.api.SoftAssertions;
import pages.OrderSuccessPage;
import utils.storage.Storage;

import java.time.Duration;

public class OrderSuccessActions extends UIInteractionSteps {
    private OrderSuccessPage orderSuccessPage;
    private final String THANK_YOU_MESSAGE = "Dziękujemy!";
    private final String ORDER_STATUS_INFO_HEADER = "O statusie zamówienia poinformujemy Cię mailowo.";
    private final String ORDER_PAID_HEADER = "Zamówienie opłacone";

    public void verifyOrderSuccessPage() {
        SoftAssertions softAssertions = new SoftAssertions();
        orderSuccessPage.orderSuccessThankYouHeader().waitUntilVisible(Duration.ofMinutes(1));
        var thankYouMessage = orderSuccessPage.orderSuccessThankYouHeader().getText();

        var orderStatusInfoHeader = orderSuccessPage.orderStatusInformHeader().getText();
        var orderPaidHeader = orderSuccessPage.orderPaidHeader().getText();

        //Capture order number for later verification
        String orderNumberText = orderSuccessPage.orderNumber().getText();
        String orderNumber = orderNumberText.replaceAll(".*o numerze\\s+([\\w\\d]+).*", "$1");
        //Save the order number to storage for later verification
        Storage.getStorage().saveValue(IStorageKey.ORDER_NUMBER, orderNumber);

        softAssertions.assertThat(thankYouMessage).isEqualTo(THANK_YOU_MESSAGE);
        softAssertions.assertThat(orderStatusInfoHeader).isEqualTo(ORDER_STATUS_INFO_HEADER);
        softAssertions.assertThat(orderPaidHeader).isEqualTo(ORDER_PAID_HEADER);
        softAssertions.assertAll();
    }

    public void verifyOrderSuccessPageIsNotDisplayed() {
        AssertionsForClassTypes.assertThat(orderSuccessPage.orderSuccessThankYouHeader().isVisible()).isFalse();
    }
}