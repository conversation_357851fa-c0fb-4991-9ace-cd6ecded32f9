package helpers;

import actions.BrowserActions;
import common.constants.Constants;
import common.constants.IStorageKey;
import common.test_data.BillingAddress;
import common.test_data.ShippingAddress;
import utils.RandomDataGenerator;
import net.serenitybdd.annotations.Steps;
import net.serenitybdd.core.steps.UIInteractionSteps;
import org.assertj.core.api.SoftAssertions;
import pages.AccountPage;
import pages.CheckoutPage;
import utils.FileUploadUtils;
import utils.storage.Storage;

import java.time.Duration;
import java.time.LocalDate;
import java.time.Month;
import java.time.format.DateTimeFormatter;
import java.util.Random;

public class AccountPageHelper extends UIInteractionSteps {
    @Steps
    private AccountPage accountPage;

    @Steps
    private CheckoutPage checkoutPage;
    @Steps
    BrowserActions browserActions;

    // ========== TICKET CREATION HELPER METHODS ==========

    /**
     * Clicks on contact with store link and waits for page to load
     */
    public void clickContactWithStoreLink() {
        accountPage.contactWithTheStoreLink().waitUntilVisible(Duration.ofSeconds(10));
        accountPage.contactWithTheStoreLink().click();
        waitABit(Constants.AccountPage.DEFAULT_WAIT_TIME);
    }

    /**
     * Clicks on create ticket link
     */
    public void clickCreateTicketLink() {
        accountPage.createATicketLink().waitUntilVisible(Duration.ofSeconds(10));
        accountPage.createATicketLink().click();
    }

    /**
     * Selects random reason for contact from dropdown
     */
    public void selectRandomReasonForContact() {
        waitABit(Constants.AccountPage.DEFAULT_WAIT_TIME);
        accountPage.reasonForContactDropdownOptions().waitUntilVisible(Duration.ofSeconds(10));
        int optionsCount = accountPage.reasonForContactDropdownOptions().getSize();
        if (optionsCount > 0) {
            Random random = new Random();
            int randomIndex = random.nextInt(optionsCount);
            String selectedReason = accountPage.reasonForContactDropdownOptions().getTextByIndex(randomIndex);
            accountPage.reasonForContactDropdownOptions().clickByIndex(randomIndex);
            Storage.getStorage().saveValue(IStorageKey.SELECTED_REASON, selectedReason);
        }
    }

    /**
     * Selects first order from dropdown for ticket creation
     */
    public void selectFirstOrderForTicket() {
        waitABit(Constants.AccountPage.DEFAULT_WAIT_TIME);
        accountPage.orderToCreateATicketDropdownOptions().waitUntilVisible(Duration.ofSeconds(10));
        if (accountPage.orderToCreateATicketDropdownOptions().getSize() > 0) {
            String selectedOrder = accountPage.orderToCreateATicketDropdownOptions().getTextByIndex(0);
            accountPage.orderToCreateATicketDropdownOptions().clickByIndex(0);
            Storage.getStorage().saveValue(IStorageKey.SELECTED_ORDER_FOR_TICKET, selectedOrder);
        }
    }

    /**
     * Enters ticket message and stores it for verification
     */
    public void enterTicketMessage(String message) {
        accountPage.messageInputField().waitUntilVisible(Duration.ofSeconds(10));
        accountPage.messageInputField().clearText();
        accountPage.messageInputField().type(message);
        Storage.getStorage().saveValue(IStorageKey.TICKET_MESSAGE, message);
    }

    /**
     * Submits the ticket request
     */
    public void submitTicketRequest() {
        accountPage.submitRequestButton().waitUntilVisible(Duration.ofSeconds(10));
        accountPage.submitRequestButton().click();
    }

    // ========== VERIFICATION HELPER METHODS ==========

    /**
     * Verifies that first order in dropdown matches recently created order
     */
    public void verifyFirstOrderMatchesRecentOrder() {
        accountPage.orderToCreateATicketDropdownOptions().waitUntilVisible(Duration.ofSeconds(10));
        if (accountPage.orderToCreateATicketDropdownOptions().getSize() > 0) {
            String firstOrderText = accountPage.orderToCreateATicketDropdownOptions().getTextByIndex(0);
            String storedOrderNumber = Storage.getStorage().getValue(IStorageKey.ORDER_NUMBER);

            SoftAssertions softAssertions = new SoftAssertions();
            softAssertions.assertThat(firstOrderText).contains(storedOrderNumber.substring(storedOrderNumber.length() - 4));
            softAssertions.assertAll();
        }
    }

    /**
     * Verifies request submitted text
     */
    public void verifyRequestSubmittedText(String expectedText) {
        accountPage.requestSubmittedText().waitUntilVisible(Duration.ofSeconds(10));
        SoftAssertions softAssertions = new SoftAssertions();
        softAssertions.assertThat(accountPage.requestSubmittedText().getText()).contains(expectedText);
        softAssertions.assertAll();
    }

    /**
     * Verifies ticket created date is current date (with timezone tolerance)
     */
    public void verifyTicketCreatedDateIsCurrent() {
        //refresh the page for better consistency of results
        browserActions.refreshPage();
        accountPage.ticketCreatedDate().waitUntilVisible(Duration.ofSeconds(10));
        LocalDate today = LocalDate.now();
        LocalDate yesterday = today.minusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd.MM.yyyy");
        String todayFormatted = today.format(formatter);
        String yesterdayFormatted = yesterday.format(formatter);
        String actualTicketDate = accountPage.ticketCreatedDate().getText();
        SoftAssertions softAssertions = new SoftAssertions();
        softAssertions.assertThat(actualTicketDate).isIn(todayFormatted, yesterdayFormatted);
        softAssertions.assertAll();
    }

    /**
     * Verifies ticket header contains order information
     */
    public void verifyTicketHeaderContainsOrderInfo() {
        accountPage.ticketHeader().waitUntilVisible(Duration.ofSeconds(10));
        String ticketHeaderText = accountPage.ticketHeader().getText();
        String storedOrderNumber = Storage.getStorage().getValue(IStorageKey.ORDER_NUMBER);

        SoftAssertions softAssertions = new SoftAssertions();
        softAssertions.assertThat(ticketHeaderText).contains(Constants.AccountPage.TICKET_ORDER_PREFIX);
        softAssertions.assertThat(ticketHeaderText).contains(storedOrderNumber.substring(storedOrderNumber.length() - 4));
        softAssertions.assertAll();
    }

    /**
     * Verifies ticket last message header
     */
    public void verifyTicketLastMessageHeader(String expectedHeader) {
        accountPage.ticketLastMessageHeader().waitUntilVisible(Duration.ofSeconds(10));
        SoftAssertions softAssertions = new SoftAssertions();
        softAssertions.assertThat(accountPage.ticketLastMessageHeader().getText()).isEqualTo(expectedHeader);
        softAssertions.assertAll();
    }

    /**
     * Verifies ticket last message content
     */
    public void verifyTicketLastMessage(String expectedMessage) {
        accountPage.ticketLastMessage().waitUntilVisible(Duration.ofSeconds(10));
        String storedMessage = Storage.getStorage().getValue(IStorageKey.TICKET_MESSAGE);
        SoftAssertions softAssertions = new SoftAssertions();
        softAssertions.assertThat(accountPage.ticketLastMessage().getText())
                .contains(storedMessage != null ? storedMessage : expectedMessage);
        softAssertions.assertAll();
    }

    /**
     * Fills shipping address form with provided data
     */
    public void fillShippingAddressForm(ShippingAddress shippingData) {
        // When editing addresses, we're taken to a checkout-like form because the selectors are the same
        checkoutPage.getFirstNameInput().waitUntilVisible(Duration.ofSeconds(10));
        checkoutPage.getFirstNameInput().clearText();
        checkoutPage.getFirstNameInput().type(shippingData.getFirstName());
        checkoutPage.getLastNameInput().clearText();
        checkoutPage.getLastNameInput().type(shippingData.getLastName());
        checkoutPage.getAddressInput().clearText();
        checkoutPage.getAddressInput().type(shippingData.getAddress());
        checkoutPage.getCityInput().clearText();
        checkoutPage.getCityInput().type(shippingData.getCity());
        checkoutPage.getPostalCodeInput().clearText();
        checkoutPage.getPostalCodeInput().type(shippingData.getPostalCode());
        checkoutPage.getPhoneInput().clearText();
        checkoutPage.getPhoneInput().type(shippingData.getPhone());

        // Fill company name if provided, otherwise ensure it's empty
        if (shippingData.hasCompanyName()) {
            checkoutPage.getCompanyNameInput().clearText();
            checkoutPage.getCompanyNameInput().type(shippingData.getCompanyName());
        } else {
            // Explicitly clear company name field to ensure no company name is saved
            checkoutPage.getCompanyNameInput().clearText();
        }
    }

    /**
     * Fills billing address form with provided data
     */
    public void fillBillingAddressForm(BillingAddress billingData) {
        if (billingData.isCompanyBilling()) {
            fillCompanyBillingForm(billingData);
        } else {
            fillIndividualBillingForm(billingData);
        }
    }

    /**
     * Fills company billing form
     */
    private void fillCompanyBillingForm(BillingAddress billingData) {
        // Fill company billing information
        checkoutPage.getInvoiceForCompanyOption().waitUntilVisible();
        checkoutPage.getInvoiceForCompanyOption().click();
        checkoutPage.getCompanyBillingNameInput().waitUntilVisible(Duration.ofSeconds(10));
        checkoutPage.getCompanyBillingNameInput().clearText();
        checkoutPage.getCompanyBillingNameInput().type(billingData.getCompanyName());
        checkoutPage.getNipBillingInput().clearText();
        checkoutPage.getNipBillingInput().type(billingData.getNip());
        checkoutPage.getAsCompanyStreetInput().clearText();
        checkoutPage.getAsCompanyStreetInput().type(billingData.getAddress());
        checkoutPage.getAsCompanyCityInput().clearText();
        checkoutPage.getAsCompanyCityInput().type(billingData.getCity());
        checkoutPage.getAsCompanyPostCodeInput().clearText();
        checkoutPage.getAsCompanyPostCodeInput().type(billingData.getPostalCode());
        checkoutPage.companyPhoneInput().clearText();
        checkoutPage.companyPhoneInput().type(billingData.getPhone());
    }

    /**
     * Fills individual billing form
     */
    private void fillIndividualBillingForm(BillingAddress billingData) {
        // Fill individual billing information
        checkoutPage.getInvoiceForPrivateOption().waitUntilVisible();
        checkoutPage.getInvoiceForPrivateOption().click();
        String[] nameParts = billingData.getCompanyName().split(" ", 2);
        String firstName = nameParts.length > 0 ? nameParts[0] : "Test";
        String lastName = nameParts.length > 1 ? nameParts[1] : "User";

        checkoutPage.getAsPersonGivenNameInput().waitUntilVisible(Duration.ofSeconds(10));
        checkoutPage.getAsPersonGivenNameInput().clearText();
        checkoutPage.getAsPersonGivenNameInput().type(firstName);
        checkoutPage.getAsPersonLastNameInput().clearText();
        checkoutPage.getAsPersonLastNameInput().type(lastName);
        checkoutPage.getAsPersonAddressInput().clearText();
        checkoutPage.getAsPersonAddressInput().type(billingData.getAddress());
        checkoutPage.getAsPersonCityInput().clearText();
        checkoutPage.getAsPersonCityInput().type(billingData.getCity());
        checkoutPage.getAsPersonPostalCodeInput().clearText();
        checkoutPage.getAsPersonPostalCodeInput().type(billingData.getPostalCode());
        checkoutPage.getAsPersonPhoneInput().clearText();
        checkoutPage.getAsPersonPhoneInput().type(billingData.getPhone());
    }


    // ========== PROFILE MANAGEMENT HELPER METHODS ==========

    /**
     * Changes user name and last name with provided data
     */
    public void changeUserNameAndLastName(String firstName, String lastName) {
        accountPage.changeDataButton().waitUntilVisible(Duration.ofSeconds(10));
        accountPage.changeDataButton().click();
        accountPage.userNameInput().waitUntilVisible(Duration.ofSeconds(10));
        accountPage.userNameInput().clearText();
        accountPage.userNameInput().type(firstName);
        accountPage.userSurnameInput().clearText();
        accountPage.userSurnameInput().type(lastName);

        // Store the new data for verification
        Storage.getStorage().saveValue(IStorageKey.UPDATED_FIRST_NAME, firstName);
        Storage.getStorage().saveValue(IStorageKey.UPDATED_LAST_NAME, lastName);
        Storage.getStorage().saveValue(IStorageKey.UPDATED_FULL_NAME, firstName + " " + lastName);
    }

    /**
     * Changes name and last name with random data for testing
     */
    public void changeUserNameAndLastNameWithRandomData() {
        String randomFirstName = RandomDataGenerator.generateFirstName();
        String randomLastName = RandomDataGenerator.generateLastName();
        changeUserNameAndLastName(randomFirstName, randomLastName);
    }

    /**
     * Generates and stores random name data for profile updates without changing the UI
     */
    public void generateAndStoreRandomNameData() {
        String randomFirstName = RandomDataGenerator.generateFirstName();
        String randomLastName = RandomDataGenerator.generateLastName();
        Storage.getStorage().saveValue(IStorageKey.UPDATED_FIRST_NAME, randomFirstName);
        Storage.getStorage().saveValue(IStorageKey.UPDATED_LAST_NAME, randomLastName);
        Storage.getStorage().saveValue(IStorageKey.UPDATED_FULL_NAME, randomFirstName + " " + randomLastName);
    }

    /**
     * Generates a random first name using the proper generator
     */
    public String generateRandomFirstName() {
        return RandomDataGenerator.generateFirstName();
    }

    /**
     * Generates a random last name using the proper generator
     */
    public String generateRandomLastName() {
        return RandomDataGenerator.generateLastName();
    }

    /**
     * Saves profile changes
     */
    public void saveProfileChanges() {
        accountPage.saveButton().waitUntilVisible(Duration.ofSeconds(10));
        accountPage.saveButton().click();
        waitABit(Constants.AccountPage.SAVE_OPERATION_WAIT);
    }

    /**
     * Verifies user profile header
     */
    public void verifyUserProfileHeader(String expected) {
        SoftAssertions softly = new SoftAssertions();
        softly.assertThat(accountPage.userProfileHeader().getText()).contains(expected);
        softly.assertAll();
    }

    /**
     * Verifies name and last name header
     */
    public void verifyNameAndLastNameHeader(String expected) {
        SoftAssertions softly = new SoftAssertions();
        softly.assertThat(accountPage.nameAndLastNameHeader().getText()).contains(expected);
        softly.assertAll();
    }

    /**
     * Verifies name and last name value
     */
    public void verifyNameAndLastNameValue(String expected) {
        SoftAssertions softly = new SoftAssertions();
        softly.assertThat(accountPage.nameAndLastNameValue().getText()).contains(expected);
        softly.assertAll();
    }

    /**
     * Verifies that name and last name have been updated with random data
     */
    public void verifyNameAndLastNameIsUpdated() {
        String expectedFullName = Storage.getStorage().getValue(IStorageKey.UPDATED_FULL_NAME);
        accountPage.nameAndLastNameValue().waitUntilVisible(Duration.ofSeconds(10));
        SoftAssertions softly = new SoftAssertions();
        softly.assertThat(accountPage.nameAndLastNameValue().getText()).contains(expectedFullName);
        softly.assertAll();
    }

    /**
     * Verifies password header
     */
    public void verifyPasswordHeader(String expected) {
        SoftAssertions softly = new SoftAssertions();
        softly.assertThat(accountPage.passwordHeader().getText()).contains(expected);
        softly.assertAll();
    }

    /**
     * Verifies password value
     */
    public void verifyPasswordValue(String expected) {
        SoftAssertions softly = new SoftAssertions();
        softly.assertThat(accountPage.passwordValue().getText()).contains(expected);
        softly.assertAll();
    }

    /**
     * Verifies email value
     */
    public void verifyEmailValue(String expectedEmail) {
        accountPage.emailValue().waitUntilVisible(Duration.ofSeconds(10));
        SoftAssertions softly = new SoftAssertions();
        softly.assertThat(accountPage.emailValue().getText()).isEqualToIgnoringCase(expectedEmail);
        softly.assertAll();
    }

    // ========== NEWSLETTER AGREEMENT CHECKBOX HELPER METHODS ==========

    /**
     * Checks the newsletter agreement checkbox if not already checked
     */
    public void checkNewsletterAgreementCheckbox() {
        accountPage.newsletterAgreementCheckbox().waitUntilVisible(Duration.ofSeconds(10));
        if (!accountPage.newsletterAgreementCheckbox().isSelected()) {
            accountPage.newsletterAgreementCheckbox().select();
            waitABit(Constants.AccountPage.STABILITY_WAIT);
        }
    }

    /**
     * Unchecks the newsletter agreement checkbox if currently checked
     */
    public void uncheckNewsletterAgreementCheckbox() {
        accountPage.newsletterAgreementCheckbox().waitUntilVisible(Duration.ofSeconds(10));
        if (accountPage.newsletterAgreementCheckbox().isSelected()) {
            accountPage.newsletterAgreementCheckbox().deselect();
            waitABit(Constants.AccountPage.STABILITY_WAIT);
        }
    }

    /**
     * Verifies the newsletter agreement checkbox label text
     */
    public void verifyNewsletterAgreementCheckboxLabel() {
        accountPage.newsletterAgreementCheckboxLabel().waitUntilVisible(Duration.ofSeconds(10));
        SoftAssertions softly = new SoftAssertions();
        String actualLabelText = accountPage.newsletterAgreementCheckboxLabel().getText();
        softly.assertThat(actualLabelText).contains(Constants.AccountPage.NEWSLETTER_AGREEMENT_LABEL);
        softly.assertAll();
    }

    /**
     * Verifies that the newsletter agreement checkbox is checked
     */
    public void verifyNewsletterAgreementCheckboxIsChecked() {
        accountPage.newsletterAgreementCheckbox().waitUntilVisible(Duration.ofSeconds(10));
        SoftAssertions softly = new SoftAssertions();
        softly.assertThat(accountPage.newsletterAgreementCheckbox().isSelected())
                .as("Newsletter agreement checkbox should be checked")
                .isTrue();
        softly.assertAll();
    }

    /**
     * Verifies that the newsletter agreement checkbox is unchecked
     */
    public void verifyNewsletterAgreementCheckboxIsUnchecked() {
        accountPage.newsletterAgreementCheckbox().waitUntilVisible(Duration.ofSeconds(10));
        SoftAssertions softly = new SoftAssertions();
        softly.assertThat(accountPage.newsletterAgreementCheckbox().isSelected())
                .as("Newsletter agreement checkbox should be unchecked")
                .isFalse();
        softly.assertAll();
    }

    /**
     * Converts Java Month to Polish month name
     */
    public String getPolishMonthName(Month month) {
        return switch (month) {
            case JANUARY -> Constants.AccountPage.POLISH_MONTHS[1];
            case FEBRUARY -> Constants.AccountPage.POLISH_MONTHS[2];
            case MARCH -> Constants.AccountPage.POLISH_MONTHS[3];
            case APRIL -> Constants.AccountPage.POLISH_MONTHS[4];
            case MAY -> Constants.AccountPage.POLISH_MONTHS[5];
            case JUNE -> Constants.AccountPage.POLISH_MONTHS[6];
            case JULY -> Constants.AccountPage.POLISH_MONTHS[7];
            case AUGUST -> Constants.AccountPage.POLISH_MONTHS[8];
            case SEPTEMBER -> Constants.AccountPage.POLISH_MONTHS[9];
            case OCTOBER -> Constants.AccountPage.POLISH_MONTHS[10];
            case NOVEMBER -> Constants.AccountPage.POLISH_MONTHS[11];
            case DECEMBER -> Constants.AccountPage.POLISH_MONTHS[12];
            default -> "";
        };
    }

    // ========== ADDRESS MANAGEMENT HELPER METHODS ==========

    /**
     * Deletes newly added shipping address (last in the list)
     */
    public void deleteNewlyAddedShippingAddress() {
        accountPage.deleteShippingAddressButtons().waitUntilVisible(Duration.ofSeconds(10));
        int deleteButtonCount = accountPage.deleteShippingAddressButtons().getSize();
        accountPage.deleteShippingAddressButtons().clickByIndex(deleteButtonCount - 1);
        waitABit(Constants.AccountPage.ADDRESS_DELETE_WAIT);
    }

    /**
     * Deletes newly added billing address (last in the list)
     */
    public void deleteNewlyAddedBillingAddress() {
        accountPage.deleteBillingAddressButtons().waitUntilVisible(Duration.ofSeconds(10));
        int deleteButtonCount = accountPage.deleteBillingAddressButtons().getSize();
        accountPage.deleteBillingAddressButtons().clickByIndex(deleteButtonCount - 1);
        waitABit(Constants.AccountPage.ADDRESS_DELETE_WAIT);
    }

    /**
     * Verifies required field error for phone number
     */
    public void verifyRequiredFieldErrorForPhoneNumber() {
        accountPage.thisFieldIsRequiredError().waitUntilVisible(Duration.ofSeconds(10));
        SoftAssertions softly = new SoftAssertions();
        softly.assertThat(accountPage.thisFieldIsRequiredError().getText())
                .contains(Constants.ValidationMessages.FIELD_REQUIRED);
        softly.assertAll();
    }

    /**
     * Verifies required field error for name and general save error message
     */
    public void verifyRequiredFieldErrorForName() {
        verifyRequiredFieldErrorAndSaveError();
    }

    /**
     * Verifies required field error for last name and general save error message
     */
    public void verifyRequiredFieldErrorForLastName() {
        verifyRequiredFieldErrorAndSaveError();
    }

    /**
     * Verifies required field error for address and general save error message
     */
    public void verifyRequiredFieldErrorForAddress() {
        verifyRequiredFieldErrorAndSaveError();
    }

    /**
     * Verifies required field error for city and general save error message
     */
    public void verifyRequiredFieldErrorForCity() {
        verifyRequiredFieldErrorAndSaveError();
    }

    /**
     * Verifies required field error for postal code and general save error message
     */
    public void verifyRequiredFieldErrorForPostCode() {
        verifyRequiredFieldErrorAndSaveError();
    }

    /**
     * Verifies both the required field error and the general save error message
     */
    private void verifyRequiredFieldErrorAndSaveError() {
        // Verify the specific field required error
        accountPage.thisFieldIsRequiredError().waitUntilVisible(Duration.ofSeconds(10));

        // Verify the general save error message
        accountPage.accountDataChangeSaveErrorMessage().waitUntilVisible(Duration.ofSeconds(10));

        SoftAssertions softly = new SoftAssertions();
        softly.assertThat(accountPage.thisFieldIsRequiredError().getText())
                .contains(Constants.ValidationMessages.FIELD_REQUIRED);
        softly.assertThat(accountPage.accountDataChangeSaveErrorMessage().getText())
                .contains(Constants.ValidationMessages.DATA_ERRORS_SAVE_MESSAGE);
        softly.assertAll();
    }

    // ========== CHAT FUNCTIONALITY HELPER METHODS ==========

    /**
     * Gets and stores username from profile before entering chat
     */
    public void getUserNameFromProfileAndStore() {
        accountPage.myProfileLink().waitUntilVisible(Duration.ofSeconds(10));
        accountPage.myProfileLink().click();
        accountPage.nameAndLastNameValue().waitUntilVisible(Duration.ofSeconds(10));
        String profileUserName = accountPage.nameAndLastNameValue().getText();
        Storage.getStorage().saveValue(IStorageKey.USER_PROFILE_NAME, profileUserName);
    }

    /**
     * Types and sends chat message
     */
    public void typeAndSendChatMessage(String message) {
        accountPage.chatInputField().waitUntilVisible(Duration.ofSeconds(10));
        accountPage.chatInputField().click();
        waitForStability(Constants.AccountPage.STABILITY_WAIT);

        accountPage.chatInputField().type(message);

        accountPage.sendMessageButton().waitUntilVisible(Duration.ofSeconds(10));
        accountPage.sendMessageButton().click();

        waitForStability(Constants.AccountPage.DEFAULT_WAIT_TIME);
        Storage.getStorage().saveValue(IStorageKey.CHAT_MESSAGE, message);
    }

    /**
     * Verifies chat user name matches profile name
     */
    public void verifyChatUserName() {
        String expectedUserName = Storage.getStorage().getValue(IStorageKey.USER_PROFILE_NAME);

        accountPage.userNameInChat().waitUntilVisible(Duration.ofSeconds(10));
        if (accountPage.userNameInChat().getSize() > 0) {
            int lastIndex = accountPage.userNameInChat().getSize() - 1;
            String actualUserName = accountPage.userNameInChat().getTextByIndex(lastIndex);

            SoftAssertions softAssertions = new SoftAssertions();
            softAssertions.assertThat(actualUserName).contains(expectedUserName);
            softAssertions.assertAll();
        }
    }

    /**
     * Verifies chat date and time format
     */
    public void verifyChatDateAndTime() {
        accountPage.dateAndTimeInChat().waitUntilVisible(Duration.ofSeconds(10));
        if (accountPage.dateAndTimeInChat().getSize() > 0) {
            int lastIndex = accountPage.dateAndTimeInChat().getSize() - 1;
            String actualDateTime = accountPage.dateAndTimeInChat().getTextByIndex(lastIndex);

            LocalDate today = LocalDate.now();
            String expectedDatePattern = today.getDayOfMonth() + " " + getPolishMonthName(today.getMonth());

            SoftAssertions softAssertions = new SoftAssertions();
            softAssertions.assertThat(actualDateTime).contains(expectedDatePattern);
            softAssertions.assertAll();
        }
    }

    /**
     * Verifies sent message in chat
     */
    public void verifyChatSentMessage() {
        String expectedMessage = Storage.getStorage().getValue(IStorageKey.CHAT_MESSAGE);

        accountPage.userSentMessageInChat().waitUntilVisible(Duration.ofSeconds(10));
        if (accountPage.userSentMessageInChat().getSize() > 0) {
            int lastIndex = accountPage.userSentMessageInChat().getSize() - 1;
            String actualMessage = accountPage.userSentMessageInChat().getTextByIndex(lastIndex);

            SoftAssertions softAssertions = new SoftAssertions();
            softAssertions.assertThat(actualMessage).contains(expectedMessage);
            softAssertions.assertAll();
        }
    }

    /**
     * Verifies message received header in chat
     */
    public void verifyMessageReceivedHeader() {
        accountPage.messageReceivedHeader().waitUntilVisible(Duration.ofSeconds(10));
        String actualHeader = accountPage.messageReceivedHeader().getText();

        SoftAssertions softAssertions = new SoftAssertions();
        softAssertions.assertThat(actualHeader).contains(Constants.AccountPage.CHAT_RECEIVED_MESSAGE);
        softAssertions.assertAll();
    }

    /**
     * Reusable wait method for stability
     */
    public void waitForStability(int milliseconds) {
        waitABit(milliseconds);
    }

    // ========== FILE ATTACHMENT HELPER METHODS ==========

    /**
     * Types a message and attaches a file in chat
     */
    public void typeMessageAndAttachFile(String message, String fileName) {
        // First type the message to enable the send button
        accountPage.chatInputField().waitUntilVisible(Duration.ofSeconds(10));
        accountPage.chatInputField().click();
        waitForStability(Constants.AccountPage.STABILITY_WAIT);
        accountPage.chatInputField().type(message);

        // Click attach file button
        accountPage.attachFileButton().waitUntilVisible(Duration.ofSeconds(10));
        accountPage.attachFileButton().click();

        // Upload the file using the file input (the attach button should trigger a file input)
        String filePath = FileUploadUtils.getTestFilePath(fileName);

        // Wait a bit for the file input to appear after clicking attach button
        waitForStability(1000);

        // Find the hidden file input and upload the file
        try {
            // Try to find and use the file input element
            accountPage.getDriver().findElement(org.openqa.selenium.By.xpath("//input[@type='file']")).sendKeys(filePath);
        } catch (Exception e) {
            // If direct file input doesn't work, try alternative selectors
            try {
                accountPage.getDriver().findElement(org.openqa.selenium.By.xpath("//input[@accept]")).sendKeys(filePath);
            } catch (Exception e2) {
                throw new RuntimeException("Could not upload file: " + fileName + ". Tried multiple selectors. Last error: " + e2.getMessage());
            }
        }

        // Store the file information for verification
        Storage.getStorage().saveValue(IStorageKey.ATTACHED_FILE_NAME, fileName);
        Storage.getStorage().saveValue(IStorageKey.ATTACHED_FILE_PATH, filePath);
        Storage.getStorage().saveValue(IStorageKey.CHAT_MESSAGE_WITH_FILE, message);

        waitForStability(Constants.AccountPage.DEFAULT_WAIT_TIME);
    }

    /**
     * Verifies that the attached file name is displayed correctly
     */
    public void verifyAttachedFileName(String expectedFileName) {
        accountPage.attachedFileName().waitUntilVisible(Duration.ofSeconds(10));
        String actualText = accountPage.attachedFileName().getText();
        String expectedText = FileUploadUtils.getExpectedAttachedFileText(expectedFileName, Constants.AccountPage.ATTACHED_FILE_PREFIX);

        SoftAssertions softAssertions = new SoftAssertions();
        softAssertions.assertThat(actualText).contains(expectedText);
        softAssertions.assertAll();
    }

    /**
     * Sends the message with attached file
     */
    public void sendMessageWithAttachedFile() {
        accountPage.sendMessageButton().waitUntilVisible(Duration.ofSeconds(10));
        accountPage.sendMessageButton().click();
        waitForStability(Constants.AccountPage.DEFAULT_WAIT_TIME);
    }

    /**
     * Verifies that the attached file appears in chat
     */
    public void verifyAttachedFileInChat() {
        accountPage.attachedFilesInChat().waitUntilVisible(Duration.ofSeconds(10));

        if (accountPage.attachedFilesInChat().getSize() > 0) {
            int lastIndex = accountPage.attachedFilesInChat().getSize() - 1;
            String attachedFileName = Storage.getStorage().getValue(IStorageKey.ATTACHED_FILE_NAME);

            // The attached file should be visible in the chat
            SoftAssertions softAssertions = new SoftAssertions();
            softAssertions.assertThat(accountPage.attachedFilesInChat().getSize()).isGreaterThan(0);
            softAssertions.assertAll();
        }
    }

    /**
     * Verifies that the attached file can be downloaded
     */
    public void verifyAttachedFileDownload() {
        accountPage.attachedFilesInChat().waitUntilVisible(Duration.ofSeconds(10));

        if (accountPage.attachedFilesInChat().getSize() > 0) {
            int lastIndex = accountPage.attachedFilesInChat().getSize() - 1;
            String attachedFileName = Storage.getStorage().getValue(IStorageKey.ATTACHED_FILE_NAME);

            // Click on the download link with cleanup
            boolean downloadSuccess = FileUploadUtils.downloadFileWithCleanup(
                accountPage.attachedFilesInChat().getElementByIndex(lastIndex).getWrappedElement(),
                attachedFileName
            );

            SoftAssertions softAssertions = new SoftAssertions();
            softAssertions.assertThat(downloadSuccess).isTrue();
            softAssertions.assertAll();
        }
    }

    /**
     * Verifies that invalid file type error is displayed
     */
    public void verifyInvalidFileTypeError() {
        accountPage.invalidFileTypeError().waitUntilVisible(Duration.ofSeconds(10));
        String actualErrorText = accountPage.invalidFileTypeError().getText();

        SoftAssertions softAssertions = new SoftAssertions();
        softAssertions.assertThat(actualErrorText).contains(Constants.AccountPage.INVALID_FILE_TYPE_ERROR);
        softAssertions.assertAll();
    }

    /**
     * Attempts to attach an unsupported file type
     */
    public void attachUnsupportedFile(String message, String fileName) {
        // First type the message to enable the send button
        accountPage.chatInputField().waitUntilVisible(Duration.ofSeconds(10));
        accountPage.chatInputField().click();
        waitForStability(Constants.AccountPage.STABILITY_WAIT);
        accountPage.chatInputField().type(message);

        // Click attach file button
        accountPage.attachFileButton().waitUntilVisible(Duration.ofSeconds(10));
        accountPage.attachFileButton().click();

        // Try to upload the unsupported file
        String filePath = FileUploadUtils.getTestFilePath(fileName);

        // Wait a bit for the file input to appear after clicking attach button
        waitForStability(1000);

        try {
            accountPage.getDriver().findElement(org.openqa.selenium.By.xpath("//input[@type='file']")).sendKeys(filePath);
        } catch (Exception e) {
            // If direct file input doesn't work, try alternative selectors
            try {
                accountPage.getDriver().findElement(org.openqa.selenium.By.xpath("//input[@accept]")).sendKeys(filePath);
            } catch (Exception e2) {
                throw new RuntimeException("Could not attempt to upload unsupported file: " + fileName + ". Tried multiple selectors. Last error: " + e2.getMessage());
            }
        }

        waitForStability(Constants.AccountPage.DEFAULT_WAIT_TIME);
    }

    /**
     * Cleans up any downloaded test files to keep the workstation clean
     */
    public void cleanupDownloadedTestFiles() {
        // Clean up all possible test files that might have been downloaded
        FileUploadUtils.cleanupMultipleDownloadedFiles(
            "automation_pdf.pdf",
            "automation_jpg.jpg",
            "automation_docx.docx",
            "automation_png.png"
        );
    }

    /**
     * Performs pre-test cleanup to ensure clean state
     */
    public void performPreTestCleanup() {
        cleanupDownloadedTestFiles();
    }

    /**
     * Performs post-test cleanup to maintain clean workstation
     */
    public void performPostTestCleanup() {
        cleanupDownloadedTestFiles();
    }
    /**
     * Attempts to submit ticket request without selecting reason and without entering message
     */
    public void attemptToSubmitEmptyTicketRequest() {
        accountPage.submitRequestButton().waitUntilVisible(Duration.ofSeconds(10));
        accountPage.submitRequestButton().click();
        waitForStability(Constants.AccountPage.DEFAULT_WAIT_TIME);
    }

    /**
     * Verifies empty message field error is displayed
     */
    public void verifyEmptyMessageFieldError() {
        accountPage.emptyMessageFieldError().waitUntilVisible(Duration.ofSeconds(10));
        SoftAssertions softAssertions = new SoftAssertions();
        softAssertions.assertThat(accountPage.emptyMessageFieldError().isVisible()).isTrue();
        softAssertions.assertThat(accountPage.emptyMessageFieldError().getText())
                .contains(Constants.AccountPage.EMPTY_MESSAGE_FIELD_ERROR);
        softAssertions.assertAll();
    }

    /**
     * Verifies reason for contact not selected error is displayed
     */
    public void verifyReasonForContactNotSelectedError() {
        accountPage.reasonForContactNotSelectedError().waitUntilVisible(Duration.ofSeconds(10));
        SoftAssertions softAssertions = new SoftAssertions();
        softAssertions.assertThat(accountPage.reasonForContactNotSelectedError().isVisible()).isTrue();
        softAssertions.assertThat(accountPage.reasonForContactNotSelectedError().getText())
                .contains(Constants.AccountPage.REASON_FOR_CONTACT_NOT_SELECTED_ERROR);
        softAssertions.assertAll();
    }
    /**
     * Verifies required field error for company name and general save error message
     */
    public void verifyRequiredFieldErrorForCompanyName() {
        verifyRequiredFieldErrorAndSaveError();
    }

    /**
     * Verifies required field error for NIP and general save error message
     */
    public void verifyRequiredFieldErrorForNip() {
        verifyRequiredFieldErrorAndSaveError();
    }

    /**
     * Verifies invalid phone number error for adding addresses at account page
     */
    public void verifyInvalidPhoneNumberErrorForAddress() {
        // Check for invalid phone number error message
        accountPage.errorMessageForInvalidPhoneNumber().waitUntilVisible(Duration.ofSeconds(10));

        SoftAssertions softly = new SoftAssertions();
        softly.assertThat(accountPage.errorMessageForInvalidPhoneNumber().getText()).contains(Constants.ValidationMessages.INVALID_PHONE_PREFIX);
        softly.assertAll();
    }
}