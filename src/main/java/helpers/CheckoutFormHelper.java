package helpers;

import common.test_data.BillingAddress;
import common.test_data.ShippingAddress;
import common.test_data.AccountPageTestData;
import pages.CheckoutPage;

public class CheckoutFormHelper {
    private final CheckoutPage checkoutPage;

    public CheckoutFormHelper(CheckoutPage checkoutPage) {
        this.checkoutPage = checkoutPage;
    }

    /**
     * Wait for a specified amount of time
     */
    private void waitABit(int milliseconds) {
        try {
            Thread.sleep(milliseconds);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    // ========== NEW METHODS FOR IMPROVED DATA STRUCTURE ==========

    /**
     * Fill a shipping address form with ShippingAddress data
     */
    public void fillShippingAddress(ShippingAddress shippingData) {
        checkoutPage.getEmailInput().clearAndType(shippingData.getEmail());
        checkoutPage.getFirstNameInput().clearAndType(shippingData.getFirstName());
        checkoutPage.getLastNameInput().clearAndType(shippingData.getLastName());
        checkoutPage.getAddressInput().clearAndType(shippingData.getAddress());
        checkoutPage.getCityInput().clearAndType(shippingData.getCity());
        checkoutPage.getPostalCodeInput().clearAndType(shippingData.getPostalCode());
        checkoutPage.getPhoneInput().clearAndType(shippingData.getPhone());

        // Fill company name if provided (optional for individuals)
        if (shippingData.hasCompanyName()) {
            checkoutPage.getCompanyNameInput().clearAndType(shippingData.getCompanyName());
        }
    }

    /**
     * Fill a billing address form with BillingAddress data
     */
    public void fillBillingAddress(BillingAddress billingData) {
        // Uncheck "same as delivery" if it's selected
        if (checkoutPage.sameAsDeliveryCheckbox().isSelected()) {
            checkoutPage.sameAsDeliveryCheckbox().click();
        }

        if (billingData.isCompanyBilling()) {
            // Fill company billing information
            checkoutPage.getInvoiceForCompanyOption().click();
            checkoutPage.getCompanyBillingNameInput().clearAndType(billingData.getCompanyName());
            checkoutPage.getNipBillingInput().clearAndType(billingData.getNip());
            checkoutPage.getAsCompanyStreetInput().clearAndType(billingData.getAddress());
            checkoutPage.getAsCompanyCityInput().clearAndType(billingData.getCity());
            checkoutPage.getAsCompanyPostCodeInput().clearAndType(billingData.getPostalCode());
            checkoutPage.companyPhoneInput().clearAndType(billingData.getPhone());
        } else {
            // Fill individual billing information
            checkoutPage.getInvoiceForPrivateOption().click();
            // For individual billing, use company name as full name
            String[] nameParts = billingData.getCompanyName().split(" ", 2);
            String firstName = nameParts.length > 0 ? nameParts[0] : "Test";
            String lastName = nameParts.length > 1 ? nameParts[1] : "User";

            checkoutPage.getAsPersonGivenNameInput().clearAndType(firstName);
            checkoutPage.getAsPersonLastNameInput().clearAndType(lastName);
            checkoutPage.getAsPersonPhoneInput().clearAndType(billingData.getPhone());
            checkoutPage.getAsPersonAddressInput().clearAndType(billingData.getAddress());
            checkoutPage.getAsPersonPostalCodeInput().clearAndType(billingData.getPostalCode());
            checkoutPage.getAsPersonCityInput().clearAndType(billingData.getCity());
        }
    }

    // ========== FORM VALIDATION HELPER METHODS ==========

    /**
     * Fill shipping form with empty email field for validation testing
     */
    public void fillShippingFormWithEmptyEmail() {
        checkoutPage.getFirstNameInput().waitUntilVisible();
        checkoutPage.getEmailInput().clearText();
        checkoutPage.getFirstNameInput().sendKeys("ValidFirst");
        checkoutPage.getLastNameInput().sendKeys("ValidLast");
        checkoutPage.getAddressInput().sendKeys("Valid Address 123");
        checkoutPage.getCityInput().sendKeys("Valid City");
        checkoutPage.getPostalCodeInput().sendKeys("00-000");
        checkoutPage.getPhoneInput().sendKeys("*********");
    }

    /**
     * Fill shipping form with empty first name field for validation testing
     */
    public void fillShippingFormWithEmptyFirstName() {
        checkoutPage.getFirstNameInput().waitUntilVisible();
        checkoutPage.getEmailInput().sendKeys("<EMAIL>");
        checkoutPage.getFirstNameInput().clearText();
        checkoutPage.getLastNameInput().sendKeys("ValidLast");
        checkoutPage.getAddressInput().sendKeys("Valid Address 123");
        checkoutPage.getCityInput().sendKeys("Valid City");
        checkoutPage.getPostalCodeInput().sendKeys("00-000");
        checkoutPage.getPhoneInput().sendKeys("*********");
    }

    /**
     * Fill shipping form with empty last name field for validation testing
     */
    public void fillShippingFormWithEmptyLastName() {
        checkoutPage.getFirstNameInput().waitUntilVisible();
        checkoutPage.getEmailInput().sendKeys("<EMAIL>");
        checkoutPage.getFirstNameInput().sendKeys("ValidFirst");
        checkoutPage.getLastNameInput().clearText();
        checkoutPage.getAddressInput().sendKeys("Valid Address 123");
        checkoutPage.getCityInput().sendKeys("Valid City");
        checkoutPage.getPostalCodeInput().sendKeys("00-000");
        checkoutPage.getPhoneInput().sendKeys("*********");
    }

    /**
     * Fill shipping form with empty street address field for validation testing
     */
    public void fillShippingFormWithEmptyStreetAddress() {
        checkoutPage.getFirstNameInput().waitUntilVisible();
        checkoutPage.getEmailInput().sendKeys("<EMAIL>");
        checkoutPage.getFirstNameInput().sendKeys("ValidFirst");
        checkoutPage.getLastNameInput().sendKeys("ValidLast");
        checkoutPage.getAddressInput().clearText();
        checkoutPage.getCityInput().sendKeys("Valid City");
        checkoutPage.getPostalCodeInput().sendKeys("00-000");
        checkoutPage.getPhoneInput().sendKeys("*********");
    }

    /**
     * Fill shipping form with empty city field for validation testing
     */
    public void fillShippingFormWithEmptyCity() {
        checkoutPage.getFirstNameInput().waitUntilVisible();
        checkoutPage.getEmailInput().sendKeys("<EMAIL>");
        checkoutPage.getFirstNameInput().sendKeys("ValidFirst");
        checkoutPage.getLastNameInput().sendKeys("ValidLast");
        checkoutPage.getAddressInput().sendKeys("Valid Address 123");
        checkoutPage.getCityInput().clearText();
        checkoutPage.getPostalCodeInput().sendKeys("00-000");
        checkoutPage.getPhoneInput().sendKeys("*********");
    }

    /**
     * Fill shipping form with empty postal code field for validation testing
     */
    public void fillShippingFormWithEmptyPostalCode() {
        checkoutPage.getFirstNameInput().waitUntilVisible();
        checkoutPage.getEmailInput().sendKeys("<EMAIL>");
        checkoutPage.getFirstNameInput().sendKeys("ValidFirst");
        checkoutPage.getLastNameInput().sendKeys("ValidLast");
        checkoutPage.getAddressInput().sendKeys("Valid Address 123");
        checkoutPage.getCityInput().sendKeys("Valid City");
        checkoutPage.getPostalCodeInput().clearText();
        checkoutPage.getPhoneInput().sendKeys("*********");
    }

    /**
     * Fill shipping form with empty phone number field for validation testing
     */
    public void fillShippingFormWithEmptyPhoneNumber() {
        checkoutPage.getFirstNameInput().waitUntilVisible();
        checkoutPage.getEmailInput().sendKeys("<EMAIL>");
        checkoutPage.getFirstNameInput().sendKeys("ValidFirst");
        checkoutPage.getLastNameInput().sendKeys("ValidLast");
        checkoutPage.getAddressInput().sendKeys("Valid Address 123");
        checkoutPage.getCityInput().sendKeys("Valid City");
        checkoutPage.getPostalCodeInput().sendKeys("00-000");
        checkoutPage.getPhoneInput().clearText();
    }

    /**
     * Fill shipping form with invalid phone number for validation testing
     */
    public void fillShippingFormWithInvalidPhoneNumber() {
        ShippingAddress shippingData = AccountPageTestData.createShippingAddressWithInvalidPhone();
        checkoutPage.getFirstNameInput().waitUntilVisible();
        checkoutPage.getEmailInput().sendKeys(shippingData.getPersonalInfo().getEmail());
        checkoutPage.getFirstNameInput().sendKeys(shippingData.getPersonalInfo().getFirstName());
        checkoutPage.getLastNameInput().sendKeys(shippingData.getPersonalInfo().getLastName());
        checkoutPage.getAddressInput().sendKeys(shippingData.getAddress());
        checkoutPage.getCityInput().sendKeys(shippingData.getCity());
        checkoutPage.getPostalCodeInput().sendKeys(shippingData.getPostalCode());
        checkoutPage.getPhoneInput().sendKeys(shippingData.getPersonalInfo().getPhone());
    }

    /**
     * Fill shipping form with invalid postal code for validation testing
     */
    public void fillShippingFormWithInvalidPostalCode() {
        ShippingAddress shippingData = AccountPageTestData.createShippingAddressWithInvalidPostalCode();
        checkoutPage.getFirstNameInput().waitUntilVisible();
        checkoutPage.getEmailInput().sendKeys(shippingData.getPersonalInfo().getEmail());
        checkoutPage.getFirstNameInput().sendKeys(shippingData.getPersonalInfo().getFirstName());
        checkoutPage.getLastNameInput().sendKeys(shippingData.getPersonalInfo().getLastName());
        checkoutPage.getAddressInput().sendKeys(shippingData.getAddress());
        checkoutPage.getCityInput().sendKeys(shippingData.getCity());
        checkoutPage.getPostalCodeInput().sendKeys(shippingData.getPostalCode());
        checkoutPage.getPhoneInput().sendKeys(shippingData.getPersonalInfo().getPhone());
    }

    /**
     * Fill complete shipping form with valid data for validation testing
     * Clears all fields first to ensure no invalid data remains
     */
    public void fillCompleteShippingFormWithValidData() {
        checkoutPage.getFirstNameInput().waitUntilVisible();

        // Clear all fields first to remove any invalid data
        checkoutPage.getEmailInput().clearText();
        checkoutPage.getFirstNameInput().clearText();
        checkoutPage.getLastNameInput().clearText();
        checkoutPage.getAddressInput().clearText();
        checkoutPage.getCityInput().clearText();
        checkoutPage.getPostalCodeInput().clearText();
        checkoutPage.getPhoneInput().clearText();

        // Fill with valid data
        checkoutPage.getEmailInput().sendKeys("<EMAIL>");
        checkoutPage.getFirstNameInput().sendKeys("ValidFirst");
        checkoutPage.getLastNameInput().sendKeys("ValidLast");
        checkoutPage.getAddressInput().sendKeys("Valid Address 123");
        checkoutPage.getCityInput().sendKeys("Valid City");
        checkoutPage.getPostalCodeInput().sendKeys("00-000");
        checkoutPage.getPhoneInput().sendKeys("*********");

        // Wait for form validation to complete and button to be re-enabled
        waitABit(2000);
    }

    // ========== BILLING FORM VALIDATION HELPER METHODS ==========

    /**
     * Fill billing form with empty company name for validation testing
     */
    public void fillBillingFormWithEmptyCompanyName() {
        // First select company billing option
        checkoutPage.invoiceForCompanyOption().click();
        waitABit(1000);

        checkoutPage.getCompanyBillingNameInput().waitUntilVisible();
        checkoutPage.getCompanyBillingNameInput().clearText();
        checkoutPage.getAsCompanyStreetInput().sendKeys("Valid Billing Street 123");
        checkoutPage.getAsCompanyCityInput().sendKeys("Valid City");
        checkoutPage.getAsCompanyPostCodeInput().sendKeys("00-000");
        checkoutPage.getNipBillingInput().sendKeys("*********");
    }

    /**
     * Fill billing form with empty street address for validation testing
     */
    public void fillBillingFormWithEmptyStreetAddress() {
        // First select company billing option
        checkoutPage.invoiceForCompanyOption().click();
        waitABit(1000);

        checkoutPage.getCompanyBillingNameInput().waitUntilVisible();
        checkoutPage.getCompanyBillingNameInput().sendKeys("Valid Company Name");
        checkoutPage.getAsCompanyStreetInput().clearText();
        checkoutPage.getAsCompanyCityInput().sendKeys("Valid City");
        checkoutPage.getAsCompanyPostCodeInput().sendKeys("00-000");
        checkoutPage.getNipBillingInput().sendKeys("*********");
    }

    /**
     * Fill billing form with empty city for validation testing
     */
    public void fillBillingFormWithEmptyCity() {
        // First select company billing option
        checkoutPage.invoiceForCompanyOption().click();
        waitABit(1000);

        checkoutPage.getCompanyBillingNameInput().waitUntilVisible();
        checkoutPage.getCompanyBillingNameInput().sendKeys("Valid Company Name");
        checkoutPage.getAsCompanyStreetInput().sendKeys("Valid Billing Street 123");
        checkoutPage.getAsCompanyCityInput().clearText();
        checkoutPage.getAsCompanyPostCodeInput().sendKeys("00-000");
        checkoutPage.getNipBillingInput().sendKeys("*********");
    }

    /**
     * Fill billing form with empty NIP for validation testing
     */
    public void fillBillingFormWithEmptyNIP() {
        // First select company billing option
        checkoutPage.invoiceForCompanyOption().click();
        waitABit(1000);

        checkoutPage.getCompanyBillingNameInput().waitUntilVisible();
        checkoutPage.getCompanyBillingNameInput().sendKeys("Valid Company Name");
        checkoutPage.getAsCompanyStreetInput().sendKeys("Valid Billing Street 123");
        checkoutPage.getAsCompanyCityInput().sendKeys("Valid City");
        checkoutPage.getAsCompanyPostCodeInput().sendKeys("00-000");
        checkoutPage.getNipBillingInput().clearText();
    }

    /**
     * Fill billing form with empty postal code for validation testing
     */
    public void fillBillingFormWithEmptyPostalCode() {
        // First select company billing option
        checkoutPage.invoiceForCompanyOption().click();
        waitABit(1000);

        checkoutPage.getCompanyBillingNameInput().waitUntilVisible();
        checkoutPage.getCompanyBillingNameInput().sendKeys("Valid Company Name");
        checkoutPage.getAsCompanyStreetInput().sendKeys("Valid Billing Street 123");
        checkoutPage.getAsCompanyCityInput().sendKeys("Valid City");
        checkoutPage.getAsCompanyPostCodeInput().clearText();
        checkoutPage.getNipBillingInput().sendKeys("*********");
    }

    /**
     * Fill billing form with invalid postal code for validation testing
     */
    public void fillBillingFormWithInvalidPostalCode() {
        BillingAddress billingData = AccountPageTestData.createBillingAddressWithInvalidPostalCode();

        // First select company billing option
        checkoutPage.invoiceForCompanyOption().click();
        waitABit(1000);

        checkoutPage.getCompanyBillingNameInput().waitUntilVisible();
        checkoutPage.getCompanyBillingNameInput().sendKeys(billingData.getCompanyName());
        checkoutPage.getAsCompanyStreetInput().sendKeys(billingData.getAddress());
        checkoutPage.getAsCompanyCityInput().sendKeys(billingData.getCity());
        checkoutPage.getAsCompanyPostCodeInput().sendKeys(billingData.getPostalCode());
        checkoutPage.getNipBillingInput().sendKeys(billingData.getNip());
    }

    /**
     * Fill complete billing form with valid data for validation testing
     * Clears all fields first to ensure no invalid data remains
     */
    public void fillCompleteBillingFormWithValidData() {
        checkoutPage.invoiceForCompanyOption().click();
        waitABit(1000);
        checkoutPage.getCompanyBillingNameInput().waitUntilVisible();

        // Clear all fields first to remove any invalid data
        checkoutPage.getCompanyBillingNameInput().clearText();
        checkoutPage.getAsCompanyStreetInput().clearText();
        checkoutPage.getAsCompanyCityInput().clearText();
        checkoutPage.getAsCompanyPostCodeInput().clearText();
        checkoutPage.getNipBillingInput().clearText();

        // Fill with valid data
        checkoutPage.getCompanyBillingNameInput().sendKeys("Valid Company Name");
        checkoutPage.getAsCompanyStreetInput().sendKeys("Valid Billing Street 123");
        checkoutPage.getAsCompanyCityInput().sendKeys("Valid City");
        checkoutPage.getAsCompanyPostCodeInput().sendKeys("00-000");
        checkoutPage.getNipBillingInput().sendKeys("*********");
        waitABit(2000);
    }
}
