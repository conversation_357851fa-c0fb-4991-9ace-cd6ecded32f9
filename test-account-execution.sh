#!/bin/bash

echo "🧪 Testing Account Page Execution - Simple Sequential Control"
echo "============================================================"
echo ""
echo "This test will run @account_page scenarios to verify:"
echo "✅ Non-sequential scenarios run in PARALLEL with each other (up to 5 threads)"
echo "✅ Sequential scenarios (@account_sequential) run SEQUENTIALLY (one after another)"
echo "✅ Sequential scenarios don't interfere with each other due to exclusive lock"
echo ""
echo "🚀 Starting test execution..."
echo ""

# Run account page tests
mvn clean verify -Dgroups="account_page" -Denvironment=DEV-GDANSK -Dwebdriver.driver=chrome

echo ""
echo "📊 Test execution completed!"
echo ""
echo "Expected behavior:"
echo "- Non-sequential scenarios should run in PARALLEL (multiple threads simultaneously)"
echo "- Sequential scenarios (@account_sequential) should run ONE AT A TIME"
echo "- No data conflicts should occur between sequential scenarios"
echo ""
echo "If you see scenarios running one-by-one instead of in parallel, there's still an issue."
