name: 🚀 Test Run Pipeline

on:
  workflow_dispatch:
    inputs:
      test_suite:
        description: 'Test Suite to Run'
        required: true
        default: 'smoke'
        type: choice
        options:
          - 'regression'
          - 'pdp'
          - 'cart'
          - 'search'
          - 'order'
          - 'account_page'
          - 'checkout'
          - 'logged_in_order'
          - 'custom_tags'
          - 'smoke'
          - 'registration'
          - 'login'
          - 'coupon_code'
          - 'country_and_language'
      
      branch:
        description: 'Branch to run tests on'
        required: true
        default: 'main'
        type: string
      
      environment:
        description: 'Test Environment'
        required: true
        default: 'DEV-GDANSK'
        type: choice
        options:
          - 'DEV-GDANSK'
          - 'DEV-KOSZALIN'
          - 'DEV-WARSZAWA'
          - 'DEV-DAJAR-DEV-PL'
      
      browser:
        description: 'Browser to run tests on'
        required: true
        default: 'chrome'
        type: choice
        options:
          - 'chrome'
          - 'firefox'
          - 'edge'
      
      parallel_threads:
        description: 'Number of parallel threads'
        required: true
        default: '8'
        type: choice
        options:
          - '1'
          - '2'
          - '3'
          - '4'
          - '5'
          - '6'
          - '7'
          - '8'
      
      custom_tags:
        description: 'Custom Tag Numbers/Names (required if test_suite is custom_tags) - e.g., 0001,0002,0004 or order,account,search'
        required: false
        type: string

env:
  MAVEN_OPTS: -Xmx2048m -XX:MetaspaceSize=512m -XX:MaxMetaspaceSize=1024m

jobs:
  validate-inputs:
    name: 🔍 Validate Inputs
    runs-on: ubuntu-latest
    outputs:
      tags: ${{ steps.validate.outputs.tags }}
      serenity_env: ${{ steps.validate.outputs.serenity_env }}
    steps:
      - name: Validate custom tags input
        id: validate
        run: |
          if [[ "${{ github.event.inputs.test_suite }}" == "custom_tags" ]]; then
            if [[ -z "${{ github.event.inputs.custom_tags }}" ]]; then
              echo "❌ Error: Custom tags are required when test_suite is 'custom_tags'"
              echo "Please provide tag numbers like: 0001,0002,0004 or text tags like: order,account,search"
              exit 1
            fi
          
            # Validate tag format - support both numeric (0001, 0002) and text tags (order, account, search)
            tags="${{ github.event.inputs.custom_tags }}"
            # Allow alphanumeric tags, underscores, and commas (no spaces around commas)
            if [[ ! $tags =~ ^[a-zA-Z0-9_]+(,[a-zA-Z0-9_]+)*$ ]]; then
              echo "❌ Error: Invalid tag format. Use alphanumeric tags separated by commas (no spaces)"
              echo "Examples:"
              echo "  Numeric: 0001,0002,0004"
              echo "  Text: order,account,search"
              echo "  Mixed: 0001,order,search"
              exit 1
            fi
          
            echo "tags=$tags" >> $GITHUB_OUTPUT
          else
            echo "tags=${{ github.event.inputs.test_suite }}" >> $GITHUB_OUTPUT
          fi
          
          # Set Serenity environment for Firefox and Edge
          if [[ "${{ github.event.inputs.browser }}" == "firefox" || "${{ github.event.inputs.browser }}" == "edge" ]]; then
            echo "serenity_env=${{ github.event.inputs.browser }}" >> $GITHUB_OUTPUT
          else
            echo "serenity_env=" >> $GITHUB_OUTPUT
          fi

  test-execution:
    name: 🧪 Execute Tests
    runs-on: ubuntu-latest
    needs: validate-inputs
    timeout-minutes: 120
    
    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch }}

      - name: ☕ Set up JDK 21
        uses: actions/setup-java@v4
        with:
          java-version: '21'
          distribution: 'temurin'
        env:
          MAVEN_OPTS: -Xmx2048m -XX:MetaspaceSize=512m -XX:MaxMetaspaceSize=1024m

      - name: 🔍 Verify Java and Maven Setup
        run: |
          echo "☕ Java Version:"
          java -version
          echo ""
          echo "📦 Maven Version:"
          mvn -version
          echo ""
          echo "🔧 Maven Options: $MAVEN_OPTS"
          echo ""
          echo "📁 Project Structure:"
          ls -la

      - name: 📦 Cache Maven Dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.m2/repository
            ~/.m2/wrapper
          key: ${{ runner.os }}-maven-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            ${{ runner.os }}-maven-

      - name: 🌐 Setup Chrome Browser with Isolation
        if: github.event.inputs.browser == 'chrome'
        run: |
          echo "🌐 Setting up Chrome with enhanced isolation..."

          # Install Chrome
          wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
          echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" | sudo tee /etc/apt/sources.list.d/google-chrome.list
          sudo apt-get update
          sudo apt-get install -y google-chrome-stable

          # Create isolated Chrome directories
          mkdir -p /tmp/chrome_isolation
          chmod 755 /tmp/chrome_isolation

          # Set Chrome isolation environment variables
          echo "CHROME_USER_DATA_DIR=/tmp/chrome_isolation" >> $GITHUB_ENV
          echo "CHROME_ISOLATION_ENABLED=true" >> $GITHUB_ENV

          echo "✅ Chrome setup with isolation completed"

      - name: 🦊 Setup Firefox Browser with Isolation
        if: github.event.inputs.browser == 'firefox'
        run: |
          echo "🦊 Setting up Firefox with enhanced isolation..."

          # Install Firefox
          sudo apt-get update
          sudo apt-get install -y firefox

          # Create isolated Firefox directories
          mkdir -p /tmp/firefox_isolation
          chmod 755 /tmp/firefox_isolation

          # Set Firefox isolation environment variables
          echo "FIREFOX_PROFILE_DIR=/tmp/firefox_isolation" >> $GITHUB_ENV
          echo "FIREFOX_ISOLATION_ENABLED=true" >> $GITHUB_ENV

          echo "✅ Firefox setup with isolation completed"

      - name: 🔷 Setup Edge Browser with Isolation
        if: github.event.inputs.browser == 'edge'
        run: |
          echo "🔷 Setting up Edge with enhanced isolation..."

          # Install Edge
          curl https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor > microsoft.gpg
          sudo install -o root -g root -m 644 microsoft.gpg /etc/apt/trusted.gpg.d/
          sudo sh -c 'echo "deb [arch=amd64,arm64,armhf signed-by=/etc/apt/trusted.gpg.d/microsoft.gpg] https://packages.microsoft.com/repos/edge stable main" > /etc/apt/sources.list.d/microsoft-edge-dev.list'
          sudo apt-get update
          sudo apt-get install -y microsoft-edge-stable

          # Create isolated Edge directories
          mkdir -p /tmp/edge_isolation
          chmod 755 /tmp/edge_isolation

          # Set Edge isolation environment variables
          echo "EDGE_USER_DATA_DIR=/tmp/edge_isolation" >> $GITHUB_ENV
          echo "EDGE_ISOLATION_ENABLED=true" >> $GITHUB_ENV

          echo "✅ Edge setup with isolation completed"

      - name: 🔧 Configure CI to Use Working Local Configuration
        run: |
          echo "🔧 Using the same configuration that works locally..."
          echo "✅ Using existing RunnerTestSuite with SerenityReporterParallel"
          echo "✅ Using existing junit-platform.properties with 3 threads"
          echo "✅ Using existing serenity.conf configuration"

          # Ensure JSON output is enabled for test results parsing
          if ! grep -q "json:target/cucumber-report/cucumber.json" src/test/java/RunnerTestSuite.java; then
            echo "🔧 Adding JSON output to RunnerTestSuite for test results parsing..."
            sed -i 's|html:target/cucumber-report/cucumber.html"|html:target/cucumber-report/cucumber.html,json:target/cucumber-report/cucumber.json"|' src/test/java/RunnerTestSuite.java
            echo "✅ JSON output added to RunnerTestSuite"
          else
            echo "✅ JSON output already configured in RunnerTestSuite"
          fi

          echo "✅ No CI-specific configuration needed - using working local setup"

      - name: 🔧 Display Test Configuration
        run: |
          echo "🚀 Test Execution Configuration:"
          echo "├── Branch: ${{ github.event.inputs.branch }}"
          echo "├── Test Suite: ${{ github.event.inputs.test_suite }}"
          echo "├── Tags: ${{ needs.validate-inputs.outputs.tags }}"
          echo "├── Environment: ${{ github.event.inputs.environment }}"
          echo "├── Browser: ${{ github.event.inputs.browser }}"
          echo "├── Execution Mode: PARALLEL (same as working local config)"
          echo "├── Parallel Threads: ${{ github.event.inputs.parallel_threads }} (max 5, overrides junit-platform.properties)"
          echo "├── Serenity Environment: ${{ needs.validate-inputs.outputs.serenity_env }}"
          echo "├── Browser Isolation: Using existing BrowserIsolationManager"
          echo "├── Serenity Reporter: SerenityReporterParallel (working config)"
          echo "└── Report Generation: During execution (like local)"
          echo ""
          echo "✅ Using EXACT same configuration that works locally"
          echo "📊 SerenityReporterParallel generates clean reports during parallel execution"

      - name: 🧹 Pre-Test Cleanup
        run: |
          echo "🧹 Performing pre-test cleanup for browser isolation..."

          # Clean up any existing browser processes
          pkill -f "chrome\|firefox\|edge\|chromium" || true

          # Clean up temporary directories from previous runs
          rm -rf /tmp/chrome_* /tmp/firefox_* /tmp/edge_* || true
          rm -rf ~/.cache/google-chrome* ~/.cache/mozilla* ~/.cache/microsoft-edge* || true

          # Clean up any leftover WebDriver processes
          pkill -f "chromedriver\|geckodriver\|msedgedriver" || true

          # Wait for cleanup to complete
          sleep 2

          echo "✅ Pre-test cleanup completed"

      - name: 🧪 Run Tests
        run: |
          # Don't exit on error - we want to generate reports even if tests fail
          set +e

          # Use the SAME Maven command that works locally
          mvn_cmd="mvn clean verify"
          mvn_cmd="$mvn_cmd -Dgroups=\"${{ needs.validate-inputs.outputs.tags }}\""
          mvn_cmd="$mvn_cmd -Denvironment=${{ github.event.inputs.environment }}"
          mvn_cmd="$mvn_cmd -Dwebdriver.driver=${{ github.event.inputs.browser }}"
          mvn_cmd="$mvn_cmd -Dserenity.take.screenshots=FOR_FAILURES"
          mvn_cmd="$mvn_cmd -Dwebdriver.autodownload=true"

          # Override junit-platform.properties parallel configuration with workflow input
          CI_THREADS=${{ github.event.inputs.parallel_threads }}
          if [ "$CI_THREADS" -gt "5" ]; then
            CI_THREADS=5
            echo "⚠️ Limiting parallel threads to 5 (from requested ${{ github.event.inputs.parallel_threads }}) for CI stability"
          fi

          # Override the junit-platform.properties settings with workflow input
          mvn_cmd="$mvn_cmd -Dcucumber.execution.parallel.enabled=true"
          mvn_cmd="$mvn_cmd -Dcucumber.execution.parallel.config.strategy=fixed"
          mvn_cmd="$mvn_cmd -Dcucumber.execution.parallel.config.fixed.parallelism=$CI_THREADS"
          mvn_cmd="$mvn_cmd -Dcucumber.execution.parallel.config.fixed.max-pool-size=$CI_THREADS"

          echo "✅ Overriding junit-platform.properties: Using $CI_THREADS parallel threads from workflow input"

          # Force headless mode for CI (only difference from local)
          mvn_cmd="$mvn_cmd -Dheadless=true"

          echo "🚀 Using SAME configuration as working local setup with $CI_THREADS threads (overriding junit-platform.properties)"
          echo "📊 SerenityReporterParallel will generate clean reports during execution"

          # Add Serenity environment for Firefox and Edge
          if [[ -n "${{ needs.validate-inputs.outputs.serenity_env }}" ]]; then
            mvn_cmd="$mvn_cmd -Dserenity.environment=${{ needs.validate-inputs.outputs.serenity_env }}"
          fi

          echo "🚀 Executing: $mvn_cmd"

          # Start browser process monitoring in background
          (
            while true; do
              echo "🔍 Browser processes at $(date):"
              ps aux | grep -E "(chrome|firefox|edge|chromedriver|geckodriver|msedgedriver)" | grep -v grep || echo "No browser processes found"
              echo "📁 Temp directories:"
              ls -la /tmp/ | grep -E "(chrome|firefox|edge)" || echo "No browser temp directories found"
              echo "---"
              sleep 30
            done
          ) &
          MONITOR_PID=$!

          # Execute tests and capture exit code
          eval $mvn_cmd
          TEST_EXIT_CODE=$?

          # Stop monitoring
          kill $MONITOR_PID 2>/dev/null || true

          echo "📊 Test execution completed with exit code: $TEST_EXIT_CODE"

          # Store the exit code for later use
          echo "TEST_EXIT_CODE=$TEST_EXIT_CODE" >> $GITHUB_ENV

      - name: 🧹 Post-Test Cleanup
        if: always()
        run: |
          echo "🧹 Performing post-test cleanup for browser isolation..."

          # Force kill any remaining browser processes
          pkill -9 -f "chrome\|firefox\|edge\|chromium" || true
          pkill -9 -f "chromedriver\|geckodriver\|msedgedriver" || true

          # Clean up temporary browser data directories
          rm -rf /tmp/chrome_* /tmp/firefox_* /tmp/edge_* || true
          rm -rf ~/.cache/google-chrome* ~/.cache/mozilla* ~/.cache/microsoft-edge* || true

          # Clean up any shared memory segments
          ipcs -m | awk '/chrome|firefox|edge/ {print $2}' | xargs -r ipcrm -m || true

          # Clean up any leftover sockets
          find /tmp -name "*chrome*" -o -name "*firefox*" -o -name "*edge*" -type s -delete 2>/dev/null || true

          echo "✅ Post-test cleanup completed"

      - name: 🏁 Final Test Result
        if: always()
        run: |
          echo "🏁 Final test execution result: ${TEST_EXIT_CODE:-1}"

          if [ "${TEST_EXIT_CODE:-1}" = "0" ]; then
            echo "✅ All tests passed successfully!"
            exit 0
          else
            echo "❌ Tests failed with exit code: ${TEST_EXIT_CODE:-1}"
            echo "📥 Check the downloaded artifacts for detailed failure information."
            exit 1
          fi

      - name: 📊 Generate Clean Serenity Reports (Post-Execution)
        if: always()
        run: |
          echo "📊 Generating clean Serenity Reports after parallel test execution..."

          # Create directories if they don't exist
          mkdir -p target/site/serenity
          mkdir -p target/cucumber-report
          mkdir -p target/failsafe-reports

          # Check what test result files exist
          echo "📁 Test result files available for report generation:"
          find target -type f -name "*.json" -o -name "*.xml" | head -20 || echo "No test result files found"

          # Wait a moment for any file system operations to complete
          sleep 2

          # Generate Serenity reports from the test results (without SerenityReporter conflicts)
          echo "🔄 Generating Serenity aggregate report from test results..."
          mvn serenity:aggregate \
            -Dtags="${{ needs.validate-inputs.outputs.tags }}" \
            -Dserenity.reports.show.step.details=true \
            -Dserenity.test.root=src/test/java \
            -Dserenity.outputDirectory=target/site/serenity \
            || echo "⚠️ Serenity aggregate failed, trying alternative approach..."

          # Alternative report generation with different parameters
          if [ ! -f "target/site/serenity/index.html" ]; then
            echo "🔄 Trying alternative Serenity report generation..."
            mvn serenity:aggregate \
              -Dserenity.reports.show.step.details=true \
              -Dserenity.outputDirectory=target/site/serenity \
              || echo "⚠️ Alternative report generation also failed"
          fi

          # Force report generation using available JSON files
          if [ ! -f "target/site/serenity/index.html" ]; then
            echo "🔄 Forcing report generation from available JSON files..."
            # Find and process any available JSON test results
            if find target -name "*.json" -type f | grep -q .; then
              mvn serenity:aggregate -Dserenity.source.dir=target || echo "⚠️ JSON-based report generation failed"
            fi
          fi

          # Create a comprehensive summary report if Serenity generation fails
          if [ ! -f "target/site/serenity/index.html" ]; then
            echo "📝 Creating comprehensive summary report..."
            cat > target/site/serenity/index.html << 'EOF'
          <!DOCTYPE html>
          <html>
          <head>
              <title>Test Execution Report</title>
              <style>
                  body { font-family: Arial, sans-serif; margin: 20px; }
                  .header { background-color: #f0f0f0; padding: 10px; border-radius: 5px; }
                  .info { margin: 10px 0; }
              </style>
          </head>
          <body>
              <div class="header">
                  <h1>🧪 Test Execution Report</h1>
              </div>
              <div class="info">
                  <p><strong>Browser:</strong> ${{ github.event.inputs.browser }}</p>
                  <p><strong>Environment:</strong> ${{ github.event.inputs.environment }}</p>
                  <p><strong>Tags:</strong> ${{ needs.validate-inputs.outputs.tags }}</p>
                  <p><strong>Parallel Threads:</strong> ${{ github.event.inputs.parallel_threads }}</p>
                  <p><strong>Execution Mode:</strong> Parallel with Enhanced Isolation</p>
              </div>
              <p>📊 Test execution completed with parallel execution and enhanced browser isolation.</p>
              <p>📁 Check the cucumber-report and failsafe-reports directories for detailed test results.</p>
              <p>🔍 Raw test data is available in the downloaded artifact.</p>
          </body>
          </html>
          EOF
          fi

          # List what was generated
          echo "📁 Final generated report files:"
          find target -type f -name "*.html" -o -name "*.json" -o -name "*.xml" | head -20 || echo "No report files found"

          echo "✅ Clean report generation completed (post-parallel execution)"

      - name: 📊 Parse Test Results
        if: always()
        run: |
          echo "📊 Parsing test results for detailed summary..."

          # Debug: List all available report files
          echo "🔍 Available report files:"
          find target -name "*.json" -o -name "*.xml" -o -name "*.html" 2>/dev/null | head -20 || echo "No report files found"

          # Initialize with defaults
          echo "TOTAL_TESTS=0" >> $GITHUB_ENV
          echo "PASSED_TESTS=0" >> $GITHUB_ENV
          echo "FAILED_TESTS=0" >> $GITHUB_ENV
          echo "SKIPPED_TESTS=0" >> $GITHUB_ENV

          # Method 1: Try Cucumber JSON report
          JSON_FOUND=false
          for json_file in target/cucumber-report/cucumber.json target/cucumber-report/*.json target/**/*.json; do
            if [ -f "$json_file" ]; then
              echo "📄 Found JSON report: $json_file"
              JSON_FOUND=true

              # Use Python to parse JSON
              python3 << EOF
          import json
          import os
          import sys

          try:
              with open('$json_file', 'r') as f:
                  data = json.load(f)

              total_scenarios = 0
              passed_scenarios = 0
              failed_scenarios = 0
              skipped_scenarios = 0
              failed_details = []

              print(f"📊 Processing JSON data with {len(data)} features")

              for feature in data:
                  feature_name = feature.get('name', 'Unknown Feature')
                  print(f"  📁 Feature: {feature_name}")

                  for element in feature.get('elements', []):
                      if element.get('type') == 'scenario':
                          total_scenarios += 1
                          scenario_name = element.get('name', 'Unknown Scenario')
                          scenario_tags = [tag['name'] for tag in element.get('tags', [])]

                          # Check scenario status based on steps
                          scenario_status = 'passed'
                          has_steps = False

                          for step in element.get('steps', []):
                              has_steps = True
                              step_result = step.get('result', {})
                              step_status = step_result.get('status', 'undefined')

                              if step_status in ['failed', 'undefined', 'error']:
                                  scenario_status = 'failed'
                                  break
                              elif step_status == 'skipped':
                                  if scenario_status != 'failed':
                                      scenario_status = 'skipped'

                          # If no steps, consider it skipped
                          if not has_steps:
                              scenario_status = 'skipped'

                          print(f"    🧪 Scenario: {scenario_name} - Status: {scenario_status}")

                          if scenario_status == 'passed':
                              passed_scenarios += 1
                          elif scenario_status == 'failed':
                              failed_scenarios += 1
                              failed_details.append({
                                  'name': scenario_name,
                                  'tags': scenario_tags,
                                  'feature': feature_name
                              })
                          else:
                              skipped_scenarios += 1

              print(f"📊 Final counts: Total={total_scenarios}, Passed={passed_scenarios}, Failed={failed_scenarios}, Skipped={skipped_scenarios}")

              # Write results to environment file
              with open(os.environ['GITHUB_ENV'], 'a') as env_file:
                  env_file.write(f"TOTAL_TESTS={total_scenarios}\n")
                  env_file.write(f"PASSED_TESTS={passed_scenarios}\n")
                  env_file.write(f"FAILED_TESTS={failed_scenarios}\n")
                  env_file.write(f"SKIPPED_TESTS={skipped_scenarios}\n")

              # Write failed test details to a file
              if failed_details:
                  with open('failed_tests.txt', 'w') as f:
                      for test in failed_details:
                          f.write(f"FAILED_TEST:{test['name']}|{','.join(test['tags'])}|{test['feature']}\n")
                  print(f"📝 Wrote {len(failed_details)} failed test details to failed_tests.txt")

              print(f"✅ Successfully parsed {total_scenarios} scenarios from JSON")

          except Exception as e:
              print(f"⚠️ Error parsing JSON {json_file}: {e}")
              import traceback
              traceback.print_exc()
          EOF
              break
            fi
          done

          # Method 2: Try XML reports if JSON parsing didn't work
          if [ "$JSON_FOUND" = false ]; then
            echo "⚠️ No Cucumber JSON found, trying XML reports..."

            for xml_file in target/failsafe-reports/TEST-*.xml target/surefire-reports/TEST-*.xml; do
              if [ -f "$xml_file" ]; then
                echo "📄 Found XML report: $xml_file"

                # Parse XML using grep
                TOTAL_TESTS=$(grep -o 'tests="[0-9]*"' "$xml_file" | grep -o '[0-9]*' | head -1 || echo "0")
                FAILED_TESTS=$(grep -o 'failures="[0-9]*"' "$xml_file" | grep -o '[0-9]*' | head -1 || echo "0")
                ERROR_TESTS=$(grep -o 'errors="[0-9]*"' "$xml_file" | grep -o '[0-9]*' | head -1 || echo "0")
                SKIPPED_TESTS=$(grep -o 'skipped="[0-9]*"' "$xml_file" | grep -o '[0-9]*' | head -1 || echo "0")

                # Calculate passed tests
                if [[ "$TOTAL_TESTS" =~ ^[0-9]+$ ]] && [[ "$FAILED_TESTS" =~ ^[0-9]+$ ]] && [[ "$ERROR_TESTS" =~ ^[0-9]+$ ]] && [[ "$SKIPPED_TESTS" =~ ^[0-9]+$ ]]; then
                  PASSED_TESTS=$((TOTAL_TESTS - FAILED_TESTS - ERROR_TESTS - SKIPPED_TESTS))
                  FAILED_TESTS=$((FAILED_TESTS + ERROR_TESTS))

                  echo "📊 XML Parsing Results: Total=$TOTAL_TESTS, Passed=$PASSED_TESTS, Failed=$FAILED_TESTS, Skipped=$SKIPPED_TESTS"

                  echo "TOTAL_TESTS=$TOTAL_TESTS" >> $GITHUB_ENV
                  echo "PASSED_TESTS=$PASSED_TESTS" >> $GITHUB_ENV
                  echo "FAILED_TESTS=$FAILED_TESTS" >> $GITHUB_ENV
                  echo "SKIPPED_TESTS=$SKIPPED_TESTS" >> $GITHUB_ENV
                  break
                fi
              fi
            done
          fi

          echo "✅ Test result parsing completed"

      - name: 📋 Test Results Summary
        if: always()
        run: |
          echo "## 📊 Test Execution Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "| Parameter | Value |" >> $GITHUB_STEP_SUMMARY
          echo "|-----------|-------|" >> $GITHUB_STEP_SUMMARY
          echo "| 🧪 Test Suite | \`${{ github.event.inputs.test_suite }}\` |" >> $GITHUB_STEP_SUMMARY
          echo "| 🏷️ Tags | \`${{ needs.validate-inputs.outputs.tags }}\` |" >> $GITHUB_STEP_SUMMARY
          echo "| 🖥 Environment | \`${{ github.event.inputs.environment }}\` |" >> $GITHUB_STEP_SUMMARY
          echo "| 🌐 Browser | \`${{ github.event.inputs.browser }}\` |" >> $GITHUB_STEP_SUMMARY
          echo "| ⚡ Threads | \`${{ github.event.inputs.parallel_threads }}\` |" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          # Add detailed test results summary
          echo "## 📈 **Test Results Overview**" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "| Metric | Count |" >> $GITHUB_STEP_SUMMARY
          echo "|--------|-------|" >> $GITHUB_STEP_SUMMARY
          echo "| 📊 **Total Tests** | \`${TOTAL_TESTS:-Unknown}\` |" >> $GITHUB_STEP_SUMMARY
          echo "| ✅ **Passed** | \`${PASSED_TESTS:-Unknown}\` |" >> $GITHUB_STEP_SUMMARY
          echo "| ❌ **Failed** | \`${FAILED_TESTS:-Unknown}\` |" >> $GITHUB_STEP_SUMMARY
          echo "| ⏭️ **Skipped** | \`${SKIPPED_TESTS:-Unknown}\` |" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          # Add test execution status
          if [ "${TEST_EXIT_CODE:-1}" = "0" ]; then
            echo "## ✅ **Overall Status: PASSED** 🎉" >> $GITHUB_STEP_SUMMARY
          else
            echo "## ❌ **Overall Status: FAILED** (Exit Code: ${TEST_EXIT_CODE:-Unknown})" >> $GITHUB_STEP_SUMMARY
          fi
          echo "" >> $GITHUB_STEP_SUMMARY

          # Add failed test details if any
          if [ -f "failed_tests.txt" ] && [ "${FAILED_TESTS:-0}" != "0" ] && [ "${FAILED_TESTS}" != "Unknown" ]; then
            echo "## 🔍 **Failed Test Details**" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "| Scenario | Tags | Feature |" >> $GITHUB_STEP_SUMMARY
            echo "|----------|------|---------|" >> $GITHUB_STEP_SUMMARY

            while IFS= read -r line; do
              if [[ $line == FAILED_TEST:* ]]; then
                # Parse the line: FAILED_TEST:name|tags|feature
                test_info=${line#FAILED_TEST:}
                test_name=$(echo "$test_info" | cut -d'|' -f1)
                test_tags=$(echo "$test_info" | cut -d'|' -f2)
                test_feature=$(echo "$test_info" | cut -d'|' -f3)

                # Clean up tags for display
                if [ -n "$test_tags" ]; then
                  formatted_tags="\`${test_tags}\`"
                else
                  formatted_tags="No tags"
                fi

                echo "| **${test_name}** | ${formatted_tags} | ${test_feature} |" >> $GITHUB_STEP_SUMMARY
              fi
            done < failed_tests.txt

            echo "" >> $GITHUB_STEP_SUMMARY
            echo "💡 **Tip**: Download the Serenity reports below for detailed failure analysis with screenshots and step-by-step execution details." >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
          fi

          # Check if reports exist and add storage quota notice
          if [ -d "target" ]; then
            echo "## ⚠️ **Test Results Status**" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "📊 **Test reports were generated successfully** but are **temporarily unavailable for download** due to GitHub Actions artifact storage quota limits." >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "### 🚫 **Artifact Upload Temporarily Disabled**" >> $GITHUB_STEP_SUMMARY
            echo "**Reason:** GitHub Actions artifact storage quota has been exceeded" >> $GITHUB_STEP_SUMMARY
            echo "**Error:** \`Artifact storage quota has been hit. Unable to upload any new artifacts.\`" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "### 📋 **Generated Reports (Not Uploaded):**" >> $GITHUB_STEP_SUMMARY
            echo "- 📊 Serenity HTML reports (\`target/site/serenity/index.html\`)" >> $GITHUB_STEP_SUMMARY
            echo "- 🥒 Cucumber reports (HTML/JSON)" >> $GITHUB_STEP_SUMMARY
            echo "- 📄 Maven Failsafe reports (XML)" >> $GITHUB_STEP_SUMMARY
            echo "- 📋 Test execution summary" >> $GITHUB_STEP_SUMMARY

            if [ "${TEST_EXIT_CODE:-1}" != "0" ]; then
              echo "- 📝 Test failure logs (generated but not uploaded)" >> $GITHUB_STEP_SUMMARY
            fi

            echo "" >> $GITHUB_STEP_SUMMARY
            echo "### 🔧 **To Re-enable Artifact Downloads:**" >> $GITHUB_STEP_SUMMARY
            echo "1. Resolve the GitHub Actions storage quota issue" >> $GITHUB_STEP_SUMMARY
            echo "2. Uncomment the upload steps in \`.github/workflows/test-run-pipeline.yml\`" >> $GITHUB_STEP_SUMMARY
            echo "3. Look for sections marked with: \`⚠️ TEMPORARILY COMMENTED OUT DUE TO GITHUB ACTIONS ARTIFACT STORAGE QUOTA EXCEEDED\`" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "📖 **More info:** [GitHub Actions Storage Billing](https://docs.github.com/en/billing/managing-billing-for-github-actions/about-billing-for-github-actions#calculating-minute-and-storage-spending)" >> $GITHUB_STEP_SUMMARY
          else
            echo "❌ **No test artifacts found**" >> $GITHUB_STEP_SUMMARY
            echo "The test execution may have failed before generating any reports." >> $GITHUB_STEP_SUMMARY
          fi

      - name: 📁 Verify Generated Test Artifacts
        if: always()
        run: |
          echo "📁 Verifying generated test artifacts (upload temporarily disabled due to storage quota)..."

          # Check if target directory exists
          if [ -d "target" ]; then
            echo "✅ Target directory exists"
            echo "📊 Target directory contents:"
            ls -la target/ || echo "Target directory is empty"

            # Check specific subdirectories
            for dir in "site/serenity" "cucumber-report" "failsafe-reports"; do
              if [ -d "target/$dir" ]; then
                echo "✅ target/$dir exists"
                ls -la "target/$dir" | head -10
              else
                echo "❌ target/$dir does not exist"
                mkdir -p "target/$dir"
                echo "📁 Created target/$dir"
              fi
            done
          else
            echo "❌ Target directory does not exist, creating it..."
            mkdir -p target/site/serenity target/cucumber-report target/failsafe-reports
          fi

          # Create a summary file
          cat > target/test-execution-summary.txt << EOF
          Test Execution Summary
          =====================
          Branch: ${{ github.event.inputs.branch }}
          Test Suite: ${{ github.event.inputs.test_suite }}
          Tags: ${{ needs.validate-inputs.outputs.tags }}
          Environment: ${{ github.event.inputs.environment }}
          Browser: ${{ github.event.inputs.browser }}
          Parallel Threads: ${{ github.event.inputs.parallel_threads }}
          Test Exit Code: ${TEST_EXIT_CODE:-"Unknown"}
          Execution Time: $(date)

          NOTE: Artifact upload temporarily disabled due to GitHub Actions storage quota exceeded.
          Reports generated locally in target/ directory but not uploaded to GitHub Actions.
          EOF

          echo "📄 Created test execution summary (artifacts not uploaded due to storage quota)"

      # ⚠️ TEMPORARILY COMMENTED OUT DUE TO GITHUB ACTIONS ARTIFACT STORAGE QUOTA EXCEEDED
      # 📤 Upload Serenity Reports - UNCOMMENT AFTER RESOLVING STORAGE QUOTA ISSUE
      # Error: "Artifact storage quota has been hit. Unable to upload any new artifacts."
      # More info: https://docs.github.com/en/billing/managing-billing-for-github-actions/about-billing-for-github-actions#calculating-minute-and-storage-spending
      #
      # TO UNCOMMENT: Remove the "# " from the beginning of each line below when storage issue is resolved
      # - name: 📤 Upload Serenity Reports
      #   if: always()
      #   uses: actions/upload-artifact@v4
      #   with:
      #     name: serenity-reports-${{ github.event.inputs.browser }}-${{ github.event.inputs.environment }}-${{ github.run_number }}
      #     path: target/
      #     retention-days: 1
      #     if-no-files-found: warn

      # ⚠️ TEMPORARILY COMMENTED OUT DUE TO GITHUB ACTIONS ARTIFACT STORAGE QUOTA EXCEEDED
      # 📤 Upload Test Logs on Failure - UNCOMMENT AFTER RESOLVING STORAGE QUOTA ISSUE
      # Error: "Artifact storage quota has been hit. Unable to upload any new artifacts."
      #
      # TO UNCOMMENT: Remove the "# " from the beginning of each line below when storage issue is resolved
      # - name: 📤 Upload Test Logs on Failure
      #   if: always() && env.TEST_EXIT_CODE != '0'
      #   uses: actions/upload-artifact@v4
      #   with:
      #     name: test-failure-logs-${{ github.run_number }}
      #     path: |
      #       *.log
      #       **/*.log
      #       target/**/*.log
      #     retention-days: 1
      #     if-no-files-found: ignore