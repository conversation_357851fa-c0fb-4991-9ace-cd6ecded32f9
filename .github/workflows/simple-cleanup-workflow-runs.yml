name: 🗑️ Simple Cleanup Workflow Runs

on:
  workflow_dispatch:
    inputs:
      confirm_deletion:
        description: 'Type "DELETE ALL WORKFLOWS" to confirm deletion'
        required: true
        type: string
      days_to_keep:
        description: 'Keep workflows from last N days (0 = delete all)'
        required: false
        default: '0'
        type: string
      dry_run:
        description: 'Dry run - show what would be deleted without actually deleting'
        required: false
        default: true
        type: boolean

permissions:
  actions: write
  contents: read

jobs:
  cleanup-workflow-runs:
    name: 🗑️ Delete Workflow Runs (Simple)
    runs-on: ubuntu-latest
    
    steps:
      - name: 🔐 Validate Confirmation
        run: |
          if [[ "${{ github.event.inputs.confirm_deletion }}" != "DELETE ALL WORKFLOWS" ]]; then
            echo "❌ ERROR: Confirmation text does not match!"
            echo "Please type exactly: DELETE ALL WORKFLOWS"
            exit 1
          fi
          echo "✅ Confirmation validated"

      - name: 🗑️ Simple Cleanup All Workflow Runs
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          REPO: ${{ github.repository }}
          DAYS_TO_KEEP: ${{ github.event.inputs.days_to_keep }}
          DRY_RUN: ${{ github.event.inputs.dry_run }}
          CURRENT_RUN_ID: ${{ github.run_id }}
        run: |
          echo "🚀 Starting simple workflow cleanup..."
          echo "Repository: $REPO"
          echo "Days to keep: $DAYS_TO_KEEP"
          echo "Dry run: $DRY_RUN"
          echo "Current run ID: $CURRENT_RUN_ID"
          echo ""
          
          # Calculate cutoff date if needed
          if [[ "$DAYS_TO_KEEP" -gt 0 ]]; then
            CUTOFF_DATE=$(date -d "${DAYS_TO_KEEP} days ago" -u +"%Y-%m-%dT%H:%M:%SZ")
            echo "📅 Cutoff date: $CUTOFF_DATE"
          else
            echo "📅 Deleting ALL workflow runs"
          fi
          echo ""
          
          TOTAL_DELETED=0
          TOTAL_SKIPPED=0
          TOTAL_ERRORS=0
          
          # Get all workflow runs from the repository (not per workflow)
          echo "🔍 Getting all workflow runs from repository..."
          PAGE=1
          
          while true; do
            echo "📄 Fetching page $PAGE of all workflow runs..."
            
            # Get all runs across all workflows
            RUNS_DATA=$(gh api repos/$REPO/actions/runs \
              --method GET \
              --field per_page=100 \
              --field page=$PAGE 2>/dev/null)
            
            if [[ $? -ne 0 ]] || [[ -z "$RUNS_DATA" ]]; then
              echo "❌ Failed to get workflow runs for page $PAGE"
              break
            fi
            
            RUNS=$(echo "$RUNS_DATA" | jq -r '.workflow_runs[].id' 2>/dev/null)
            
            if [[ -z "$RUNS" ]]; then
              echo "📄 No more runs found"
              break
            fi
            
            RUNS_COUNT=$(echo "$RUNS" | wc -w)
            echo "📊 Found $RUNS_COUNT runs on page $PAGE"
            
            # Process each run
            for RUN_ID in $RUNS; do
              # Skip the currently running workflow
              if [[ "$RUN_ID" == "$CURRENT_RUN_ID" ]]; then
                echo "⏭️ Skipping current run ($RUN_ID)"
                ((TOTAL_SKIPPED++))
                continue
              fi
              
              # Get run details from the data we already have
              RUN_DETAILS=$(echo "$RUNS_DATA" | jq -r ".workflow_runs[] | select(.id == $RUN_ID)")
              
              if [[ -z "$RUN_DETAILS" ]]; then
                echo "⚠️ Could not find details for run $RUN_ID"
                ((TOTAL_ERRORS++))
                continue
              fi
              
              RUN_DATE=$(echo "$RUN_DETAILS" | jq -r '.created_at')
              RUN_STATUS=$(echo "$RUN_DETAILS" | jq -r '.status')
              RUN_CONCLUSION=$(echo "$RUN_DETAILS" | jq -r '.conclusion // "null"')
              WORKFLOW_NAME=$(echo "$RUN_DETAILS" | jq -r '.name // "Unknown"')
              
              # Skip in-progress runs
              if [[ "$RUN_STATUS" == "in_progress" ]]; then
                echo "⏭️ Skipping in-progress run $RUN_ID ($WORKFLOW_NAME)"
                ((TOTAL_SKIPPED++))
                continue
              fi
              
              # Check if we should delete based on date
              SHOULD_DELETE=true
              if [[ "$DAYS_TO_KEEP" -gt 0 ]]; then
                if [[ "$RUN_DATE" > "$CUTOFF_DATE" ]]; then
                  SHOULD_DELETE=false
                fi
              fi
              
              if [[ "$SHOULD_DELETE" == "true" ]]; then
                if [[ "$DRY_RUN" == "true" ]]; then
                  echo "🔍 [DRY RUN] Would delete: $RUN_ID | $WORKFLOW_NAME | $RUN_DATE | $RUN_STATUS"
                  ((TOTAL_DELETED++))
                else
                  echo "🗑️ Deleting: $RUN_ID | $WORKFLOW_NAME | $RUN_DATE | $RUN_STATUS"
                  if gh api repos/$REPO/actions/runs/$RUN_ID --method DELETE 2>/dev/null; then
                    echo "✅ Deleted run $RUN_ID"
                    ((TOTAL_DELETED++))
                  else
                    echo "❌ Failed to delete run $RUN_ID"
                    ((TOTAL_ERRORS++))
                  fi
                fi
              else
                echo "⏭️ Keeping: $RUN_ID | $WORKFLOW_NAME | $RUN_DATE (within keep period)"
                ((TOTAL_SKIPPED++))
              fi
              
              # Small delay to avoid rate limiting
              sleep 0.1
            done
            
            # Check if there are more pages
            TOTAL_COUNT=$(echo "$RUNS_DATA" | jq -r '.total_count')
            CURRENT_COUNT=$(($PAGE * 100))
            
            echo "📊 Page $PAGE complete. Processed: $CURRENT_COUNT, Total available: $TOTAL_COUNT"
            
            if [[ $RUNS_COUNT -eq 0 ]] || [[ $CURRENT_COUNT -ge $TOTAL_COUNT ]]; then
              echo "📄 Reached end of workflow runs"
              break
            fi
            
            ((PAGE++))
          done
          
          echo ""
          echo "🎯 CLEANUP SUMMARY"
          echo "=================="
          echo "Repository: $REPO"
          echo "Total processed: $((TOTAL_DELETED + TOTAL_SKIPPED + TOTAL_ERRORS))"
          echo "Total deleted: $TOTAL_DELETED"
          echo "Total skipped: $TOTAL_SKIPPED"
          echo "Total errors: $TOTAL_ERRORS"
          echo "Dry run: $DRY_RUN"
          
          if [[ "$DRY_RUN" == "true" ]]; then
            echo ""
            echo "ℹ️ This was a dry run. No workflows were actually deleted."
            echo "To perform actual deletion, run again with 'Dry run' unchecked."
          else
            echo ""
            echo "✅ Cleanup completed!"
          fi
