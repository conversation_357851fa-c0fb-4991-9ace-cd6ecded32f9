name: 🗑️ Cleanup Workflow Runs

on:
  workflow_dispatch:
    inputs:
      confirm_deletion:
        description: 'Type "DELETE ALL WORKFLOWS" to confirm deletion'
        required: true
        type: string
      days_to_keep:
        description: 'Keep workflows from last N days (0 = delete all)'
        required: false
        default: '0'
        type: string
      workflow_name:
        description: 'Specific workflow to delete (leave empty for all workflows)'
        required: false
        type: string
      dry_run:
        description: 'Dry run - show what would be deleted without actually deleting'
        required: false
        default: false
        type: boolean

permissions:
  actions: write
  contents: read

jobs:
  cleanup-workflow-runs:
    name: 🗑️ Delete Workflow Runs
    runs-on: ubuntu-latest
    
    steps:
      - name: 🔐 Validate Confirmation
        run: |
          if [[ "${{ github.event.inputs.confirm_deletion }}" != "DELETE ALL WORKFLOWS" ]]; then
            echo "❌ ERROR: Confirmation text does not match!"
            echo "Please type exactly: DELETE ALL WORKFLOWS"
            exit 1
          fi
          echo "✅ Confirmation validated"

      - name: 🔍 Display Cleanup Configuration
        run: |
          echo "🗑️ Workflow Cleanup Configuration:"
          echo "=================================="
          echo "Repository: ${{ github.repository }}"
          echo "Days to keep: ${{ github.event.inputs.days_to_keep }}"
          echo "Specific workflow: ${{ github.event.inputs.workflow_name || 'ALL WORKFLOWS' }}"
          echo "Dry run: ${{ github.event.inputs.dry_run }}"
          echo "Triggered by: ${{ github.actor }}"
          echo ""

      - name: 🗑️ Delete Workflow Runs
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          REPO: ${{ github.repository }}
          DAYS_TO_KEEP: ${{ github.event.inputs.days_to_keep }}
          WORKFLOW_NAME: ${{ github.event.inputs.workflow_name }}
          DRY_RUN: ${{ github.event.inputs.dry_run }}
        run: |
          # Don't exit on errors - handle them gracefully
          set +e

          echo "🚀 Starting workflow cleanup process..."
          
          # Calculate cutoff date if days_to_keep > 0
          if [[ "$DAYS_TO_KEEP" -gt 0 ]]; then
            if [[ "$OSTYPE" == "darwin"* ]]; then
              # macOS date command
              CUTOFF_DATE=$(date -v-${DAYS_TO_KEEP}d -u +"%Y-%m-%dT%H:%M:%SZ")
            else
              # Linux date command
              CUTOFF_DATE=$(date -d "${DAYS_TO_KEEP} days ago" -u +"%Y-%m-%dT%H:%M:%SZ")
            fi
            echo "📅 Cutoff date: $CUTOFF_DATE (keeping workflows newer than this)"
          else
            echo "📅 Deleting ALL workflow runs (no cutoff date)"
          fi
          
          # Use repository-wide approach to get ALL runs (including from deleted workflows)
          if [[ -n "$WORKFLOW_NAME" ]]; then
            echo "🔍 Getting runs for specific workflow: $WORKFLOW_NAME"
            echo "⚠️ Note: This will only work if the workflow still exists"
            WORKFLOW_IDS=$(gh api repos/$REPO/actions/workflows --jq ".workflows[] | select(.name == \"$WORKFLOW_NAME\") | .id")
            if [[ -z "$WORKFLOW_IDS" ]]; then
              echo "❌ No workflow found with name: $WORKFLOW_NAME"
              echo "💡 Tip: Leave workflow name empty to clean ALL runs (including deleted workflows)"
              exit 1
            fi
            USE_WORKFLOW_SPECIFIC=true
          else
            echo "🔍 Getting ALL workflow runs from repository (including deleted workflows)..."
            USE_WORKFLOW_SPECIFIC=false
          fi

          TOTAL_DELETED=0
          TOTAL_SKIPPED=0

          if [[ "$USE_WORKFLOW_SPECIFIC" == "true" ]]; then
            # Process specific workflow (old method)
            for WORKFLOW_ID in $WORKFLOW_IDS; do
            echo ""
            echo "🔄 Processing workflow ID: $WORKFLOW_ID"
            
            # Get workflow name and total run count with error handling
            WORKFLOW_INFO=$(gh api repos/$REPO/actions/workflows/$WORKFLOW_ID 2>/dev/null)
            if [[ $? -ne 0 ]] || [[ -z "$WORKFLOW_INFO" ]]; then
              echo "⚠️ Could not get workflow info for ID: $WORKFLOW_ID (might be deleted)"
              continue
            fi

            CURRENT_WORKFLOW_NAME=$(echo "$WORKFLOW_INFO" | jq -r '.name')
            echo "📝 Workflow name: $CURRENT_WORKFLOW_NAME"

            # Get total run count for this workflow with error handling
            TOTAL_RUNS_INFO=$(gh api repos/$REPO/actions/workflows/$WORKFLOW_ID/runs --field per_page=1 2>/dev/null)
            if [[ $? -ne 0 ]] || [[ -z "$TOTAL_RUNS_INFO" ]]; then
              echo "⚠️ Could not get runs for workflow: $CURRENT_WORKFLOW_NAME"
              echo "📊 Total runs for this workflow: Unknown (API error)"
              # Continue anyway - we'll try to get runs with pagination
            else
              TOTAL_RUNS_COUNT=$(echo "$TOTAL_RUNS_INFO" | jq -r '.total_count')
              echo "📊 Total runs for this workflow: $TOTAL_RUNS_COUNT"
            fi
            
            # Get workflow runs with pagination
            PAGE=1
            WORKFLOW_DELETED=0
            WORKFLOW_SKIPPED=0

            while true; do
              echo "📄 Fetching page $PAGE of workflow runs..."

              # Get ALL workflow runs using a more comprehensive approach with error handling
              RUNS_DATA_ALL=""

              # Try different methods to ensure we get older runs
              if [[ $PAGE -eq 1 ]]; then
                # First, try to get runs from all time periods
                echo "🔍 Trying to get runs with created filter..."
                RUNS_DATA_ALL=$(gh api repos/$REPO/actions/workflows/$WORKFLOW_ID/runs \
                  --method GET \
                  --field per_page=100 \
                  --field page=$PAGE \
                  --field created=">=2020-01-01" 2>/dev/null)

                # If that fails, try without the created filter
                if [[ $? -ne 0 ]] || [[ -z "$RUNS_DATA_ALL" ]]; then
                  echo "⚠️ Created filter failed, trying without filter..."
                  RUNS_DATA_ALL=$(gh api repos/$REPO/actions/workflows/$WORKFLOW_ID/runs \
                    --method GET \
                    --field per_page=100 \
                    --field page=$PAGE 2>/dev/null)
                fi
              else
                RUNS_DATA_ALL=$(gh api repos/$REPO/actions/workflows/$WORKFLOW_ID/runs \
                  --method GET \
                  --field per_page=100 \
                  --field page=$PAGE 2>/dev/null)
              fi

              # Check if API call failed
              if [[ $? -ne 0 ]] || [[ -z "$RUNS_DATA_ALL" ]]; then
                echo "❌ Failed to get workflow runs for page $PAGE"
                break
              fi

              RUNS=$(echo "$RUNS_DATA_ALL" | jq -r '.workflow_runs[].id' 2>/dev/null)

              if [[ -z "$RUNS" ]]; then
                echo "📄 No more runs found for this workflow"
                break
              fi

              echo "📊 Found $(echo "$RUNS" | wc -w) runs on page $PAGE"
              
              # Process each run
              for RUN_ID in $RUNS; do
                # Skip the currently running workflow (this cleanup run itself)
                if [[ "$RUN_ID" == "${{ github.run_id }}" ]]; then
                  echo "⏭️ Skipping current workflow run ($RUN_ID) - cannot delete self"
                  ((WORKFLOW_SKIPPED++))
                  continue
                fi

                # Get run details with error handling
                RUN_INFO=$(gh api repos/$REPO/actions/runs/$RUN_ID 2>/dev/null)
                if [[ $? -ne 0 ]] || [[ -z "$RUN_INFO" ]]; then
                  echo "⚠️ Could not get details for run $RUN_ID (might be deleted)"
                  ((WORKFLOW_SKIPPED++))
                  continue
                fi

                RUN_DATE=$(echo "$RUN_INFO" | jq -r '.created_at')
                RUN_STATUS=$(echo "$RUN_INFO" | jq -r '.status')
                RUN_CONCLUSION=$(echo "$RUN_INFO" | jq -r '.conclusion')

                # Skip runs that are currently in progress (except our own, which we already skipped)
                if [[ "$RUN_STATUS" == "in_progress" ]]; then
                  echo "⏭️ Skipping in-progress run $RUN_ID (created: $RUN_DATE)"
                  ((WORKFLOW_SKIPPED++))
                  continue
                fi

                # Check if we should keep this run based on date
                SHOULD_DELETE=true
                if [[ "$DAYS_TO_KEEP" -gt 0 ]]; then
                  if [[ "$RUN_DATE" > "$CUTOFF_DATE" ]]; then
                    SHOULD_DELETE=false
                  fi
                fi
                
                if [[ "$SHOULD_DELETE" == "true" ]]; then
                  if [[ "$DRY_RUN" == "true" ]]; then
                    echo "🔍 [DRY RUN] Would delete run $RUN_ID (created: $RUN_DATE, status: $RUN_STATUS, conclusion: $RUN_CONCLUSION)"
                    ((WORKFLOW_DELETED++))
                  else
                    echo "🗑️ Deleting run $RUN_ID (created: $RUN_DATE, status: $RUN_STATUS, conclusion: $RUN_CONCLUSION)"
                    if gh api repos/$REPO/actions/runs/$RUN_ID --method DELETE; then
                      ((WORKFLOW_DELETED++))
                      echo "✅ Successfully deleted run $RUN_ID"
                    else
                      echo "❌ Failed to delete run $RUN_ID"
                    fi
                  fi
                else
                  echo "⏭️ Keeping run $RUN_ID (created: $RUN_DATE - within keep period)"
                  ((WORKFLOW_SKIPPED++))
                fi
                
                # Small delay to avoid rate limiting
                sleep 0.1
              done
              
              ((PAGE++))
              
              # Check if there are more pages
              TOTAL_COUNT=$(echo "$RUNS_DATA_ALL" | jq -r '.total_count')
              CURRENT_COUNT=$((($PAGE - 1) * 100))
              RUNS_ON_PAGE=$(echo "$RUNS" | wc -w)

              echo "📊 Page $PAGE: Found $RUNS_ON_PAGE runs, Total: $TOTAL_COUNT, Processed so far: $CURRENT_COUNT"

              if [[ $RUNS_ON_PAGE -eq 0 ]] || [[ $CURRENT_COUNT -ge $TOTAL_COUNT ]]; then
                echo "📄 Reached end of workflow runs"
                break
              fi
            done
            
            echo "📊 Workflow '$CURRENT_WORKFLOW_NAME' summary:"
            echo "   - Deleted: $WORKFLOW_DELETED runs"
            echo "   - Skipped: $WORKFLOW_SKIPPED runs"
            
            TOTAL_DELETED=$((TOTAL_DELETED + WORKFLOW_DELETED))
            TOTAL_SKIPPED=$((TOTAL_SKIPPED + WORKFLOW_SKIPPED))
          done

          else
            # Repository-wide cleanup (gets ALL runs including from deleted workflows)
            echo "🌍 Using repository-wide cleanup to catch ALL workflow runs..."
            echo "📋 This includes runs from deleted workflows that still appear in Actions tab"
            echo ""

            PAGE=1

            while true; do
              echo "📄 Fetching page $PAGE of ALL workflow runs..."

              # Get all runs across all workflows (including deleted ones)
              RUNS_DATA=$(gh api repos/$REPO/actions/runs \
                --method GET \
                --field per_page=100 \
                --field page=$PAGE 2>/dev/null)

              if [[ $? -ne 0 ]] || [[ -z "$RUNS_DATA" ]]; then
                echo "❌ Failed to get workflow runs for page $PAGE"
                break
              fi

              RUNS=$(echo "$RUNS_DATA" | jq -r '.workflow_runs[].id' 2>/dev/null)

              if [[ -z "$RUNS" ]]; then
                echo "📄 No more runs found"
                break
              fi

              RUNS_COUNT=$(echo "$RUNS" | wc -w)
              echo "📊 Found $RUNS_COUNT runs on page $PAGE"

              # Process each run
              for RUN_ID in $RUNS; do
                # Skip the currently running workflow
                if [[ "$RUN_ID" == "${{ github.run_id }}" ]]; then
                  echo "⏭️ Skipping current run ($RUN_ID)"
                  ((TOTAL_SKIPPED++))
                  continue
                fi

                # Get run details from the data we already have
                RUN_DETAILS=$(echo "$RUNS_DATA" | jq -r ".workflow_runs[] | select(.id == $RUN_ID)")

                if [[ -z "$RUN_DETAILS" ]]; then
                  echo "⚠️ Could not find details for run $RUN_ID"
                  ((TOTAL_SKIPPED++))
                  continue
                fi

                RUN_DATE=$(echo "$RUN_DETAILS" | jq -r '.created_at')
                RUN_STATUS=$(echo "$RUN_DETAILS" | jq -r '.status')
                RUN_CONCLUSION=$(echo "$RUN_DETAILS" | jq -r '.conclusion // "null"')
                WORKFLOW_NAME=$(echo "$RUN_DETAILS" | jq -r '.name // "Unknown"')

                # Skip in-progress runs
                if [[ "$RUN_STATUS" == "in_progress" ]]; then
                  echo "⏭️ Skipping in-progress run $RUN_ID ($WORKFLOW_NAME)"
                  ((TOTAL_SKIPPED++))
                  continue
                fi

                # Check if we should delete based on date
                SHOULD_DELETE=true
                if [[ "$DAYS_TO_KEEP" -gt 0 ]]; then
                  if [[ "$RUN_DATE" > "$CUTOFF_DATE" ]]; then
                    SHOULD_DELETE=false
                  fi
                fi

                if [[ "$SHOULD_DELETE" == "true" ]]; then
                  if [[ "$DRY_RUN" == "true" ]]; then
                    echo "🔍 [DRY RUN] Would delete: $RUN_ID | $WORKFLOW_NAME | $RUN_DATE | $RUN_STATUS"
                    ((TOTAL_DELETED++))
                  else
                    echo "🗑️ Deleting: $RUN_ID | $WORKFLOW_NAME | $RUN_DATE | $RUN_STATUS"
                    if gh api repos/$REPO/actions/runs/$RUN_ID --method DELETE 2>/dev/null; then
                      echo "✅ Deleted run $RUN_ID"
                      ((TOTAL_DELETED++))
                    else
                      echo "❌ Failed to delete run $RUN_ID"
                      ((TOTAL_SKIPPED++))
                    fi
                  fi
                else
                  echo "⏭️ Keeping: $RUN_ID | $WORKFLOW_NAME | $RUN_DATE (within keep period)"
                  ((TOTAL_SKIPPED++))
                fi

                # Small delay to avoid rate limiting
                sleep 0.1
              done

              # Check if there are more pages
              TOTAL_COUNT=$(echo "$RUNS_DATA" | jq -r '.total_count')
              CURRENT_COUNT=$(($PAGE * 100))

              echo "📊 Page $PAGE complete. Processed: $CURRENT_COUNT, Total available: $TOTAL_COUNT"

              if [[ $RUNS_COUNT -eq 0 ]] || [[ $CURRENT_COUNT -ge $TOTAL_COUNT ]]; then
                echo "📄 Reached end of workflow runs"
                break
              fi

              ((PAGE++))
            done
          fi
          
          echo ""
          echo "🎯 CLEANUP SUMMARY"
          echo "=================="
          echo "Repository: $REPO"
          echo "Total deleted: $TOTAL_DELETED workflow runs"
          echo "Total skipped: $TOTAL_SKIPPED workflow runs"
          echo "Dry run: $DRY_RUN"
          
          if [[ "$DRY_RUN" == "true" ]]; then
            echo ""
            echo "ℹ️ This was a dry run. No workflows were actually deleted."
            echo "To perform the actual deletion, run again with 'Dry run' set to false."
          else
            echo ""
            echo "✅ Cleanup completed successfully!"
          fi

      - name: 📊 Final Summary
        if: always()
        run: |
          echo ""
          echo "🏁 Workflow Cleanup Complete"
          echo "============================"
          echo "✅ All cleanup operations finished"
          echo "📝 Check the logs above for detailed results"
          echo ""
          echo "💡 Tips:"
          echo "- Use 'Dry run' to preview what would be deleted"
          echo "- Set 'Days to keep' > 0 to preserve recent workflows"
          echo "- Specify 'Workflow name' to target specific workflows"
          echo ""
          echo "⚠️ Remember: Deleted workflow runs cannot be recovered!"
