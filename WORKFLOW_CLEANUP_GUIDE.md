# 🗑️ Workflow Cleanup Guide

## Overview
The **Cleanup Workflow Runs** workflow helps you delete old GitHub Actions workflow runs from **ALL workflows** in your repository to free up storage space and keep your repository clean.

### 🎯 What It Cleans Up
This workflow deletes runs from **ALL workflows** in your repository, including:
- ✅ **Current active workflows** (like "Team Test Execution")
- ✅ **Orphaned runs from deleted workflows** - the key feature you need!
- ✅ **Old workflow runs** from deleted `.yml` files that still appear in Actions tab
- ✅ **Any workflow** that has ever run in your repository history
- ✅ **Failed, cancelled, or completed** workflow runs
- ✅ **Workflows with any name** - it finds them all automatically

### 🔑 **Key Feature: Deleted Workflow Cleanup**
**The main problem this solves:** When you delete a workflow `.yml` file, GitHub keeps all the historical runs from that workflow. These "orphaned" runs still appear in your Actions tab and consume storage, but they're not associated with any existing workflow file.

**Our solution:** When you leave the "Workflow Name" field **empty**, the cleanup uses a repository-wide approach that finds and deletes ALL workflow runs, including those from deleted workflows.

## 🚀 How to Use

### 1. Navigate to GitHub Actions
1. Go to your repository on GitHub
2. Click on the **Actions** tab
3. Find **"🗑️ Cleanup Workflow Runs"** in the workflow list
4. Click **"Run workflow"**

### 2. Configure Cleanup Options

#### **Required Parameters:**

**Confirm Deletion:**
- **Input**: Type exactly `DELETE ALL WORKFLOWS`
- **Purpose**: Safety confirmation to prevent accidental deletions
- **Example**: `DELETE ALL WORKFLOWS`

#### **Optional Parameters:**

**Days to Keep:**
- **Input**: Number (0 = delete all)
- **Purpose**: Keep workflows from the last N days
- **Examples**: 
  - `0` = Delete all workflows
  - `7` = Keep workflows from last 7 days, delete older ones
  - `30` = Keep workflows from last 30 days, delete older ones

**Workflow Name:**
- **Input**: Exact workflow name (leave empty for all)
- **Purpose**: Target specific workflow for deletion
- **Examples**:
  - Leave empty = Delete from all workflows
  - `Team Test Execution` = Delete only from this workflow
  - `🗑️ Cleanup Workflow Runs` = Delete only cleanup workflow runs

**Dry Run:**
- **Input**: true/false checkbox
- **Purpose**: Preview what would be deleted without actually deleting
- **Recommendation**: Always run with `true` first to see what will be deleted

## 📋 Usage Examples

### Example 1: Delete All Workflows (Dry Run First)
```
Confirm Deletion: DELETE ALL WORKFLOWS
Days to Keep: 0
Workflow Name: (empty)
Dry Run: ✅ true
```
**Result**: Shows what would be deleted without actually deleting anything.

### Example 2: Actually Delete All Workflows
```
Confirm Deletion: DELETE ALL WORKFLOWS
Days to Keep: 0
Workflow Name: (empty)
Dry Run: ❌ false
```
**Result**: Deletes ALL workflow runs from ALL workflows.

### Example 3: Keep Recent Workflows, Delete Old Ones
```
Confirm Deletion: DELETE ALL WORKFLOWS
Days to Keep: 30
Workflow Name: (empty)
Dry Run: ❌ false
```
**Result**: Keeps workflows from last 30 days, deletes everything older.

### Example 4: Clean Up Specific Workflow Only
```
Confirm Deletion: DELETE ALL WORKFLOWS
Days to Keep: 0
Workflow Name: Team Test Execution
Dry Run: ❌ false
```
**Result**: Deletes ALL runs from "Team Test Execution" workflow only.

### Example 5: Conservative Cleanup
```
Confirm Deletion: DELETE ALL WORKFLOWS
Days to Keep: 7
Workflow Name: (empty)
Dry Run: ❌ false
```
**Result**: Keeps last 7 days of all workflows, deletes everything older.

## ⚠️ Important Safety Notes

### 🔒 Safety Features
- **Confirmation Required**: Must type exact confirmation text
- **Dry Run Option**: Preview deletions before executing
- **Detailed Logging**: Shows exactly what's being deleted
- **Granular Control**: Target specific workflows or time periods

### ⚠️ Warnings
- **Irreversible**: Deleted workflow runs cannot be recovered
- **Artifacts Lost**: All artifacts associated with deleted runs are also deleted
- **History Lost**: Workflow run history and logs are permanently removed

### 🛡️ Best Practices
1. **Always dry run first**: Use `Dry Run: true` to preview deletions
2. **Start conservative**: Begin with shorter retention periods (e.g., 7-30 days)
3. **Target specific workflows**: Clean up one workflow at a time initially
4. **Monitor storage**: Check GitHub storage usage before/after cleanup
5. **Document important runs**: Download any important reports before cleanup

## 📊 What Gets Deleted

### ✅ Deleted Items:
- Workflow run records
- Workflow run logs
- Associated artifacts (reports, files, etc.)
- Run status and timing information
- Step-by-step execution details

### ❌ NOT Deleted:
- Workflow definition files (`.yml` files)
- Repository code and commits
- Issues, pull requests, or other repository data
- Workflow secrets and variables

## 🔍 Monitoring Results

### During Execution:
- Real-time progress updates
- Count of deleted vs. skipped runs
- Detailed information about each deletion
- Error reporting for failed deletions

### After Completion:
- Summary of total deletions
- Breakdown by workflow
- Storage space freed up
- Recommendations for future cleanups

## 🆘 Troubleshooting

### Common Issues:

**"Confirmation text does not match"**
- Solution: Type exactly `DELETE ALL WORKFLOWS` (case-sensitive)

**"No workflow found with name: [name]"**
- Solution: Check exact workflow name in Actions tab, copy-paste if needed

**"Failed to delete run [ID]"**
- Solution: Run may be currently executing or have permissions issues

**Rate limiting errors**
- Solution: Workflow includes delays to prevent this, but may occur with very large numbers of runs

### Recovery Options:
- **No recovery possible**: Deleted runs cannot be restored
- **Prevention**: Always use dry run first
- **Mitigation**: Keep recent runs by setting appropriate "Days to Keep" value

## 📈 Storage Impact

### Expected Results:
- **Immediate**: Storage usage reduction visible in GitHub settings
- **Artifacts**: All associated artifacts are also deleted
- **Billing**: Reduced GitHub Actions storage costs (if applicable)

### Monitoring Storage:
1. Go to **Settings** → **Billing and plans**
2. Check **Storage and bandwidth** usage
3. Compare before/after cleanup

## 🔄 Regular Maintenance

### Recommended Schedule:
- **Weekly**: For active repositories with many workflow runs
- **Monthly**: For moderate usage repositories
- **Quarterly**: For low-activity repositories

### Automation Ideas:
- Set calendar reminders for regular cleanup
- Monitor storage usage alerts
- Consider implementing retention policies

This cleanup workflow is a powerful tool for managing repository storage - use it wisely and always with proper precautions!
