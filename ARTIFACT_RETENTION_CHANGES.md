# GitHub Actions Artifact Retention Changes

## Summary
Updated GitHub Actions workflow to reduce artifact retention period from 30/7 days to 1 day to optimize GitHub storage usage.

## Changes Made

### 1. Serenity Reports Artifact
**File**: `.github/workflows/test-run-pipeline.yml`
**Location**: Lines 741-748

**Before:**
```yaml
- name: 📤 Upload Serenity Reports
  if: always()
  uses: actions/upload-artifact@v4
  with:
    name: serenity-reports-${{ github.event.inputs.browser }}-${{ github.event.inputs.environment }}-${{ github.run_number }}
    path: target/
    retention-days: 30  # ← Changed from 30 days
    if-no-files-found: warn
```

**After:**
```yaml
- name: 📤 Upload Serenity Reports
  if: always()
  uses: actions/upload-artifact@v4
  with:
    name: serenity-reports-${{ github.event.inputs.browser }}-${{ github.event.inputs.environment }}-${{ github.run_number }}
    path: target/
    retention-days: 1   # ← Changed to 1 day
    if-no-files-found: warn
```

### 2. Test Failure Logs Artifact
**File**: `.github/workflows/test-run-pipeline.yml`
**Location**: Lines 750-760

**Before:**
```yaml
- name: 📤 Upload Test Logs on Failure
  if: always() && env.TEST_EXIT_CODE != '0'
  uses: actions/upload-artifact@v4
  with:
    name: test-failure-logs-${{ github.run_number }}
    path: |
      *.log
      **/*.log
      target/**/*.log
    retention-days: 7   # ← Changed from 7 days
    if-no-files-found: ignore
```

**After:**
```yaml
- name: 📤 Upload Test Logs on Failure
  if: always() && env.TEST_EXIT_CODE != '0'
  uses: actions/upload-artifact@v4
  with:
    name: test-failure-logs-${{ github.run_number }}
    path: |
      *.log
      **/*.log
      target/**/*.log
    retention-days: 1   # ← Changed to 1 day
    if-no-files-found: ignore
```

### 3. Documentation Update
**File**: `README.md`
**Location**: Lines 318-324

**Added note about artifact retention:**
```markdown
#### **🔗 Accessing Test Reports**:
After workflow completion, test reports are available as:
- **Artifacts**: Download complete target directory (retained for 1 day)
- **GitHub Pages**: Live links to Serenity reports (when available)
- **Direct Links**: Generated links to index.html and summary reports

**📝 Note**: Artifacts are automatically deleted after 1 day to optimize GitHub storage usage.
```

## Impact

### Storage Savings
- **Serenity Reports**: Reduced from 30 days to 1 day (96.7% reduction in retention time)
- **Test Failure Logs**: Reduced from 7 days to 1 day (85.7% reduction in retention time)

### User Impact
- **Immediate access**: Reports are still available immediately after test completion
- **Download window**: Users have 24 hours to download artifacts instead of 7-30 days
- **Storage optimization**: Significantly reduces GitHub Actions storage usage

## Recommendations

1. **Download reports promptly**: Users should download needed reports within 24 hours
2. **Alternative storage**: For long-term report storage, consider:
   - Local storage after download
   - External storage services
   - GitHub Pages for live report hosting (if configured)

## Rollback Instructions

If longer retention is needed, update the `retention-days` values in `.github/workflows/test-run-pipeline.yml`:
- For 7 days: `retention-days: 7`
- For 30 days: `retention-days: 30`
- For maximum (90 days): `retention-days: 90`
