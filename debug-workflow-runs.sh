#!/bin/bash

echo "🔍 Debug: Checking Workflow Runs"
echo "================================="
echo ""

# Check if gh CLI is available
if ! command -v gh &> /dev/null; then
    echo "❌ GitHub CLI (gh) is not installed"
    exit 1
fi

# Check if authenticated
if ! gh auth status &> /dev/null; then
    echo "❌ Not authenticated with GitHub CLI"
    echo "Run: gh auth login"
    exit 1
fi

REPO=$(gh repo view --json nameWithOwner -q .nameWithOwner 2>/dev/null)
if [[ -z "$REPO" ]]; then
    echo "❌ Could not determine repository name"
    echo "Make sure you're in a git repository with GitHub remote"
    exit 1
fi

echo "📁 Repository: $REPO"
echo ""

echo "🔍 Getting all workflows..."
WORKFLOWS=$(gh api repos/$REPO/actions/workflows --jq '.workflows[] | {id: .id, name: .name}')

if [[ -z "$WORKFLOWS" ]]; then
    echo "❌ No workflows found"
    exit 1
fi

echo "$WORKFLOWS" | jq -r '. | "\(.id)|\(.name)"' | while IFS='|' read -r WORKFLOW_ID WORKFLOW_NAME; do
    echo ""
    echo "🔄 Checking workflow: $WORKFLOW_NAME (ID: $WORKFLOW_ID)"
    echo "=================================================="
    
    # Get total count
    TOTAL_INFO=$(gh api repos/$REPO/actions/workflows/$WORKFLOW_ID/runs --field per_page=1)
    TOTAL_COUNT=$(echo "$TOTAL_INFO" | jq -r '.total_count')
    echo "📊 Total runs reported by API: $TOTAL_COUNT"
    
    if [[ $TOTAL_COUNT -eq 0 ]]; then
        echo "⚠️ No runs found for this workflow"
        continue
    fi
    
    # Get first page of runs
    echo "📄 Fetching first page of runs..."
    RUNS_PAGE1=$(gh api repos/$REPO/actions/workflows/$WORKFLOW_ID/runs --field per_page=10)
    RUNS_COUNT_PAGE1=$(echo "$RUNS_PAGE1" | jq -r '.workflow_runs | length')
    echo "📊 Runs on first page: $RUNS_COUNT_PAGE1"
    
    if [[ $RUNS_COUNT_PAGE1 -gt 0 ]]; then
        echo "📋 Sample runs from first page:"
        echo "$RUNS_PAGE1" | jq -r '.workflow_runs[0:5][] | "   - ID: \(.id) | Created: \(.created_at) | Status: \(.status) | Conclusion: \(.conclusion // "null")"'
        
        # Check oldest run on first page
        OLDEST_ON_PAGE=$(echo "$RUNS_PAGE1" | jq -r '.workflow_runs[-1].created_at')
        echo "📅 Oldest run on first page: $OLDEST_ON_PAGE"
    fi
    
    # Try to get older runs with different approaches
    echo ""
    echo "🔍 Trying to get older runs..."
    
    # Method 1: Try with created filter
    echo "📄 Method 1: Using created filter..."
    OLDER_RUNS=$(gh api repos/$REPO/actions/workflows/$WORKFLOW_ID/runs \
        --field per_page=5 \
        --field created=">=2020-01-01" 2>/dev/null || echo "")
    
    if [[ -n "$OLDER_RUNS" ]]; then
        OLDER_COUNT=$(echo "$OLDER_RUNS" | jq -r '.workflow_runs | length')
        echo "📊 Found $OLDER_COUNT runs with created filter"
        if [[ $OLDER_COUNT -gt 0 ]]; then
            echo "📋 Sample older runs:"
            echo "$OLDER_RUNS" | jq -r '.workflow_runs[0:3][] | "   - ID: \(.id) | Created: \(.created_at) | Status: \(.status)"'
        fi
    else
        echo "❌ Created filter method failed"
    fi
    
    # Method 2: Try pagination
    echo "📄 Method 2: Checking pagination..."
    PAGE2_RUNS=$(gh api repos/$REPO/actions/workflows/$WORKFLOW_ID/runs \
        --field per_page=10 \
        --field page=2 2>/dev/null || echo "")
    
    if [[ -n "$PAGE2_RUNS" ]]; then
        PAGE2_COUNT=$(echo "$PAGE2_RUNS" | jq -r '.workflow_runs | length')
        echo "📊 Found $PAGE2_COUNT runs on page 2"
        if [[ $PAGE2_COUNT -gt 0 ]]; then
            echo "📋 Sample runs from page 2:"
            echo "$PAGE2_RUNS" | jq -r '.workflow_runs[0:3][] | "   - ID: \(.id) | Created: \(.created_at) | Status: \(.status)"'
        fi
    else
        echo "❌ Page 2 method failed or no more runs"
    fi
    
    echo ""
    echo "🎯 Summary for $WORKFLOW_NAME:"
    echo "   - Total runs (API): $TOTAL_COUNT"
    echo "   - Runs on page 1: $RUNS_COUNT_PAGE1"
    echo "   - Accessible via pagination: $(if [[ -n "$PAGE2_RUNS" ]] && [[ $(echo "$PAGE2_RUNS" | jq -r '.workflow_runs | length') -gt 0 ]]; then echo "Yes"; else echo "No"; fi)"
    
done

echo ""
echo "🔧 Debugging Complete"
echo "====================="
echo ""
echo "💡 If you see workflows with total runs > 0 but can't access them:"
echo "   - This might be a GitHub API limitation"
echo "   - Try running the cleanup workflow anyway - it might still work"
echo "   - The workflow uses more aggressive pagination and filtering"
echo ""
echo "🚀 Next steps:"
echo "   1. Run the updated cleanup workflow with Dry Run: true"
echo "   2. Check if it can access more runs than this debug script"
echo "   3. If issues persist, we may need to use alternative approaches"
