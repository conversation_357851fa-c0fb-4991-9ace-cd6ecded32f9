# 🗑️ Workflow Cleanup - Quick Reference

## 🎯 What This Cleans
**Deletes workflow runs from ALL workflows in your repository:**
- Current active workflows (Team Test Execution, etc.)
- Old workflows from deleted .yml files still showing in Actions tab
- Any workflow that has ever run in your repository

## 🚀 Quick Start
1. Go to **Actions** → **"🗑️ Cleanup Workflow Runs"**
2. Click **"Run workflow"**
3. Fill in parameters and run

## 📋 Parameter Quick Guide

| Parameter | Required | Example | Description |
|-----------|----------|---------|-------------|
| **Confirm Deletion** | ✅ Yes | `DELETE ALL WORKFLOWS` | Safety confirmation (exact text) |
| **Days to Keep** | ❌ No | `30` | Keep last N days (0 = delete all) |
| **Workflow Name** | ❌ No | `Team Test Execution` | Target specific workflow (empty = all) |
| **Dry Run** | ❌ No | `true` | Preview without deleting |

## 🎯 Common Use Cases

### 🧪 Safe Preview (Recommended First Step)
```
Confirm Deletion: DELETE ALL WORKFLOWS
Days to Keep: 0
Workflow Name: (empty)
Dry Run: ✅ true
```
**Result**: Shows what would be deleted

### 🗑️ Delete Everything
```
Confirm Deletion: DELETE ALL WORKFLOWS
Days to Keep: 0
Workflow Name: (empty)
Dry Run: ❌ false
```
**Result**: Deletes ALL workflow runs

### 📅 Keep Recent (30 days)
```
Confirm Deletion: DELETE ALL WORKFLOWS
Days to Keep: 30
Workflow Name: (empty)
Dry Run: ❌ false
```
**Result**: Keeps last 30 days, deletes older

### 🎯 Target Specific Workflow
```
Confirm Deletion: DELETE ALL WORKFLOWS
Days to Keep: 0
Workflow Name: Team Test Execution
Dry Run: ❌ false
```
**Result**: Deletes only from specified workflow

## ⚠️ Safety Checklist

- [ ] **Always dry run first** (`Dry Run: true`)
- [ ] **Double-check confirmation text** (exact: `DELETE ALL WORKFLOWS`)
- [ ] **Consider retention period** (start with 7-30 days)
- [ ] **Download important reports** before cleanup
- [ ] **Verify workflow name** if targeting specific workflow

## 🔍 What to Expect

### ✅ Will Be Deleted:
- Workflow run records and logs
- All artifacts (reports, files)
- Run history and timing data

### ❌ Will NOT Be Deleted:
- Workflow files (`.yml`)
- Repository code
- Issues/PRs
- Secrets/variables

## 📊 Storage Impact

**Before Cleanup**: Check Settings → Billing → Storage usage
**After Cleanup**: Immediate reduction in storage usage
**Artifacts**: All associated files are also deleted

## 🆘 Emergency Stop

**If running by mistake**: 
- Workflow cannot be stopped once started
- Deletions are immediate and irreversible
- Always use dry run first!

## 📞 Quick Help

**Confirmation error**: Type exactly `DELETE ALL WORKFLOWS`
**No workflows found**: Check exact workflow name in Actions tab
**Rate limiting**: Workflow includes delays, wait for completion
**Failed deletions**: Some runs may be protected or currently running

---

**💡 Pro Tip**: Start conservative with `Days to Keep: 30` and `Dry Run: true` to get familiar with the tool!
